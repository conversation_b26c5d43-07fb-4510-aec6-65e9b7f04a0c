import { validate } from 'class-validator';
import { CreateAppointmentDto } from './create-appointment.dto';
import { UpdateAppointmentsDto } from './update-appointment.dto';
import { UpdateAppointmentFeildsDto } from './update-appointmentField.dto';
import { EnumAppointmentType } from '../../enums/enum-appointment-type';
import { EnumAppointmentStatus } from '../../enums/enum-appointment-status';

describe('DTO Validation', () => {
	// CreateAppointmentDto Tests
	describe('CreateAppointmentDto', () => {
		it('should validate successfully with valid data', async () => {
			const dto = new CreateAppointmentDto();
			dto.clinicId = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db';
			dto.doctorIds = ['f0e31d2d-6ae2-4c2b-9e55-7e1b0454e59f'];
			dto.providerIds = ['30362034-bcee-4697-be5a-c4f2676cb925'];
			dto.patientId = 'a2b4c6d8-9f0a-4d9f-8a1b-3d4e8a9b10cf';
			dto.date = new Date().toISOString();
			dto.startTime = new Date();
			dto.endTime = new Date();
			dto.reason = 'Visit';
			dto.type = EnumAppointmentType.Adhoc;
			dto.status = EnumAppointmentStatus.Scheduled;

			const errors = await validate(dto);
			expect(errors.length).toBe(0);
		});
		it('should fail if enums (status and type fields) have wrong values', async () => {
			const dto = new CreateAppointmentDto();
			dto.clinicId = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db';
			dto.doctorIds = ['f0e31d2d-6ae2-4c2b-9e55-7e1b0454e59f'];
			dto.patientId = 'a2b4c6d8-9f0a-4d9f-8a1b-3d4e8a9b10cf';
			dto.providerIds = ['30362034-bcee-4697-be5a-c4f2676cb925'];
			dto.date = new Date().toISOString();
			dto.startTime = new Date('2024-08-01T09:00:00Z');
			dto.endTime = new Date('2024-08-01T10:00:00Z');
			dto.type = 'wrong_type' as any;
			dto.status = 'wrong_status' as any;

			const errors = await validate(dto);
			expect(errors.length).toBeGreaterThan(0);
		});

		it('should fail if required fields are missing', async () => {
			const dto = new CreateAppointmentDto();

			const errors = await validate(dto);
			expect(errors.length).toBeGreaterThan(0);
		});
	});

	// UpdateAppointmentsDto Tests
	describe('UpdateAppointmentsDto', () => {
		it('should validate successfully with valid data', async () => {
			const dto = new UpdateAppointmentsDto();
			dto.status = EnumAppointmentStatus.Completed;
			dto.soapPending = true;

			const errors = await validate(dto);
			expect(errors.length).toBe(0);
		});

		it('should fail if status is an invalid enum value', async () => {
			const dto = new UpdateAppointmentsDto();
			dto.status = 'invalid_status' as any;

			const errors = await validate(dto);
			expect(errors.length).toBeGreaterThan(0);
		});

		it('should pass when soapPending is omitted', async () => {
			const dto = new UpdateAppointmentsDto();
			dto.status = EnumAppointmentStatus.Cancelled;

			const errors = await validate(dto);
			expect(errors.length).toBe(0);
		});
	});

	// UpdateAppointmentFieldsDto Tests
	describe('UpdateAppointmentFieldsDto', () => {
		it('should validate successfully with valid data', async () => {
			const dto = new UpdateAppointmentFeildsDto();
			dto.doctorIds = ['342f7f24-6e07-4dfa-9831-9940d0a37421'];
			dto.patientId = '0984bc6d-b48f-4e87-adcb-86f83f972d4f';
			dto.providerIds = ['30362034-bcee-4697-be5a-c4f2676cb925'];
			dto.status = EnumAppointmentStatus.Scheduled;

			const errors = await validate(dto);
			expect(errors.length).toBe(0);
		});

		it('should fail if doctorIds array is empty', async () => {
			const dto = new UpdateAppointmentFeildsDto();
			dto.doctorIds = []; // Empty doctorIds array
			dto.patientId = 'a2b4c6d8-9f0a-4d9f-8a1b-3d4e8a9b10cf';

			const errors = await validate(dto);
			expect(errors.length).toBeGreaterThan(0);
		});

		it('should fail if required patientId is missing', async () => {
			const dto = new UpdateAppointmentFeildsDto(); // Missing patientId
			dto.doctorIds = ['f0e31d2d-6ae2-4c2b-9e55-7e1b0454e59f'];

			const errors = await validate(dto);
			expect(errors.length).toBeGreaterThan(0);
		});

		it('should fail if roomId is an invalid UUID', async () => {
			const dto = new UpdateAppointmentFeildsDto();
			dto.doctorIds = ['f0e31d2d-6ae2-4c2b-9e55-7e1b0454e59f'];
			dto.patientId = 'a2b4c6d8-9f0a-4d9f-8a1b-3d4e8a9b10cf';
			dto.roomId = 'invalid-uuid';

			const errors = await validate(dto);
			expect(errors.length).toBeGreaterThan(0);
		});
	});
});
