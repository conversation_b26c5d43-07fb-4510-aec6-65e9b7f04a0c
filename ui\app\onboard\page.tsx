'use client';

import { StepItemType } from '@/app/atoms/VerticalTabs';
import { RegClinicFormData } from '@/app/molecules/clinics/RegistrationDetails';
import { WorkHoursFormData } from '@/app/molecules/clinics/WorkHours';
import { getAuth, setAuth } from '@/app/services/identity.service';
import {
    useGetClinicUserData,
    useUpdateWorkingHours,
    useUpdateStaff,
    useGetUserClinics,
} from '@/app/services/user.queries';
import ClinicRegistration from '@/app/template/ClinicRegistration';
import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { parsePhoneNumber } from 'libphonenumber-js';
import { useRouter } from 'next/navigation';
import { Inter } from 'next/font/google';

const StaffDetails = () => {
    const router = useRouter();
    const auth = getAuth();
    const { userId, clinicId, username, clinicName, globalUserId, role } =
        auth || {};
    const [step, setStep] = useState<string | null>(null);
    const [selectedClinic, setSelectedClinic] = useState(clinicId);
    const [clinicUserId, setClinicUserId] = useState('');
    const [selectedClinicName, setSelectedClinicName] = useState(clinicName);
    const [onboarded, setOnboarded] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const { data: userClinic, isLoading: isUserClinicLoading } =
        useGetUserClinics(globalUserId);
    const {
        data: clinicUserData,
        isLoading: isClinicUserDataLoading,
        error,
        refetch,
    } = useGetClinicUserData(clinicUserId);
    const updateClinicUserMutation = useUpdateStaff();
    const updateWorkingHoursMutation = useUpdateWorkingHours();

    const isMultiClinic = userClinic?.data.length > 1;
    const isFirstLogin = auth?.isFirstLogin;

    const initializeStep = useCallback(() => {
        if (isFirstLogin) {
            setStep('registration-step');
        } else if (isMultiClinic) {
            setStep('select-clinic');
        } else if (userClinic?.data[0]) {
            if (userClinic.data[0].isOnboarded) {
                router.push('/dashboard');
            } else {
                setClinicUserId(userClinic.data[0].clinic_user_id);
                setStep('work-hours-step');
            }
        }
        setIsLoading(false);
    }, [isFirstLogin, isMultiClinic, userClinic, router]);

    useEffect(() => {
        if (!isUserClinicLoading) {
            initializeStep();
        }
    }, [isUserClinicLoading, initializeStep]);

    useEffect(() => {
        if (clinicUserId) {
            if (onboarded) {
                router.push('/dashboard');
            } else {
                setStep('work-hours-step');
            }
            setAuth({
                ...auth,
                clinicName: selectedClinicName,
                userId: clinicUserId,
                clinicId: selectedClinic,
            });
        }
    }, [clinicUserId, selectedClinic, onboarded, auth, router]);

    const {
        control,
        handleSubmit,
        getValues,
        setValue,
        watch,
        register,
        formState: { errors },
    } = useForm<RegClinicFormData>({
        defaultValues: {
            phoneNumber: '',
            licenseNumber: '',
            digitalSignature: '',
        },
    });

    const {
        control: controlWh,
        handleSubmit: handleSubmitWh,
        getValues: getValuesWh,
    } = useForm<WorkHoursFormData>();

    const onSubmit = async (data: RegClinicFormData) => {
        try {
            // Parse phone number
            const phoneNumber = parsePhoneNumber(data.phoneNumber);
            
            // Prepare data for clinic user update, including digital signature
            const updatedData = {
                countryCode: phoneNumber.countryCallingCode,
                mobileNumber: phoneNumber.nationalNumber,
                licenseNumber:
                    data.licenseNumber?.toString().slice(0, 25) || '',
                // Include digital signature in the same API call if it exists
                ...(data.digitalSignature ? { digitalSignature: data.digitalSignature } : {})
            };

            // Send all data in a single API call
            await updateClinicUserMutation.mutateAsync({
                globalUserId,
                userData: updatedData,
            });

            if (userId) {
                setClinicUserId(userId);
            }
            if (isMultiClinic) {
                setStep('select-clinic');
            } else {
                setStep('work-hours-step');
            }
        } catch (error: any) {
            // Handle errors silently in production
        }
    };

    const onAdminSubmit = async (data: RegClinicFormData) => {
        try {
            // Include all data including digital signature
            const updatedData = {
                licenseNumber:
                    data.licenseNumber?.toString().slice(0, 25) || '',
                // Include digital signature in the same API call if it exists
                ...(data.digitalSignature ? { digitalSignature: data.digitalSignature } : {})
            };

            // Send all data in a single API call
            await updateClinicUserMutation.mutateAsync({
                globalUserId,
                userData: updatedData,
            });
            
            if (userId) {
                setClinicUserId(userId);
            }
            if (isMultiClinic) {
                setStep('select-clinic');
            } else {
                setStep('work-hours-step');
            }
        } catch (error: any) {
            // Handle errors silently in production
        }
    };

    const handleWorkingHoursUpdate = async (updatedHours: any) => {
        const formattedHours = Object.keys(updatedHours).reduce<Record<string, any>>((acc, day) => {
            acc[day.toLowerCase()] = updatedHours[day];
            return acc;
        }, {} as Record<string, any>);

        try {
            await updateWorkingHoursMutation.mutateAsync({
                userId,
                workingHours: { workingHours: formattedHours },
            });
            router.push('/dashboard');
        } catch (error: any) {
            // Handle errors silently in production
        }
    };

    const handleClinicSelect = useCallback(
        (
            clinicId: string,
            clinicUserId: string,
            clinicName: string,
            isOnboarded: boolean
        ) => {
            setClinicUserId(clinicUserId);
            setSelectedClinic(clinicId);
            setSelectedClinicName(clinicName);
            if (role === 'admin') {
                setOnboarded(true);
            } else {
                setOnboarded(isOnboarded);
            }
        },
        []
    );

    const onNextStep = (data: StepItemType) => {
        if (data.id === 'registration-step') {
            if (role === 'admin') handleSubmit(onAdminSubmit)();
            handleSubmit(onSubmit)();
        } else if (data.id === 'work-hours-step') {
            const workingHoursData = getValuesWh();
            handleWorkingHoursUpdate(workingHoursData);
        }
    };

    const formattedWorkingHours = clinicUserData?.data?.workingHours
        ?.workingHours
        ? Object.entries(clinicUserData.data.workingHours.workingHours).reduce<Record<string, any[]>>(
              (acc, [day, hours]) => {
                  const capitalizedDay =
                      day.charAt(0).toUpperCase() + day.slice(1);
                  if (Array.isArray(hours)) {
                      acc[capitalizedDay] = hours.map((slot) => ({
                          startTime: slot.startTime,
                          endTime: slot.endTime,
                          isWorkingDay: slot.isWorkingDay,
                      }));
                  }
                  return acc;
              },
              {
                  Monday: [],
                  Tuesday: [],
                  Wednesday: [],
                  Thursday: [],
                  Friday: [],
                  Saturday: [],
                  Sunday: [],
              } as Record<string, any[]>
          )
        : {
              Monday: [],
              Tuesday: [],
              Wednesday: [],
              Thursday: [],
              Friday: [],
              Saturday: [],
              Sunday: [],
          };

    if (isLoading || isUserClinicLoading || isClinicUserDataLoading) {
        return <div>Loading...</div>;
    }

    if (error) {
        return <div>Error loading user data: {error.message}</div>;
    }

    if (step === null) {
        return null;
    }

    return (
        <div>
            <ClinicRegistration
                clinicFormProps={{
                    getValues: getValues as any,
                    setValue: setValue as any,
                    control,
                    errors: errors as any,
                    watch,
                    register: register as any,
                }}
                workingHours={{
                    daysOfWeek: [
                        'Monday',
                        'Tuesday',
                        'Wednesday',
                        'Thursday',
                        'Friday',
                        'Saturday',
                        'Sunday',
                    ],
                    workingHours: formattedWorkingHours,
                }}
                step={step as string}
                setStep={setStep as any}
                onNextStep={onNextStep}
                username={username}
                clinicName={clinicName}
                onUpdateWorkingHours={handleWorkingHoursUpdate}
                clinicList={userClinic?.data}
                onClinicSelect={handleClinicSelect as any}
                isAdmin={role === 'admin'}
                userRole={role}
            />
        </div>
    );
};

export default StaffDetails;
