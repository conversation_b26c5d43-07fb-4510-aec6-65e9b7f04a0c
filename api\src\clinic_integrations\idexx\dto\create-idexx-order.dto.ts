import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsArray, ArrayNotEmpty } from 'class-validator';

export class CreateIdexxOrderDto {
	@ApiProperty({
		description: 'The patient id',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	patientId!: string;

	@ApiProperty({
		description: 'The code of the test list item from IDEXX',
		example: 'IHC_test'
	})
	@IsNotEmpty()
	integrationCode?: string; // IDEXX test code

	@ApiProperty({
		description: 'The appointmen id',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	appointmentId!: string;

	@ApiProperty({
		description: 'The clinic lab report id',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	clinicLabReportId!: string;

	@ApiProperty({
		description: 'The clinic id',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	clinicId!: string;

	@ApiProperty({
		description: 'The line item id',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	lineItemId!: string;
}

export class CheckIdexxOrdersDeletionDto {
	@ApiProperty({
		description:
			'Array of lab report IDs to check for deletion eligibility',
		example: ['uuid1', 'uuid2', 'uuid3'],
		type: [String]
	})
	@IsArray()
	@ArrayNotEmpty()
	@IsUUID('4', { each: true })
	labReportIds!: string[];
}
