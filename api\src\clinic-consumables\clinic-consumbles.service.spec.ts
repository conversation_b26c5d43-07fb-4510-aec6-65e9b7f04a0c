import { Test, TestingModule } from '@nestjs/testing';
import { ClinicConsumblesService } from './clinic-consumbles.service';
import { ILike, Repository } from 'typeorm';
import { ClinicConsumableEntity } from './entities/clinic-consumable.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateConsumableDto } from './dto/create-consumable.dto';
import { NotFoundException } from '@nestjs/common';
import { UpdateConsumableDto } from './dto/update-consumable.dto';
import { create } from 'domain';

describe('ClinicConsumblesService', () => {
	let service: ClinicConsumblesService;
	let repository: jest.Mocked<Repository<ClinicConsumableEntity>>;
	let logger: jest.Mocked<WinstonLogger>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicConsumblesService,
				{
					provide: getRepositoryToken(ClinicConsumableEntity),
					useValue: {
						find: jest.fn(),
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						create: jest.fn(),
						count: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicConsumblesService>(ClinicConsumblesService);
		repository = module.get(getRepositoryToken(ClinicConsumableEntity));
		logger = module.get(WinstonLogger);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('Get list of consumables', () => {
		const consumablesDataList: ClinicConsumableEntity[] = [
			{
				id: 'consumable_uuid_1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				productName: 'product_name_A',
				currentStock: 12,
				minimumQuantity: 2
			},
			{
				id: 'consumable_uuid_2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_2',
				productName: 'product_name_B',
				currentStock: 12,
				minimumQuantity: 2
			}
		];

		it('should return an list of consumables without a search keyword and clinic id', async () => {
			repository.find.mockResolvedValue(consumablesDataList);

			const result = await service.getConsumables('clinicId');

			expect(result).toEqual(consumablesDataList);
			expect(repository.find).toHaveBeenCalled();
		});

		it('should return an list of consumables for a clinic id with a search keyword', async () => {
			const searchKeyword = 'Test';
			const clinicId = 'clinic_id';

			const filteredData = consumablesDataList.filter(consumable =>
				consumable.productName.includes(searchKeyword)
			);

			repository.find.mockResolvedValue(filteredData);

			const result = await service.getConsumables(
				clinicId,
				searchKeyword
			);

			expect(result).toEqual(filteredData);

			expect(repository.find).toHaveBeenCalledWith({
				where: { productName: ILike(`%${searchKeyword}%`), clinicId }
			});
		});

		it('should return an list of consumables for a clinic id', async () => {
			const searchKeyword = 'Test';
			const clinicId = 'clinic_id';

			const filteredData = consumablesDataList.filter(consumable =>
				consumable.productName.includes(searchKeyword)
			);

			repository.find.mockResolvedValue(filteredData);

			const result = await service.getConsumables(clinicId, undefined);

			expect(result).toEqual(filteredData);

			expect(repository.find).toHaveBeenCalledWith({
				where: { clinicId }
			});
		});

		describe('bulkInsert', () => {
			it('should successfully insert multiple consumables', async () => {
				const consumables: CreateConsumableDto[] = [
					{
						clinicId: 'clinic_uuid_1',
						brandId: 'brand_uuid_1',
						productName: 'product_name_A',
						currentStock: 12,
						minimumQuantity: 2
					},
					{
						clinicId: 'clinic_uuid_1',
						brandId: 'brand_uuid_1',
						productName: 'product_name_B',
						currentStock: 10,
						minimumQuantity: 3
					}
				];

				// Mock count for generateUniqueId
				repository.count.mockResolvedValue(0);
				repository.findOne.mockResolvedValue(null);
				repository.save.mockImplementation((entity: any) =>
					Promise.resolve({
						id: 'generated_id',
						...entity
					} as ClinicConsumableEntity)
				);

				const result = await service.bulkInsert(consumables);

				expect(result).toBe(
					'Bulk insert of 2 consumables completed successfully'
				);
				expect(repository.save).toHaveBeenCalled();
				expect(logger.log).toHaveBeenCalledWith(
					'Bulk insert completed. Inserted 2 consumables.'
				);
			});

			it('should throw an error when bulk insert fails', async () => {
				const consumables: CreateConsumableDto[] = [
					{
						clinicId: 'clinic_uuid_1',
						brandId: 'brand_uuid_1',
						productName: 'Invalid Product',
						currentStock: 10,
						minimumQuantity: 3
					}
				];

				// Mock count for generateUniqueId
				repository.count.mockResolvedValue(0);
				repository.findOne.mockResolvedValue(null);
				repository.save.mockRejectedValue(new Error('Database error'));

				await expect(service.bulkInsert(consumables)).rejects.toThrow(
					'Failed to insert consumables'
				);
				expect(logger.error).toHaveBeenCalledWith(
					'Error during bulk insert of consumables',
					{ error: expect.anything() }
				);
			});
		});

		describe('findOneEntry', () => {
			it('should find a consumable by productName and clinicId', async () => {
				const criteria = {
					productName: 'product_name_A',
					clinicId: 'clinic1'
				};
				const expectedConsumable = {
					id: 'consumable_uuid_1',
					clinicId: 'clinic1',
					brandId: 'brand_uuid_1',
					uniqueId: 'unique_id_1',
					productName: 'product_name_A',
					currentStock: 12,
					minimumQuantity: 2
				};
				repository.findOne.mockResolvedValue(expectedConsumable);

				const result = await service.findOneEntry(criteria);

				expect(result).toEqual(expectedConsumable);
				expect(repository.findOne).toHaveBeenCalledWith({
					where: criteria
				});
			});

			it('should return null if no consumable is found', async () => {
				const criteria = {
					productName: 'nonexistent_product',
					clinicId: 'clinic1'
				};
				repository.findOne.mockResolvedValue(null);

				const result = await service.findOneEntry(criteria);

				expect(result).toBeNull();
				expect(repository.findOne).toHaveBeenCalledWith({
					where: criteria
				});
			});
		});
	});

	describe('create', () => {
		it('should create and save a new consumable', async () => {
			const createDto: CreateConsumableDto = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};

			// Mock count for generateUniqueId
			repository.count.mockResolvedValue(0);

			const createdEntity = {
				...createDto,
				id: 'new_uuid',
				uniqueId: 'C_000001'
			};
			repository.create.mockReturnValue(createdEntity as any);
			repository.save.mockResolvedValue(createdEntity as any);

			const result = await service.create(createDto);

			expect(repository.create).toHaveBeenCalledWith({
				...createDto,
				uniqueId: 'C_000001'
			});
			expect(repository.save).toHaveBeenCalledWith(createdEntity);
			expect(result).toEqual(createdEntity);
		});

		it('should log and throw an error if creation fails', async () => {
			const createDto: CreateConsumableDto = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};

			// Mock count for generateUniqueId
			repository.count.mockResolvedValue(0);

			const error = new Error('Creation failed');
			repository.save.mockRejectedValue(error);

			await expect(service.create(createDto)).rejects.toThrow(error);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating consumable',
				{ error }
			);
		});
	});

	describe('update', () => {
		it('should update and save an existing consumable', async () => {
			const updateDto: UpdateConsumableDto = {
				productName: 'Updated Product',
				currentStock: 150,
				minimumQuantity: 15
			};
			const existingEntity = {
				id: 'existing_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'unique_id',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};
			const updatedEntity = { ...existingEntity, ...updateDto };

			repository.findOne.mockResolvedValue(existingEntity as any);
			repository.save.mockResolvedValue(updatedEntity as any);

			const result = await service.update('existing_uuid', updateDto);

			expect(repository.findOne).toHaveBeenCalledWith({
				where: { id: 'existing_uuid' }
			});
			expect(repository.save).toHaveBeenCalledWith(updatedEntity);
			expect(result).toEqual(updatedEntity);
		});

		it('should throw NotFoundException if consumable not found', async () => {
			repository.findOne.mockResolvedValue(null);

			await expect(
				service.update('nonexistent_uuid', {})
			).rejects.toThrow(NotFoundException);
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating consumable',
				{ error: expect.anything() }
			);
		});
	});

	describe('findOne', () => {
		it('should return a consumable if found', async () => {
			const consumable = {
				id: 'consumable_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'unique_id',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};
			repository.findOne.mockResolvedValue(consumable as any);

			const result = await service.findOne('consumable_uuid');

			expect(repository.findOne).toHaveBeenCalledWith({
				where: { id: 'consumable_uuid' }
			});
			expect(result).toEqual(consumable);
		});

		it('should throw NotFoundException if consumable not found', async () => {
			repository.findOne.mockResolvedValue(null);

			await expect(service.findOne('nonexistent_uuid')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('remove', () => {
		it('should delete the consumable if it exists', async () => {
			repository.delete.mockResolvedValue({ affected: 1 } as any);

			await service.remove('consumable_uuid');

			expect(repository.delete).toHaveBeenCalledWith('consumable_uuid');
		});

		it('should throw NotFoundException if consumable does not exist', async () => {
			repository.delete.mockResolvedValue({ affected: 0 } as any);

			await expect(service.remove('nonexistent_uuid')).rejects.toThrow(
				NotFoundException
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error deleting consumable',
				{ error: expect.anything() }
			);
		});
	});

	describe('deleteItem', () => {
		it('should delete the consumable item', async () => {
			repository.delete.mockResolvedValue({ affected: 1 } as any);

			await service.deleteItem('item_uuid');

			expect(repository.delete).toHaveBeenCalledWith('item_uuid');
		});

		it('should handle deletion failure gracefully', async () => {
			repository.delete.mockResolvedValue({ affected: 0 } as any);

			const result = await service.deleteItem('nonexistent_item_uuid');

			expect(result).toEqual({ affected: 0 });
		});
	});
});
