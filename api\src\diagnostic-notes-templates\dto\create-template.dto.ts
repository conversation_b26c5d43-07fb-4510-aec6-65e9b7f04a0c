import { IsString, <PERSON><PERSON><PERSON>, IsU<PERSON>D, IsArray, IsOptional, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { TemplateType } from '../entities/diagnostic-template.entity';

class AssignedDiagnosticDto {
    @IsUUID()
    id!: string;

    @IsString()
    name!: string;
}

class TableColumnDto {
    @IsString()
    name!: string;

    @IsEnum(['text', 'number', 'range'])
    type!: 'text' | 'number' | 'range';

    @IsArray()
    @IsOptional()
    options?: string[];
}

class TableStructureDto {
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TableColumnDto)
    columns!: TableColumnDto[];
}
export class CreateDiagnosticTemplateDto {
    @IsString()
    templateName!: string;

    @IsUUID()
    clinicId!: string;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => AssignedDiagnosticDto)
    assignedDiagnostics!: AssignedDiagnosticDto[];

    @IsEnum(TemplateType)
    templateType!: TemplateType;

    @IsOptional()
    @ValidateNested()
    @Type(() => TableStructureDto)
    tableStructure?: TableStructureDto;

    @IsString()
    @IsOptional()
    notes?: string;

    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}
