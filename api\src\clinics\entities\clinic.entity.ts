import {
	<PERSON><PERSON><PERSON>,
	PrimaryGeneratedColumn,
	<PERSON>umn,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn,
	OneToMany,
	OneToOne
} from 'typeorm';
import { Brand } from '../../brands/entities/brand.entity';
import { User } from '../../users/entities/user.entity';
import { ClinicUser } from './clinic-user.entity';
import { ClinicAlerts } from '../../clinic-alerts/entities/clinicAlerts.entity';
import { CreateClinicIdexxEntity } from '../../clinic_integrations/idexx/entities/create-clinic-idexx.entity';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { DocumentLibrary } from '../../document-library/entities/document-library.entity';
import { Emr } from '../../emr/entities/emr.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';

// Define time duration interface for more granular time settings
export interface TimeDuration {
	days?: number;
	hours?: number;
	minutes?: number;
}

// Define structure for client booking working hours
export interface ClientBookingDaySchedule {
	startTime: string | null; // HH:MM format or null
	endTime: string | null; // HH:MM format or null
	isWorkingDay: boolean;
}

export interface ClientBookingWorkingHours {
	monday?: ClientBookingDaySchedule[];
	tuesday?: ClientBookingDaySchedule[];
	wednesday?: ClientBookingDaySchedule[];
	thursday?: ClientBookingDaySchedule[];
	friday?: ClientBookingDaySchedule[];
	saturday?: ClientBookingDaySchedule[];
	sunday?: ClientBookingDaySchedule[];
}

// Define structure for overall client booking settings
export interface ClientBookingSettings {
	isEnabled: boolean;
	allowAllDoctors?: boolean;
	workingHours?: ClientBookingWorkingHours | null;
	allowedDoctorIds?: string[] | null; // Array of ClinicUser UUIDs

	// New time-specific fields with granular components
	minBookingLeadTime?: TimeDuration | null; // Min time before appointment to book
	modificationDeadlineTime?: TimeDuration | null; // Time before appointment to modify
	maxAdvanceBookingTime?: TimeDuration | null; // Max time in advance to book

	// Legacy fields for backward compatibility
	minBookingLeadHours?: number | null; // Min hours notice
	modificationDeadlineHours?: number | null; // Hours before appointment lock
}

// Update ClinicCustomRule to include the new settings structure
export interface ClinicCustomRule {
	patientLastNameAsOwnerLastName: boolean;
	defaultPatientList?: 'all' | 'alive';
	appointmentBookingList?: 'all' | 'alive';
	clientBookingSettings?: ClientBookingSettings | null; // Added client booking settings
}

@Entity('clinics')
export class ClinicEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ unique: true, nullable: false, length: 50 })
	name!: string;

	@Column({ name: 'address_line_1', nullable: true })
	addressLine1!: string;

	@Column({ name: 'address_line_2', nullable: true })
	addressLine2!: string;

	@Column({ nullable: true })
	city!: string;

	@Column({ name: 'address_pincode', nullable: true })
	addressPincode!: string;

	@Column({ nullable: true })
	state!: string;

	@Column({ nullable: true })
	country!: string;

	@Column({
		type: 'varchar',
		nullable: false,
		default: 'Asia/Kolkata', // Default to IST
		comment: "IANA timezone name (e.g., 'Asia/Kolkata', 'America/New_York')"
	})
	timezone!: string;

	@Column({ nullable: true })
	email!: string;

	@Column({ nullable: true })
	website!: string;

	@Column({ nullable: true })
	mobile!: string;

	@Column({ name: 'logo_url', nullable: true })
	logoUrl!: string;

	@Column({ name: 'drug_license_number', nullable: true })
	drugLicenseNumber!: string;

	@Column('jsonb', { nullable: true, name: 'phone_numbers' })
	phoneNumbers!: Array<{ country_code: string; number: string }>;

	@Column({ nullable: true, name: 'clinic_logo' })
	clinicLogo!: string;

	@Column('jsonb', {
		nullable: true,
		name: 'custom_rule',
		default: {
			patientLastNameAsOwnerLastName: false,
			defaultPatientList: 'all',
			appointmentBookingList: 'alive',
			clientBookingSettings: {
				isEnabled: false,
				allowAllDoctors: false,
				workingHours: null,
				allowedDoctorIds: null,
				minBookingLeadTime: null,
				modificationDeadlineTime: null,
				maxAdvanceBookingTime: null,
				minBookingLeadHours: null,
				modificationDeadlineHours: null
			}
		}
	})
	customRule!: ClinicCustomRule;

	@Column({ name: 'brand_id', nullable: true })
	brandId!: string;

	@ManyToOne(() => Brand)
	@JoinColumn({ name: 'brand_id' })
	brand!: Brand;

	@Column({ name: 'is_onboarded', default: false })
	isOnboarded!: boolean;

	@Column('jsonb', { nullable: true })
	working_hours!: any;

	@Column({ name: 'is_active', default: true })
	isActive!: boolean;

	@Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
	deletedAt?: Date;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column({ nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column({ nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@OneToMany(() => ClinicUser, clinicUser => clinicUser.clinic)
	clinicUsers!: ClinicUser[];

	@Column({ name: 'admin_first_name', nullable: true, length: 50 })
	adminFirstName!: string;

	@Column({ name: 'admin_last_name', nullable: true, length: 50 })
	adminLastName!: string;

	@Column({ name: 'admin_email', nullable: true })
	adminEmail!: string;

	@Column({ name: 'admin_mobile', nullable: true })
	adminMobile!: string;

	@OneToMany(() => ClinicAlerts, clinicAlerts => clinicAlerts.clinicId)
	clinicAlerts?: ClinicAlerts[];

	@OneToMany(() => LabReport, labReport => labReport.clinicId)
	labReports?: LabReport[];

	@OneToMany(
		() => CreateClinicIdexxEntity,
		clinicIdexxEntity => clinicIdexxEntity.clinic
	)
	idexx?: CreateClinicIdexxEntity[];

	@OneToMany(() => DocumentLibrary, documentLibrary => documentLibrary.clinic)
	documentLibrary!: DocumentLibrary[];

	@OneToMany(() => Emr, emr => emr.clinic)
	emrs?: Emr[];

	@OneToOne(() => PaymentDetailsEntity, paymentDetail => paymentDetail.clinic)
	paymentDetail?: PaymentDetailsEntity;
}
