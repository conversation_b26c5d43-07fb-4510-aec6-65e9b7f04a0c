import { validate } from 'class-validator';
import 'reflect-metadata';
import { CreateLabReportDto, FileInfoDto } from './lab-report.dto';

describe('lab report file', () => {
    
    it('CreateLabReportDto -> should create lab report', async () => {
        let dto = new CreateLabReportDto();
        dto.appointmentId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.clinicId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.clinicLabReportId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.files = [] as FileInfoDto[],
        dto.patientId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.status = 'COMPLETED';

        const errors = await validate(dto);

        expect(errors.length).toEqual(1);
    });

    it('CreateLabReportDto -> should not create lab report if appointmentId or clinicId is not appropriate', async () => {
        let dto = new CreateLabReportDto();
        dto.appointmentId = 'invalid uuid';
        dto.clinicId = 'c_1';
        dto.clinicLabReportId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.files = [] as FileInfoDto[],
        dto.patientId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.status = 'COMPLETED';

        const errors = await validate(dto);

        expect(errors.length).toEqual(3);
    });

    it('FileInfoDto -> should create file info', async () => {
        let dto = new FileInfoDto();
        dto.fileKey = 'file.key';
        dto.fileName = 'Blood Test';
        dto.fileSize = 9890;
        dto.s3Url = 'url.com';
        dto.uploadDate = '2023-01-22'; 

        const errors = await validate(dto);

        expect(errors.length).toBe(0);
    });

    it('FileInfoDto -> failing scenario, fileKey', async () => {
        let dto = new FileInfoDto();
        dto.fileName = 'Blood Test';
        dto.fileSize = 9890;
        dto.s3Url = 'url.com';
        dto.uploadDate = '2023-01-22'; 
        const errors = await validate(dto);

        expect(errors.length).toBe(1);
    });
});
