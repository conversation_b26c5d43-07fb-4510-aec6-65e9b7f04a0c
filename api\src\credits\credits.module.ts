import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreditsController } from './credits.controller';
import { CreditsService } from './credits.service';
import { CreditTransactionEntity } from './entities/credit-transaction.entity';
import { RoleModule } from '../roles/role.module';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { User } from '../users/entities/user.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			CreditTransactionEntity,
			OwnerBrand,
			InvoiceEntity,
			PaymentDetailsEntity,
			User
		]),
		RoleModule
	],
	controllers: [CreditsController],
	providers: [CreditsService, WinstonLogger],
	exports: [CreditsService]
})
export class CreditsModule {}
