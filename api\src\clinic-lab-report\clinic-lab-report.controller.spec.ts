import { Test, TestingModule } from '@nestjs/testing';
import { ClinicLabReportController } from './clinic-lab-report.controller';
import { ClinicLabReportService } from './clinic-lab-report.service';
import { CreateLabReportDto } from './dto/lab-report.dto';
import { UpdateStatusDto } from './dto/update-clinic-lab-report-status.dto';
import { ClinicLabReport } from './entities/clinic-lab-report.entity';
import { LabReport } from './entities/lab-report.entity';
import { User } from '../users/entities/user.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import * as moment from 'moment';
import { RoleService } from '../roles/role.service';
import { Patient } from '../patients/entities/patient.entity';
import { BadRequestException } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';

describe('ClinicLabReportController', () => {
	let controller: ClinicLabReportController;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let service: ClinicLabReportService;

	const mockClinicLabReportService = {
		getLabReports: jest.fn(),
		createOrUpdateLabReport: jest.fn(),
		deleteFile: jest.fn(),
		deleteLabReport: jest.fn(),
		getLabReportsForClinic: jest.fn(),
		updateLabReportStatus: jest.fn(),
		deleteLabReportsByAppointmentId: jest.fn()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicLabReportController],
			providers: [
				{
					provide: ClinicLabReportService,
					useValue: mockClinicLabReportService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				},
			]
		}).compile();

		controller = module.get<ClinicLabReportController>(
			ClinicLabReportController
		);
		service = module.get<ClinicLabReportService>(ClinicLabReportService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('getClinicLabReports', () => {
		it('should return clinic lab reports', async () => {
			const clinicLabReports: ClinicLabReport[] = [
				{
					id: 'report-1',
					clinicId: '',
					name: '',
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: '',
					createdByUser: new User(),
					updatedBy: '',
					updatedByUser: new User(),
					uniqueId: '',
					chargeablePrice: 0,
					tax: 0,
					associatedLab: 0,
					description: '',
					brandId: ''
				}
			];
			mockClinicLabReportService.getLabReports.mockResolvedValue(
				clinicLabReports
			);

			const result = await controller.getClinicLabReports(
				'clinicId',
				undefined,
				''
			);
			expect(result).toEqual(clinicLabReports);
			expect(
				mockClinicLabReportService.getLabReports
			).toHaveBeenCalledWith('clinicId', undefined, '');
		});

		it('should return clinic lab reports with search term', async () => {
			const clinicLabReports: ClinicLabReport[] = [
				{
					id: 'report-2',
					clinicId: '',
					name: '',
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: '',
					createdByUser: new User(),
					updatedBy: '',
					updatedByUser: new User(),
					uniqueId: '',
					chargeablePrice: 0,
					tax: 0,
					associatedLab: 0,
					description: '',
					brandId: ''
				}
			];
			mockClinicLabReportService.getLabReports.mockResolvedValue(
				clinicLabReports
			);

			const result = await controller.getClinicLabReports(
				'clinicId',
				'Blood Test',
				''
			);
			expect(result).toEqual(clinicLabReports);
			expect(
				mockClinicLabReportService.getLabReports
			).toHaveBeenCalledWith('clinicId', 'Blood Test', '');
		});
	});

	describe('createLabReport', () => {
		it('should create a new lab report', async () => {
			const createLabReportDto: CreateLabReportDto = {
				appointmentId: 'appointment-id',
				clinicLabReportId: 'clinic-lab-report-id',
				patientId: 'p_1',
				clinicId: 'clinic-id',
				status: 'PENDING',
				files: [
					{ fileName: 'test.pdf', fileSize: 1024, fileKey: 's3Key' }
				],
				integrationDetails: {
					id: 'some id'
				},
				integrationOrderId: 'some id'
			};

			const labReport: LabReport = {
				id: 'report-1',
				appointmentId: '',
				appointment: new AppointmentEntity(),
				clinicLabReportId: '',
				clinicLabReport: new ClinicLabReport(),
				clinicId: '',
				clinic: new ClinicEntity(),
				files: [],
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				status: 'PENDING',
				patient: new Patient(),
				patientId: 'p_1',
				brandId: 'b_1',
				diagnosticNotes: [{id: 'u_1', type: 'upload', data: {notes: "okay", title:" title 1"}}]
			};
			mockClinicLabReportService.createOrUpdateLabReport.mockResolvedValue(
				labReport
			);
			const req = {
				user: {
					clinicId: "some-clinic-id",
					brandId: "some-brand-id",
				},
			};
			const result = await controller.createLabReport(createLabReportDto, req);
			expect(result).toEqual(labReport);
			expect(
				mockClinicLabReportService.createOrUpdateLabReport
			).toHaveBeenCalled();
		});
	});

	describe('deleteFile', () => {
		it('should delete a file from a lab report', async () => {
			const labReportId = 'report-1';
			const fileId = 'file-1';
			mockClinicLabReportService.deleteFile.mockResolvedValue({
				id: labReportId,
				files: []
			});

			const result = await controller.deleteFile(labReportId, fileId);
			expect(result).toEqual({ id: labReportId, files: [] });
			expect(mockClinicLabReportService.deleteFile).toHaveBeenCalledWith(
				labReportId,
				fileId
			);
		});
	});

	describe('deleteLabReport', () => {
		it('should delete a lab report', async () => {
			const labReportId = 'report-1';
			mockClinicLabReportService.deleteLabReport.mockResolvedValue(
				undefined
			);

			const result = await controller.deleteLabReport(labReportId, '');
			expect(result).toBeUndefined();
			expect(
				mockClinicLabReportService.deleteLabReport
			).toHaveBeenCalledWith(labReportId, '');
		});
	});

	describe('getLabReportsForClinic', () => {
		it('should return lab reports for a clinic with default pagination', async () => {
			const clinicId = 'clinic-id';
			const mockResponse = {
				data: [{ id: 'lab-report-1', appointmentId: 'appointment-1' }],
				total: 1,
				page: 1,
				limit: 10
			};

			mockClinicLabReportService.getLabReportsForClinic.mockResolvedValue(
				mockResponse
			);
			const startDate = moment().startOf('day').toDate();
			const endDate = moment().endOf('day').toDate();
			const result = await controller.getLabReportsForClinic(clinicId);
			expect(result).toEqual(mockResponse);
			expect(
				mockClinicLabReportService.getLabReportsForClinic
			).toHaveBeenCalledWith(
				clinicId,
				1,
				10,
				startDate,
				endDate,
				undefined,
				undefined
			);
		});

		it('should return lab reports for a clinic with custom pagination and date range', async () => {
			const clinicId = 'clinic-id';
			const page = 2;
			const limit = 20;
			const searchTerm = 'test';
			const status = 'PENDING';
			const mockResponse = {
				data: [{ id: 'lab-report-2', appointmentId: 'appointment-2' }],
				total: 1,
				page: 2,
				limit: 20
			};

			mockClinicLabReportService.getLabReportsForClinic.mockResolvedValue(
				mockResponse
			);

			const result = await controller.getLabReportsForClinic(
				clinicId,
				page,
				limit,
				searchTerm,
				status
			);
			expect(result).toEqual(mockResponse);
			expect(
				mockClinicLabReportService.getLabReportsForClinic
			).toHaveBeenCalledWith(
				clinicId,
				page,
				limit,
				expect.any(Date), // Adjust startDate as needed
				expect.any(Date), // Adjust endDate as needed
				searchTerm,
				status
			);
		});
	});

	describe('updateStatus', () => {
		it('should update lab report status', async () => {
			const updateStatusDto: UpdateStatusDto = {
				id: 'report-1',
				status: 'COMPLETED'
			};

			const updatedStatus = {
				id: 'report-1',
				status: 'COMPLETED'
			};
			mockClinicLabReportService.updateLabReportStatus.mockResolvedValue(
				updatedStatus
			);

			const result = await controller.updateStatus(updateStatusDto);
			expect(result).toEqual(updatedStatus);
			expect(
				mockClinicLabReportService.updateLabReportStatus
			).toHaveBeenCalledWith(updateStatusDto);
		});
	});
	describe('deleteLabReportsByAppointmentId', () => {
		it('should be defined', () => {
			expect(controller.deleteLabReportsByAppointmentId).toBeDefined();
		});
		it('should delete labReport associated with appointmentId', async () => {
			const mockAppointmentId = 'a_1';
			mockClinicLabReportService.deleteLabReportsByAppointmentId.mockResolvedValue(
				undefined
			);

			const result =
				await controller.deleteLabReportsByAppointmentId(
					mockAppointmentId
				);
			expect(result).toBeUndefined();
			expect(
				mockClinicLabReportService.deleteLabReportsByAppointmentId
			).toHaveBeenCalledWith(mockAppointmentId);
		});
		it('should throw badRequest if something when wrong', () => {
			const mockAppointmentId = 'a_1';
			mockClinicLabReportService.deleteLabReportsByAppointmentId.mockRejectedValue(
				undefined
			);

			jest.spyOn(
				controller,
				'deleteLabReportsByAppointmentId'
			).mockRejectedValue(new BadRequestException());
		});
	});
});
