import { Test, TestingModule } from '@nestjs/testing';
import { ILike, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ClinicServicesService } from './clinic-services.service';
import { ClinicServiceEntity } from './entities/clinic-service.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { NotFoundException } from '@nestjs/common';
import { UpdateServiceDto } from './dto/update-services.dto';
import { CreateServiceDto } from './dto/create-services.dto';

describe('ClinicServicesService', () => {
	let service: ClinicServicesService;
	let repository: jest.Mocked<Repository<ClinicServiceEntity>>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockServices: ClinicServiceEntity[] = [
		{
			id: 'service_uuid_1',
			clinicId: 'clinic_uuid_1',
			brandId: 'brand_uuid_1',
			uniqueId: 'unique_id_1',
			serviceName: 'service_name_A',
			chargeablePrice: 0,
			tax: 10
		},
		{
			id: 'service_uuid_2',
			clinicId: 'clinic_uuid_1',
			brandId: 'brand_uuid_1',
			uniqueId: 'unique_id_2',
			serviceName: 'service_name_B',
			chargeablePrice: 0,
			tax: 10
		}
	];

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicServicesService,
				{
					provide: getRepositoryToken(ClinicServiceEntity),
					useValue: {
						find: jest.fn(),
						save: jest.fn(),
						findOne: jest.fn(),
						findOneBy: jest.fn(),
						delete: jest.fn(),
						create: jest.fn(),
						count: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicServicesService>(ClinicServicesService);
		repository = module.get(getRepositoryToken(ClinicServiceEntity));
		logger = module.get(WinstonLogger);

		// Reset all mocks before each test
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('generateUniqueId', () => {
		it('should generate a unique ID with correct format', async () => {
			repository.count.mockResolvedValue(5);
			const uniqueId = await (service as any).generateUniqueId(
				'S_',
				'clinic_uuid'
			);
			expect(uniqueId).toBe('S_000006');
			expect(repository.count).toHaveBeenCalledWith({
				where: { clinicId: 'clinic_uuid' }
			});
		});

		it('should handle first service case', async () => {
			repository.count.mockResolvedValue(0);
			const uniqueId = await (service as any).generateUniqueId(
				'S_',
				'clinic_uuid'
			);
			expect(uniqueId).toBe('S_000001');
		});
	});

	describe('getServices', () => {
		it('should return all services for a clinic without a search keyword', async () => {
			repository.find.mockResolvedValue(mockServices);

			const result = await service.getServices('clinic_uuid_1');

			expect(result).toEqual(mockServices);
			expect(repository.find).toHaveBeenCalledWith({
				where: { clinicId: 'clinic_uuid_1' }
			});
		});

		it('should return filtered services for a clinic with a search keyword', async () => {
			const filteredServices = [mockServices[0]];
			repository.find.mockResolvedValue(filteredServices);

			const result = await service.getServices('clinic_uuid_1', 'A');

			expect(result).toEqual(filteredServices);
			expect(repository.find).toHaveBeenCalledWith({
				where: { serviceName: ILike('%A%'), clinicId: 'clinic_uuid_1' }
			});
		});

		it('should return filtered services with only search keyword', async () => {
			const searchKeyword = 'service_name_A';
			const filteredServices = [mockServices[0]];
			repository.find.mockResolvedValue(filteredServices);

			const result = await service.getServices('', searchKeyword);

			expect(result).toEqual(filteredServices);
			expect(repository.find).toHaveBeenCalledWith({
				where: { serviceName: ILike(`%${searchKeyword}%`) }
			});
		});
	});

	describe('bulkInsert', () => {
		const servicesToInsert: CreateServiceDto[] = [
			{
				serviceName: 'New Service 1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				chargeablePrice: 100,
				tax: 10
			},
			{
				serviceName: 'New Service 2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				chargeablePrice: 150,
				tax: 15
			}
		];

		it('should insert multiple services successfully', async () => {
			repository.count.mockResolvedValue(0);
			repository.findOne.mockResolvedValue(null);
			repository.save.mockImplementation((entity: any) =>
				Promise.resolve(entity)
			);

			const result = await service.bulkInsert(servicesToInsert);

			expect(result).toBe(
				'Bulk insert of 2 services completed successfully'
			);
			expect(repository.save).toHaveBeenCalledTimes(2);
			expect(repository.findOne).toHaveBeenCalledTimes(2);
			expect(logger.log).toHaveBeenCalledWith(
				'Bulk insert completed. Processed 2 services.'
			);
		});

		it('should update existing services and create new ones', async () => {
			const existingService = {
				id: 'existing_uuid',
				...servicesToInsert[0],
				uniqueId: 'S_000001'
			};

			repository.count.mockResolvedValue(1);
			repository.findOne.mockImplementation(async (query: any) =>
				query.where.serviceName === 'New Service 1'
					? existingService
					: null
			);
			repository.save.mockImplementation(async (entity: any) =>
				Promise.resolve(entity)
			);

			const result = await service.bulkInsert(servicesToInsert);

			expect(result).toBe(
				'Bulk insert of 2 services completed successfully'
			);
			expect(repository.save).toHaveBeenCalledTimes(2);
			expect(repository.findOne).toHaveBeenCalledTimes(2);
		});

		it('should throw Error when bulk insert fails', async () => {
			const mockError = new Error('Database error');
			repository.findOne.mockRejectedValue(mockError);

			await expect(service.bulkInsert(servicesToInsert)).rejects.toThrow(
				'Failed to insert services'
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error during bulk insert of services',
				{ error: mockError }
			);
		});
	});

	describe('findOneEntry', () => {
		it('should find a service by serviceName and clinicId', async () => {
			const criteria = {
				serviceName: 'service_name_A',
				clinicId: 'clinic_uuid_1'
			};
			repository.findOne.mockResolvedValue(mockServices[0]);

			const result = await service.findOneEntry(criteria);

			expect(result).toEqual(mockServices[0]);
			expect(repository.findOne).toHaveBeenCalledWith({
				where: criteria
			});
		});

		it('should return null if no service matches criteria', async () => {
			const criteria = {
				serviceName: 'Nonexistent Service',
				clinicId: 'clinic_uuid_1'
			};
			repository.findOne.mockResolvedValue(null);

			const result = await service.findOneEntry(criteria);

			expect(result).toBeNull();
			expect(repository.findOne).toHaveBeenCalledWith({
				where: criteria
			});
		});
	});

	describe('create', () => {
		const createServiceDto: CreateServiceDto = {
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			serviceName: 'Test Service',
			chargeablePrice: 100,
			tax: 10
		};

		it('should create and save a new service', async () => {
			const createdService = {
				...createServiceDto,
				id: 'new_uuid',
				uniqueId: 'S_000001'
			};
			repository.count.mockResolvedValue(0);
			repository.create.mockReturnValue(createdService);
			repository.save.mockResolvedValue(createdService);

			const result = await service.create(createServiceDto);

			expect(repository.create).toHaveBeenCalledWith({
				...createServiceDto,
				uniqueId: 'S_000001'
			});
			expect(repository.save).toHaveBeenCalledWith(createdService);
			expect(result).toEqual(createdService);
		});

		it('should log and throw an error if creation fails', async () => {
			const error = new Error('Creation failed');
			repository.count.mockResolvedValue(0);
			repository.save.mockRejectedValue(error);

			await expect(service.create(createServiceDto)).rejects.toThrow(
				error
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating service',
				{
					error
				}
			);
		});
	});

	describe('update', () => {
		const updateServiceDto: UpdateServiceDto = {
			serviceName: 'Updated Service',
			chargeablePrice: 150
		};

		it('should update and save an existing service', async () => {
			const existingService = mockServices[0];
			const updatedService = { ...existingService, ...updateServiceDto };

			repository.findOneBy.mockResolvedValue(existingService);
			repository.save.mockResolvedValue(updatedService);

			const result = await service.update(
				'existing_uuid',
				updateServiceDto
			);

			expect(repository.findOneBy).toHaveBeenCalledWith({
				id: 'existing_uuid'
			});
			expect(repository.save).toHaveBeenCalledWith(updatedService);
			expect(result).toEqual(updatedService);
		});

		it('should throw NotFoundException if service not found', async () => {
			repository.findOneBy.mockResolvedValue(null);

			await expect(
				service.update('nonexistent_uuid', updateServiceDto)
			).rejects.toThrow(NotFoundException);
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating service',
				{
					error: expect.any(NotFoundException)
				}
			);
		});

		it('should handle save error during update', async () => {
			const error = new Error('Save failed');
			repository.findOneBy.mockResolvedValue(mockServices[0]);
			repository.save.mockRejectedValue(error);

			await expect(
				service.update('existing_uuid', updateServiceDto)
			).rejects.toThrow(error);
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating service',
				{
					error
				}
			);
		});
	});

	describe('findOne', () => {
		it('should return a service if found', async () => {
			repository.findOneBy.mockResolvedValue(mockServices[0]);

			const result = await service.findOne('service_uuid_1');

			expect(repository.findOneBy).toHaveBeenCalledWith({
				id: 'service_uuid_1'
			});
			expect(result).toEqual(mockServices[0]);
		});

		it('should throw NotFoundException if service not found', async () => {
			repository.findOneBy.mockResolvedValue(null);

			await expect(service.findOne('nonexistent_uuid')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('remove', () => {
		it('should delete the service if it exists', async () => {
			repository.delete.mockResolvedValue({ raw: {}, affected: 1 });

			await service.remove('service_uuid');

			expect(repository.delete).toHaveBeenCalledWith('service_uuid');
		});

		it('should throw NotFoundException if service does not exist', async () => {
			repository.delete.mockResolvedValue({ raw: {}, affected: 0 });

			await expect(service.remove('nonexistent_uuid')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('deleteItem', () => {
		it('should delete the item and return delete result', async () => {
			const deleteResult = { raw: {}, affected: 1 };
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('item_uuid');

			expect(result).toEqual(deleteResult);
			expect(repository.delete).toHaveBeenCalledWith('item_uuid');
		});

		it('should return delete result even if item does not exist', async () => {
			const deleteResult = { raw: {}, affected: 0 };
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('nonexistent_uuid');

			expect(result).toEqual(deleteResult);
			expect(repository.delete).toHaveBeenCalledWith('nonexistent_uuid');
		});
	});
});
