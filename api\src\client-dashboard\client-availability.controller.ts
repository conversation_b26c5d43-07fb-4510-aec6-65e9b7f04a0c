import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import {
	ApiBearerAuth,
	ApiOperation,
	ApiQuery,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { ClientAvailabilityService } from './client-availability.service';
import {
	AvailableDatesRequestDto,
	AvailableDatesResponseDto,
	AvailableTimeSlotsResponseDto,
	TimeSlotRequestDto
} from './dto/client-availability.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
@ApiTags('Client Availability')
@Controller()
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ClientAvailabilityController {
	constructor(
		private readonly clientAvailabilityService: ClientAvailabilityService
	) {}

	@Get('client-availability/dates')
	@ApiOperation({ summary: 'Get available dates for selected doctor(s)' })
	@ApiResponse({
		status: 200,
		description: 'Returns available dates for selected doctor(s)',
		type: AvailableDatesResponseDto
	})
	@ApiQuery({ name: 'doctorIds', required: true, type: String, description: 'Can be a single doctor ID, "all", or comma-separated IDs' })
	@ApiQuery({ name: 'startDate', required: false, type: String })
	@ApiQuery({ name: 'endDate', required: false, type: String })
	@ApiQuery({ name: 'clinicId', required: false, type: String })
	@Roles(Role.OWNER, Role.ADMIN)
	async getAvailableDates(
		@Query('doctorIds') doctorIds: string,
		@Query() query: AvailableDatesRequestDto
	): Promise<AvailableDatesResponseDto> {
		return this.clientAvailabilityService.getAvailableDatesForDoctor(
			doctorIds,
			query.startDate,
			query.endDate,
			query.clinicId
		);
	}

	@Get('client-availability/timeslots/:date')
	@ApiOperation({
		summary: 'Get available time slots for selected doctor(s) on a specific date'
	})
	@ApiResponse({
		status: 200,
		description:
			'Returns available time slots for selected doctor(s) on a specific date',
		type: AvailableTimeSlotsResponseDto
	})
	@ApiQuery({ name: 'doctorIds', required: true, type: String, description: 'Can be a single doctor ID, "all", or comma-separated IDs' })
	@ApiQuery({ name: 'clinicId', required: false, type: String })
	@Roles(Role.OWNER, Role.ADMIN)
	async getAvailableTimeSlots(
		@Query('doctorIds') doctorIds: string,
		@Param('date') date: string,
		@Query() query: TimeSlotRequestDto
	): Promise<AvailableTimeSlotsResponseDto> {
		return this.clientAvailabilityService.getAvailableTimeSlotsForDoctor(
			doctorIds,
			date,
			query.clinicId
		);
	}
}
