import {
	IsNotEmpty,
	<PERSON><PERSON>ptional,
	IsString,
	IsBoolean,
	IsEnum,
	IsUUID,
	IsObject
} from 'class-validator';

export class CreateDocumentLibraryDto {
	@IsUUID()
	@IsOptional()
	clinicId?: string;

	@IsString()
	@IsNotEmpty()
	documentName!: string;

	@IsBoolean()
	@IsNotEmpty()
	signatureRequired!: boolean;

	@IsString()
	@IsOptional()
	category?: string;

	@IsEnum(['upload', 'create'])
	@IsNotEmpty()
	documentType!: 'upload' | 'create';

	@IsString()
	@IsOptional()
	fileKey?: string;

	@IsObject()
	@IsOptional()
	documentBody?: { title: string; bodyText: string };

	@IsUUID()
	@IsOptional()
	createdBy?: string;

	@IsUUID()
	@IsOptional()
	updatedBy?: string;
}
