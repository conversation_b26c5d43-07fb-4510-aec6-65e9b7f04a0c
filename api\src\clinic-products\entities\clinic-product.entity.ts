import { Column, Entity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { CartItemEntity } from '../../cart-items/entities/cart-item.entity';

@Entity('clinic_products')
export class ClinicProductEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ name: 'unique_id' })
	uniqueId!: string;

	@Column({ name: 'product_name' })
	productName!: string;

	@Column({ name: 'chargeable_price' })
	chargeablePrice!: number;

	@Column({ name: 'tax' })
	tax!: number;

	@Column({ name: 'current_stock' })
	currentStock!: number;

	@Column({ name: 'minimum_quantity' })
	minimumQuantity!: number;

	@OneToOne(() => CartItemEntity, cart => cart.product)
	cart?: CartItemEntity;
}
