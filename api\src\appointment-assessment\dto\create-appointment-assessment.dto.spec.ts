import { validate } from 'class-validator';
import { CreateAppointmentAssessmentDto } from './create-appointment-assessment.dto';

describe('CreateAppointmentAssessmentDto', () => {
	let dto: CreateAppointmentAssessmentDto;

	beforeEach(() => {
		dto = new CreateAppointmentAssessmentDto();
	});

	it('should fail if name is not provided', async () => {
		const errors = await validate(dto);

		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].property).toBe('name');
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
		expect(errors[0].constraints?.isNotEmpty).toContain(
			'The lab report should have a name.'
		);
	});

	it('should pass if name is provided', async () => {
		dto.name = 'Respiratory Assessment';

		const errors = await validate(dto);

		expect(errors.length).toBe(0);
	});
});
