import {
	<PERSON>umn,
	CreateDateColumn,
	Entity,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	ManyToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Optional } from '@nestjs/common';

@Entity({ name: 'appointment_assessments' })
export class AppointmentAssessmentEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ nullable: false })
	name!: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@Column({ type: 'boolean', name: 'is_added_by_user' })
	@Optional()
	isAddedByUser!: boolean;

	@Column('uuid', { name: 'clinic_id' })
	clinicId!: string;

	@Column('uuid', { name: 'brand_id' })
	brandId!: string;
}
