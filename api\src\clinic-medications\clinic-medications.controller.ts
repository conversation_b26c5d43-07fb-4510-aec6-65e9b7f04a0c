import {
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Patch,
	Post,
	Query,
	UseGuards,
	UsePipes,
	ValidationPipe
} from '@nestjs/common';
import { ClinicMedicationsService } from './clinic-medications.service';
import { ClinicMedicationEntity } from './entities/clinic-medication.entity';
import { CreateClinicMedicationDto } from './dto/create-clinic-medication.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UpdateClinicMedicationDto } from './dto/update-clinic-medication.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@Controller('clinic-medications')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicMedicationsController {
	constructor(
		private readonly clinicMedicationsService: ClinicMedicationsService,
		private readonly logger: WinstonLogger
	) {}

	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getClinicMedications-clinic-medications')
	async getClinicMedications(
		@Query('clinicId') clinicId: string,
		@Query('search') searchKeyword?: string,
		@Query('all') all: boolean = false 
	): Promise<ClinicMedicationEntity[]> {
		return this.clinicMedicationsService.getMedications(
			clinicId,
			searchKeyword,
			all
		);
	}

	@Post()
	@UsePipes(ValidationPipe)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createNewMedication-clinic-medications')
	async createNewMedication(
		@Body() createClinicMedicationDto: CreateClinicMedicationDto
	) {
		try {
			this.logger.log('Creating new medication', {
				dto: createClinicMedicationDto
			});

			return await this.clinicMedicationsService.createNewMedication(
				createClinicMedicationDto
			);
		} catch (error) {
			this.logger.error('Error creating new medication', {
				error,
				createClinicMedicationDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get medication by ID' })
	@ApiResponse({ status: 200, description: 'Medication found' })
	@ApiResponse({ status: 404, description: 'Medication not found' })
	@TrackMethod('findOne-clinic-medications')
	async findOne(@Param('id') id: string) {
		try {
			return await this.clinicMedicationsService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching medication', { error, id });
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Patch(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Update medication' })
	@ApiResponse({ status: 200, description: 'Medication updated' })
	@TrackMethod('update-clinic-medications')
	async update(
		@Param('id') id: string,
		@Body() updateClinicMedicationDto: UpdateClinicMedicationDto
	) {
		try {
			return await this.clinicMedicationsService.update(
				id,
				updateClinicMedicationDto
			);
		} catch (error) {
			this.logger.error('Error updating medication', {
				error,
				id,
				updateClinicMedicationDto
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete medication' })
	@ApiResponse({ status: 200, description: 'Medication deleted' })
	@TrackMethod('remove-clinic-medications')
	async remove(@Param('id') id: string) {
		try {
			await this.clinicMedicationsService.remove(id);
			return { message: 'Medication deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting medication', { error, id });
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
