import { validate } from 'class-validator';
import { CreateAppointmentDetailsDto } from './create-appointment-details.dto';

describe('CreateAppointmentDetailsDto', () => {
	it('should validate successfully with valid data', async () => {
		const dto = new CreateAppointmentDetailsDto();
		dto.appointmentId = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db';
		dto.createdAt = new Date();
		dto.updatedAt = new Date();
		(dto.details = {
			objective: 'objective data',
			subjective: 'subjective data'
		}),
			(dto.createdBy = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db');
		dto.updatedBy = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db';

		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should fail if appointment id is passed with wrong values', async () => {
		const dto = new CreateAppointmentDetailsDto();
		dto.appointmentId = '';
		dto.createdAt = new Date();
		dto.updatedAt = new Date();
		(dto.details = {
			objective: 'objective data',
			subjective: 'subjective data'
		}),
			(dto.createdBy = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db');
		dto.updatedBy = 'b1e57271-d5d6-4ef7-bc2f-bb0b0cf0e5db';

		const errors = await validate(dto);
		expect(errors.length).toBe(1);
	});
});
