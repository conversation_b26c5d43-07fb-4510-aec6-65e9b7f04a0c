import { INestApplication } from '@nestjs/common';
import {
	ApiBadRequestResponse,
	ApiNotFoundResponse,
	ApiUnauthorizedResponse,
	DocumentBuilder,
	SwaggerModule
} from '@nestjs/swagger';
import { description, name, tag, version } from '../../package.json';

@ApiNotFoundResponse({
	description: 'Not Found'
})
@ApiBadRequestResponse({
	description: 'Bad Request'
})
@ApiUnauthorizedResponse({
	description: 'Unauthorized'
})

// We use Swagger for api documentation- The Swagger framework allows developers to create interactive, machine and human-readable API documentation.
export class ApiDocumentationBase {
	static initApiDocumentation(app: INestApplication<any>): void {
		const config = new DocumentBuilder()
			.setTitle(name)
			.setDescription(description)
			.setVersion(version)
			.addTag(tag)
			// .addSecurity()
			.build();
		const document = SwaggerModule.createDocument(app, config);
		SwaggerModule.setup('api', app, document);
	}
}
