import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";

export class CreateClinicIdexxDto {
    @ApiProperty({
		description: 'The clinic id',
		example: 'uuid'
	})
    @IsNotEmpty()
    @IsUUID()
    clinicId!: string;

    @ApiProperty({
		description: 'The IDEXX user name',
		example: 'string'
	})
    userName!: string;

    @ApiProperty({
		description: 'The IDEXX password',
		example: 'string'
	})
    password!: string;

	@ApiProperty({
		description: 'The integration type',
		example: 'IDEXX'
	})
    type!: string;
}