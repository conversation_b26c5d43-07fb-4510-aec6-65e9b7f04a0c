import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientDashboardController } from './client-dashboard.controller';
import { ClientDashboardService } from './client-dashboard.service';
import { ClientAvailabilityController } from './client-availability.controller';
import { ClientAvailabilityService } from './client-availability.service';
import { ClientBookingController } from './client-booking.controller';
import { ClientBookingService } from './client-booking.service';
import { OwnersModule } from '../owners/owners.module';
import { AppointmentsModule } from '../appointments/appointments.module';
import { RoleModule } from '../roles/role.module';
import { BrandsModule } from '../brands/brands.module';
import { PatientsModule } from '../patients/patients.module';
import { UsersModule } from '../users/users.module';
import { AvailabilityModule } from '../availability/availability.module';
import { User } from '../users/entities/user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { AvailabilityExceptionEntity } from '../users/entities/availability-exception.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { Patient } from '../patients/entities/patient.entity';
import { AuditModule } from '../audit/audit.module';
import { ClinicModule } from '../clinics/clinic.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			User,
			ClinicEntity,
			ClinicUser,
			AvailabilityExceptionEntity,
			AppointmentEntity,
			Patient
		]),
		JwtModule.registerAsync({
			imports: [ConfigModule],
			useFactory: async (configService: ConfigService) => ({
				secret: configService.get<string>('JWT_SECRET'),
				signOptions: { expiresIn: '7d' } // Longer expiration for pet owners
			}),
			inject: [ConfigService]
		}),
		OwnersModule,
		AppointmentsModule,
		RoleModule,
		BrandsModule,
		ConfigModule,
		PatientsModule,
		UsersModule,
		AvailabilityModule,
		AuditModule,
		ClinicModule
	],
	controllers: [
		ClientDashboardController,
		ClientAvailabilityController,
		ClientBookingController
	],
	providers: [
		ClientDashboardService,
		ClientAvailabilityService,
		ClientBookingService
	],
	exports: [
		ClientDashboardService,
		ClientAvailabilityService,
		ClientBookingService
	]
})
export class ClientDashboardModule {}
