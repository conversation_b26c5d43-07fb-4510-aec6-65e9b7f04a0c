import { Modu<PERSON> } from '@nestjs/common';
import { ClinicAlertsController } from './clinic-alerts.controller';
import { ClinicAlertsService } from './clinic-alerts.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicAlerts } from './entities/clinicAlerts.entity';
import { RoleModule } from '../roles/role.module';

@Module({
	imports: [TypeOrmModule.forFeature([ClinicAlerts]),RoleModule],
	controllers: [ClinicAlertsController],
	providers: [ClinicAlertsService]
})
export class ClinicAlertsModule {}
