import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ClientAvailabilityController } from './client-availability.controller';
import { ClientAvailabilityService } from './client-availability.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import {
	AvailableDatesRequestDto,
	AvailableDatesResponseDto,
	AvailableTimeSlotsResponseDto,
	TimeSlotRequestDto
} from './dto/client-availability.dto';
import { Role } from '../roles/role.enum';
import { Reflector } from '@nestjs/core';

// Mock ClientAvailabilityService (revert to simple jest.fn())
const mockClientAvailabilityService = {
	getAvailableDatesForDoctor: jest.fn(),
	getAvailableTimeSlotsForDoctor: jest.fn()
};

// Mock Guards (allow all for unit tests, role checks are separate)
const mockJwtAuthGuard = { canActivate: jest.fn(() => true) };
const mockRolesGuard = { canActivate: jest.fn(() => true) }; // We'll test Roles decorator separately if needed

describe('ClientAvailabilityController', () => {
	let controller: ClientAvailabilityController;
	let service: ClientAvailabilityService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClientAvailabilityController],
			providers: [
				{
					provide: ClientAvailabilityService,
					useValue: mockClientAvailabilityService
				},
				// Provide a mock Reflector for RolesGuard if it depends on it internally
				Reflector
			]
		})
			.overrideGuard(JwtAuthGuard)
			.useValue(mockJwtAuthGuard)
			.overrideGuard(RolesGuard)
			.useValue(mockRolesGuard)
			.compile();

		controller = module.get<ClientAvailabilityController>(
			ClientAvailabilityController
		);
		service = module.get<ClientAvailabilityService>(
			ClientAvailabilityService
		);

		// Reset mocks before each test
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	// --- Test cases for getAvailableDates ---

	describe('getAvailableDates', () => {
		const doctorId = 'doc-uuid-123';
		const mockResponse: AvailableDatesResponseDto = {
			availableDates: ['2024-07-01', '2024-07-02']
		};

		it('should return available dates successfully (handling missing optional query params)', async () => {
			const query: AvailableDatesRequestDto = {}; // Empty query, simulating missing optional params
			// Cast to any to bypass strict type checking for mockResolvedValue
			(
				mockClientAvailabilityService.getAvailableDatesForDoctor as any
			).mockResolvedValue(mockResponse);

			const result = await controller.getAvailableDates(doctorId, query);

			expect(result).toEqual(mockResponse);
			expect(service.getAvailableDatesForDoctor).toHaveBeenCalledTimes(1);
			expect(service.getAvailableDatesForDoctor).toHaveBeenCalledWith(
				doctorId,
				undefined, // startDate
				undefined, // endDate
				undefined // clinicId
			);
		});

		it('should handle provided optional query parameters (startDate, endDate, clinicId)', async () => {
			const startDate = '2024-08-01';
			const endDate = '2024-08-15';
			const clinicId = 'clinic-uuid-456';
			const query: AvailableDatesRequestDto = {
				startDate,
				endDate,
				clinicId
			};
			// Cast to any
			(
				mockClientAvailabilityService.getAvailableDatesForDoctor as any
			).mockResolvedValue(mockResponse);

			const result = await controller.getAvailableDates(doctorId, query);

			expect(result).toEqual(mockResponse);
			expect(service.getAvailableDatesForDoctor).toHaveBeenCalledTimes(1);
			expect(service.getAvailableDatesForDoctor).toHaveBeenCalledWith(
				doctorId,
				startDate,
				endDate,
				clinicId
			);
		});

		it('should have Roles decorator for OWNER and ADMIN', () => {
			// This test primarily checks if the decorator is applied.
			// Actual guard logic testing might require a different setup or e2e tests.
			const reflector = new Reflector();
			const roles = reflector.get<Role[]>(
				'roles',
				controller.getAvailableDates
			);
			expect(roles).toEqual([Role.OWNER, Role.ADMIN]);
			// Note: This test might not be fully effective with mocked guards.
			// It verifies metadata presence.
		});

		// Add test for error handling (e.g., service throws an error)
	});

	// --- Test cases for getAvailableTimeSlots ---

	describe('getAvailableTimeSlots', () => {
		const doctorId = 'doc-uuid-123';
		const date = '2024-07-01';
		const mockResponse: AvailableTimeSlotsResponseDto = {
			date: date,
			timeSlots: [
				{ startTime: '09:00', endTime: '09:30', isAvailable: true },
				{ startTime: '10:00', endTime: '10:30', isAvailable: true }
			]
		};

		it('should return available time slots successfully (handling missing optional clinicId)', async () => {
			const query: TimeSlotRequestDto = {}; // Empty query, simulating missing optional clinicId
			// Cast to any
			(
				mockClientAvailabilityService.getAvailableTimeSlotsForDoctor as any
			).mockResolvedValue(mockResponse);

			const result = await controller.getAvailableTimeSlots(
				doctorId,
				date,
				query
			);

			expect(result).toEqual(mockResponse);
			expect(
				service.getAvailableTimeSlotsForDoctor
			).toHaveBeenCalledTimes(1);
			// Verify it was called correctly with undefined clinicId
			expect(service.getAvailableTimeSlotsForDoctor).toHaveBeenCalledWith(
				doctorId,
				date,
				undefined // clinicId
			);
		});

		it('should handle provided optional query parameter (clinicId)', async () => {
			const clinicId = 'clinic-uuid-456';
			const query: TimeSlotRequestDto = { clinicId };
			// Cast to any
			(
				mockClientAvailabilityService.getAvailableTimeSlotsForDoctor as any
			).mockResolvedValue(mockResponse);

			const result = await controller.getAvailableTimeSlots(
				doctorId,
				date,
				query
			);

			expect(result).toEqual(mockResponse);
			expect(
				service.getAvailableTimeSlotsForDoctor
			).toHaveBeenCalledTimes(1);
			// Verify it was called correctly with the provided clinicId
			expect(service.getAvailableTimeSlotsForDoctor).toHaveBeenCalledWith(
				doctorId,
				date,
				clinicId
			);
		});

		it('should have Roles decorator for OWNER and ADMIN', () => {
			// This test checks if the decorator is applied.
			const reflector = new Reflector();
			const roles = reflector.get<Role[]>(
				'roles',
				controller.getAvailableTimeSlots
			);
			expect(roles).toEqual([Role.OWNER, Role.ADMIN]);
		});

		// Add test for error handling (e.g., service throws an error)
	});
});
