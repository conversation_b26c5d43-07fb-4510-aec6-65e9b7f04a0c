import { ApiProperty } from '@nestjs/swagger';
import {
	IsEmail,
	IsMobilePhone,
	IsNotEmpty,
	IsString,
	Length
} from 'class-validator';

export class CreateClinicDto {
	@ApiProperty({
		description: 'The name of the the clinic.',
		example: 'ABC Clinic'
	})
	@IsNotEmpty({ message: 'The clinic should have a name.' })
	@IsString()
	@Length(1, 50)
	name!: string;

	@ApiProperty({ description: 'Brand Id For the Clinic' })
	@IsString()
	@IsNotEmpty()
	brandId!: string;

	@ApiProperty({
		description: 'The first name of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a first name.' })
	@IsString()
	@Length(1, 50)
	adminFirstName!: string;

	@ApiProperty({
		description: 'The last name of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a last name.' })
	@IsString()
	@Length(1, 50)
	@IsNotEmpty()
	@Length(1, 50)
	adminLastName!: string;

	@ApiProperty({
		description: 'The email of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a Email.' })
	@IsEmail()
	@IsNotEmpty()
	adminEmail!: string;

	@ApiProperty({
		description: 'The mobile of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a Mobile.' })
	@IsString()
	@IsNotEmpty()
	@IsMobilePhone()
	adminMobile!: string;
}
export class UpdateBasicClinicDto {
	@ApiProperty({
		description: 'The name of the the clinic.',
		example: 'ABC Clinic'
	})
	@IsNotEmpty({ message: 'The clinic should have a name.' })
	@IsString()
	@Length(1, 50)
	name!: string;

	@ApiProperty({
		description: 'The first name of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a first name.' })
	@IsString()
	@Length(1, 50)
	adminFirstName!: string;

	@ApiProperty({
		description: 'The last name of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a last name.' })
	@IsString()
	@Length(1, 50, {
		message:
			' adminFirstName must be shorter than or equal to 50 characters'
	})
	@IsNotEmpty()
	@Length(1, 50)
	adminLastName!: string;

	@ApiProperty({
		description: 'The email of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a Email.' })
	@IsEmail()
	@IsNotEmpty()
	adminEmail!: string;

	@ApiProperty({
		description: 'The mobile of the Admin',
		example: 'FirstName'
	})
	@IsNotEmpty({ message: 'The Admin should have a Mobile.' })
	@IsString()
	@IsNotEmpty()
	@IsMobilePhone()
	adminMobile!: string;
}
