import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Role } from '../../roles/role.enum';
import { RoleService } from '../../roles/role.service';

@Injectable()
export class RolesGuard implements CanActivate {
	constructor(
		private reflector: Reflector,
		private roleService: RoleService
	) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const requiredRoles = this.reflector.getAllAndOverride<Role[]>(
			'roles',
			[context.getHandler(), context.getClass()]
		);
		if (!requiredRoles) {
			return true;
		}
		const { user } = context.switchToHttp().getRequest();

		// Check if user and role exist
		if (!user || !user.role) {
			return false;
		}

		// If the user is a SUPER_ADMIN, grant access immediately
		if (user.role === Role.SUPER_ADMIN) {
			return true;
		}

		// Convert user role to lowercase for case-insensitive comparison
		const userRole = user.role.toLowerCase();

		// Check if the user's role matches any of the required roles
		return requiredRoles.some(role => {
			const enumRole =
				typeof role === 'string' ? role.toLowerCase() : role;
			return enumRole === userRole;
		});
	}
}
