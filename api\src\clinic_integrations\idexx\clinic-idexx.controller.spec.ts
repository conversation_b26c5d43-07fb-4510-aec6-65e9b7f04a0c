import { Test, TestingModule } from '@nestjs/testing';
import { ClinicIdexxController } from './clinic-idexx.controller';
import { ClinicIdexxService } from './clinic-idexx.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { <PERSON>Logger } from '../../utils/logger/winston-logger.service';
import { CreateClinicIdexxDto } from './dto/create-clinic-idexx.dto';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { User } from '../../users/entities/user.entity';
import { CreateClinicIdexxTestItemDto } from './dto/create_clinic-idexx-test-item.dto';
import { CreateIdexxOrderDto } from './dto/create-idexx-order.dto';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { AxiosResponse } from 'axios';

describe('ClinicIdexxController', () => {
	let controller: ClinicIdexxController;
	let service: jest.Mocked<ClinicIdexxService>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicIdexxController],
			providers: [
				{
					provide: ClinicIdexxService,
					useValue: {
						createIdexxEntry: jest.fn(),
						getIdexxEntries: jest.fn(),
						deletIdexxEntry: jest.fn(),
						getAllIDexxTestsList: jest.fn(),
						createIdexxTestItem: jest.fn(),
						deleteIdexxTestItem: jest.fn(),
						createIdexxOrder: jest.fn(),
						cancelIdexxOrder: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicIdexxController>(ClinicIdexxController);
		service = module.get(ClinicIdexxService);
	});

	const createMockOutput = (
		items: CreateClinicIdexxEntity[],
		total: number
	) => ({
		items,
		total
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('Create a IDEXX entry for a clinic - POST - /clinic-integrations', () => {
		const mockInputDto: CreateClinicIdexxDto = {
			clinicId: 'clinic_uuid',
			userName: '',
			password: '',
			type: ''
		};

		it('should have a createIdexxEntry function', () => {
			expect(controller.createIdexxEntry).toBeDefined();
		});

		it('should create an entry in the table and return with id', async () => {
			const mockOutputEntity: CreateClinicIdexxEntity = {
				clinicId: 'clinic_uuid',
				id: 'idexx_uuid',
				userName: '',
				password: '',
				type: '',
				authKey: '',
				clinic: new ClinicEntity()
			};
			service.createIdexxEntry.mockResolvedValue(mockOutputEntity);

			const result = await controller.createIdexxEntry(mockInputDto);

			expect(service.createIdexxEntry).toHaveBeenCalledWith(mockInputDto);
			expect(result).toEqual(mockOutputEntity);
		});

		it('should throw error if something goes wrong', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			service.createIdexxEntry.mockRejectedValue(error);

			await expect(
				controller.createIdexxEntry(mockInputDto)
			).rejects.toThrow('Something went wrong');

			expect(service.createIdexxEntry).toHaveBeenCalled();
		});
	});

	describe('Get all IDEXX entries for a clinic - GET - /clinic-integrations/:clinicIdexxId', () => {
		const mockEntries: CreateClinicIdexxEntity[] = [
			{
				id: 'entry_uuid_1',
				clinicId: 'clinic_uuid',
				userName: '',
				password: '',
				type: '',
				authKey: '',
				clinic: new ClinicEntity()
			},
			{
				id: 'entry_uuid_2',
				clinicId: 'clinic_uuid',
				userName: '',
				password: '',
				type: '',
				authKey: '',
				clinic: new ClinicEntity()
			}
		];

		const clinicId = 'clinic_uuid';

		it('should have a createIdexxEntry function', () => {
			expect(controller.getIdexxEntries).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the getIdexxEntries call', async () => {
			const error = new HttpException(
				'Error fetching list by clinic id',
				HttpStatus.NOT_FOUND
			);

			service.getIdexxEntries.mockRejectedValue(error);

			await expect(controller.getIdexxEntries(clinicId)).rejects.toThrow(
				'Error fetching list by clinic id'
			);
			expect(service.getIdexxEntries).toHaveBeenCalledWith(clinicId);
		});

		it('should return the list of idexx entries for a given clinic', async () => {
			const mockOutput = createMockOutput([mockEntries[0]], 1);

			service.getIdexxEntries.mockResolvedValue(mockOutput);

			const result = await controller.getIdexxEntries(clinicId);

			expect(result.items).toEqual(mockOutput.items);
			expect(result.total).toBe(mockOutput.total);

			expect(service.getIdexxEntries).toHaveBeenCalledWith(clinicId);
		});
	});

	describe('Delete a IDEXX entry item - DELETE /clinic-integrations/:clinicIdexxId', () => {
		const clinicIdexxId = 'entry_id';

		it('should have deletIdexxEntry function', () => {
			expect(controller.deletIdexxEntry).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the deletIdexxEntry call', async () => {
			const error = new HttpException(
				`This entry with ${clinicIdexxId} doesn't exist`,
				HttpStatus.NOT_FOUND
			);

			service.deletIdexxEntry.mockRejectedValue(error);
			await expect(
				controller.deletIdexxEntry(clinicIdexxId)
			).rejects.toThrow(`This entry with ${clinicIdexxId} doesn't exist`);
			expect(service.deletIdexxEntry).toHaveBeenCalledWith(clinicIdexxId);
		});

		it('should delete an entry item for the given entry id', async () => {
			const mockDeletedItem = { status: true };
			service.deletIdexxEntry.mockResolvedValue(mockDeletedItem);
			const result = await controller.deletIdexxEntry(clinicIdexxId);
			expect(result).toEqual(mockDeletedItem);
			expect(service.deletIdexxEntry).toHaveBeenCalledWith(clinicIdexxId);
		});
	});

	describe('Get all the tests list supported by IDEXX - GET - clinic-integrations/:clinicId/testsList', () => {
		const clinicId = 'clinic_id';

		it('should have a getAllIDexxTestsList function', () => {
			expect(controller.getAllIDexxTestsList).toBeDefined();
		});

		it('should throw error if something goes wrong in getAllIDexxTestsList call', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			service.getAllIDexxTestsList.mockRejectedValue(error);

			await expect(
				controller.getAllIDexxTestsList(clinicId)
			).rejects.toThrow('Something went wrong');
			expect(service.getAllIDexxTestsList).toHaveBeenCalled();
		});

		it('should return list of IDEXX tests list', async () => {
			service.getAllIDexxTestsList.mockResolvedValue([]);

			const result = await controller.getAllIDexxTestsList(clinicId);

			expect(service.getAllIDexxTestsList).toHaveBeenCalled();
			expect(result).toEqual([]);
		});
	});

	describe('Create the data in the clinic_lab_reports table for IDEXX entry - POST - clinic-integrations/labReport/:clinicLabReportEntryId', () => {
		const mockInput: CreateClinicIdexxTestItemDto = {
			integrationType: 'IDEXX',
			clinicId: 'clinic_uuid',
			name: 'test item',
			description: 'test item description',
			chargeablePrice: 230,
			tax: 20
		};

		it('should have a createIdexxTestItem function', () => {
			expect(controller.createIdexxTestItem).toBeDefined();
		});

		it('should create an entry successfully - POST - /clinic-integrations', async () => {
			const createdDate = new Date();

			const mockOutput = {
				id: '',
				clinicId: '',
				name: '',
				createdAt: createdDate,
				updatedAt: createdDate,
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				uniqueId: '',
				chargeablePrice: 0,
				tax: 0,
				associatedLab: 0,
				description: '',
				brandId: '',
				integrationType: 'IDEXX'
			};

			service.createIdexxTestItem.mockResolvedValue(mockOutput);

			const result = await controller.createIdexxTestItem(mockInput);
			expect(result).toEqual(mockOutput);
			expect(service.createIdexxTestItem).toHaveBeenCalledWith(mockInput);
		});

		it('should throw error if something goes wrong in createIdexxTestItem call', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			service.createIdexxTestItem.mockRejectedValue(error);

			await expect(
				controller.createIdexxTestItem(mockInput)
			).rejects.toThrow('Something went wrong');

			expect(service.createIdexxTestItem).toHaveBeenCalledWith(mockInput);
		});
	});

	describe('Delete a IDEXX entry item - DELETE /clinic-integrations/labReport/:clinicLabReportEntryId', () => {
		const clinicLabReportEntryId = 'entry_id';

		it('should have deleteIdexxTestItem function', () => {
			expect(controller.deleteIdexxTestItem).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the deleteIdexxTestItem call', async () => {
			const error = new HttpException(
				`This entry with ${clinicLabReportEntryId} doesn't exist`,
				HttpStatus.NOT_FOUND
			);

			service.deleteIdexxTestItem.mockRejectedValue(error);
			await expect(
				controller.deleteIdexxTestItem(clinicLabReportEntryId)
			).rejects.toThrow(
				`This entry with ${clinicLabReportEntryId} doesn't exist`
			);
			expect(service.deleteIdexxTestItem).toHaveBeenCalledWith(
				clinicLabReportEntryId
			);
		});

		it('should delete an entry item for the given entry id', async () => {
			const mockDeletedItem = { status: true };
			service.deleteIdexxTestItem.mockResolvedValue(mockDeletedItem);
			const result = await controller.deleteIdexxTestItem(
				clinicLabReportEntryId
			);
			expect(result).toEqual(mockDeletedItem);
			expect(service.deleteIdexxTestItem).toHaveBeenCalledWith(
				clinicLabReportEntryId
			);
		});
	});

	describe('Create an IDEXX order - POST - /clinic-integrations/create', () => {
		const mockInputOrderdto: CreateIdexxOrderDto = {
			patientId: 'patient_uuid',
			appointmentId: 'appointment_uuid',
			clinicLabReportId: 'clinic_lab_report_uuid',
			clinicId: 'clinic_uuid'
		};
		it('should have a createIdexxOrder function', () => {
			expect(controller.createIdexxOrder).toBeDefined();
		});

		it('should create an IDEXX order and return its order id', async () => {
			const mockOutputEntity = { orderId: 'order_id', uiURL: 'test_url' };
			service.createIdexxOrder.mockResolvedValue(mockOutputEntity);

			const result = await controller.createIdexxOrder(mockInputOrderdto);

			expect(service.createIdexxOrder).toHaveBeenCalledWith(
				mockInputOrderdto
			);
			expect(result).toEqual(mockOutputEntity);
		});

		it('should throw error if something goes wrong', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			service.createIdexxOrder.mockRejectedValue(error);

			await expect(
				controller.createIdexxOrder(mockInputOrderdto)
			).rejects.toThrow('Something went wrong');

			expect(service.createIdexxOrder).toHaveBeenCalledWith(
				mockInputOrderdto
			);
		});
	});

	describe('Cancel an IDEXX order - DELETE - /clinic-integrations/:idexxOrderId', () => {
		const idexxOrderId = 'oreder_id';
		const clinicId = 'clinic_id';

		it('should have a createIdexxOrder function', () => {
			expect(controller.cancelIdexxOrder).toBeDefined();
		});

		it('should cancel an order with the given idexx order id', async () => {
			const mockOutput: AxiosResponse<any> = {
				data: { status: 'CANCELLED' },
				status: 200,
				statusText: 'OK',
				headers: {},
				config: { headers: { 'Content-Type': 'JSON' } as any }
			};
			// const mock;
			service.cancelIdexxOrder.mockResolvedValue(mockOutput);

			const result = await controller.cancelIdexxOrder(
				clinicId,
				idexxOrderId
			);
			expect(service.cancelIdexxOrder).toHaveBeenCalledWith(
				clinicId,
				idexxOrderId
			);
		});

		it('should throw error if something goes wrong', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			service.cancelIdexxOrder.mockRejectedValue(error);

			await expect(
				controller.cancelIdexxOrder(clinicId, idexxOrderId)
			).rejects.toThrow('Something went wrong');

			expect(service.cancelIdexxOrder).toHaveBeenCalledWith(
				clinicId,
				idexxOrderId
			);
		});
	});
});
