import { Test, TestingModule } from '@nestjs/testing';
import { ChatRoomService } from './chat-room.service';
import { ChatRoom } from './chat-room.entity';
import { CreateChatRoomDto } from './dto/create-chat-room.dto';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatRoomUser } from './chat-room-users.entity';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { InternalServerErrorException } from '@nestjs/common';
import { CreateChatMessageDto } from './dto/create-chat-message.dto';
import { UpdateChatUserRoomDto } from './dto/update-chat-user-room.dto';

describe('ChatRoomService', () => {
  let service: ChatRoomService;
  let chatRoomRepository: jest.Mocked<Repository<ChatRoom>>
  let chatRoomUserRepository: jest.Mocked<Repository<ChatRoom>>
  let chatRoomMessageRepository : jest.Mocked<Repository<ChatRoom>>
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ChatRoomService,
        {
          provide:getRepositoryToken(ChatRoom),
          useValue: {
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn()
					}
        },
        {
          provide:getRepositoryToken(ChatRoomUser),
          useValue: {
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn()
					}
        },
        {
          provide:getRepositoryToken(ChatRoomMessage),
          useValue: {
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn()
					}
        }
      ],
    }).compile();

    service = module.get<ChatRoomService>(ChatRoomService);
    chatRoomRepository = module.get(getRepositoryToken(ChatRoom))
    chatRoomUserRepository = module.get(getRepositoryToken(ChatRoomUser))
    chatRoomMessageRepository = module.get(getRepositoryToken(ChatRoomMessage))

  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create chatRoom',() => {

    it('should be defined the service', () => {
      expect(service.create).toBeDefined();
    })

    it('should create the chatRoom', async() => {
      const mockChatRoomId = 'c_1';
      const mockChatRoom : ChatRoom = {
        id: 'c_1',
        lastMessageSender: 'a_1',
        lastMessage: 'hi',
        unreadMessage: 0,
        users: [],
        messages: [],
        createdAt: new Date(),
        createdBy: 'd_1',
        updatedAt: new Date(),
        updatedBy: 'e_1'
      };
      const mockChatRoomDto:CreateChatRoomDto = {
        userIds: ['c_1'],
      };
      chatRoomUserRepository.save.mockResolvedValue(mockChatRoom)
			chatRoomRepository.save.mockResolvedValue(mockChatRoom);
      const result = await service.create(mockChatRoomDto);

      expect(chatRoomRepository.save).toHaveBeenCalled();
      expect(chatRoomUserRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockChatRoom)

    })

    it('should throw an error if userChatRoom creation fails', async () => {
      const mockChatRoomDto: CreateChatRoomDto = {
        userIds: ['s1'],
      };
    
      const mockChatRoom = {} as ChatRoom
      chatRoomRepository.save.mockResolvedValue(mockChatRoom); // Ensure save returns null
    
      await expect(service.create(mockChatRoomDto)).rejects.toThrow(InternalServerErrorException);
    });
  })

  describe('findAllForUser', () => {
    it('Service must be defined', () => {
      expect(service.findAllForUser).toBeDefined();
    })

    it('should find all the chat_room_users', async() => {
      const mockUserId = 'u_1';

      const mockChatRoom : ChatRoom []=[ {
        id: 'c_1',
        lastMessageSender: 'a_1',
        lastMessage: 'hi',
        unreadMessage: 0,
        users: [],
        messages: [],
        createdAt: new Date(),
        createdBy: 'd_1',
        updatedAt: new Date(),
        updatedBy: 'e_1'
      }];
      chatRoomUserRepository.find.mockResolvedValue(mockChatRoom)
      const result = await service.findAllForUser(mockUserId);
      expect(chatRoomUserRepository.find).toHaveBeenCalled();
      expect(result).toEqual(mockChatRoom)

    })
  })

  describe('sendMessage', () => {
    it('service must be defined', () => {
      expect(service.sendMessage).toBeDefined();
    })

    it('should send message', async () => {
      const mockCreateChatMessageDto :CreateChatMessageDto = {
        chatRoomId: 'a_1',
        message: 'testing',
        senderId:  'c_1',
        otherUserId: 'b_1'
      };
      const mockChatRoom : ChatRoom = {
        id: 'c_1',
        lastMessageSender: 'a_1',
        lastMessage: 'hi',
        unreadMessage: 0,
        users: [],
        messages: [],
        createdAt: new Date(),
        createdBy: 'd_1',
        updatedAt: new Date(),
        updatedBy: 'e_1'
      };
      chatRoomRepository.findOne.mockResolvedValue(mockChatRoom);
      chatRoomRepository.save.mockResolvedValue(mockChatRoom);

      chatRoomMessageRepository.save.mockResolvedValue(mockChatRoom);

      const response = await service.sendMessage(mockCreateChatMessageDto);

      expect(chatRoomRepository.findOne).toHaveBeenCalled()
      expect(chatRoomRepository.save).toHaveBeenCalled()
      expect(chatRoomMessageRepository.save).toHaveBeenCalled()
      expect(response).toEqual(mockChatRoom)

    })

    it('should throw error if chatRoom is not there', async() => {
      const mockCreateChatMessageDto :CreateChatMessageDto = {
        chatRoomId: 'a_1',
        message: 'testing',
        senderId:  'c_1',
        otherUserId: 'b_1'
      };
      const error =  new InternalServerErrorException(
        'Chat room not found'
      );
      chatRoomRepository.findOne.mockResolvedValue(null);
      await expect(service.sendMessage(mockCreateChatMessageDto)).rejects.toThrow(error);

    })
  })

  describe('getChatRoomDetails', () => {
    it('service must be defined', () => {
      expect(service.getChatRoomDetails).toBeDefined();
    })

    it('should be return the details of chatRoom', async() => {
      const mockChatRoomId: string = 'c_1';
      const mockChatRoom : ChatRoom = {
        id: 'c_1',
        lastMessageSender: 'a_1',
        lastMessage: 'hi',
        unreadMessage: 0,
        users: [],
        messages: [],
        createdAt: new Date(),
        createdBy: 'd_1',
        updatedAt: new Date(),
        updatedBy: 'e_1'
      };

      chatRoomRepository.findOne.mockResolvedValue(mockChatRoom)

      const response = await service.getChatRoomDetails(mockChatRoomId);
      expect(chatRoomRepository.findOne).toHaveBeenCalled();
      expect(response).toEqual(mockChatRoom);

    })
    it('should throw error if chatRoom is not there', async() => {
      const mockChatRoomId: string = 'c_1';
      const error =  new InternalServerErrorException(
        'No chat room found with given id'
      );
      chatRoomRepository.findOne.mockResolvedValue(null);
      await expect(service.getChatRoomDetails(mockChatRoomId)).rejects.toThrow(error);

    })
  })

  describe('updateChatRoom', () => {
    it('service must be defined', () => {
      expect(service.updateChatRoom).toBeDefined();
    })
    it('should update the chatRoom', async () => {
      const mockId = 'c_1';
      const mockUpdateChatUserRoomDto :UpdateChatUserRoomDto = {
        unreadMessage: 2
      }
      const mockChatRoom : ChatRoom = {
        id: 'c_1',
        lastMessageSender: 'a_1',
        lastMessage: 'hi',
        unreadMessage: 0,
        users: [],
        messages: [],
        createdAt: new Date(),
        createdBy: 'd_1',
        updatedAt: new Date(),
        updatedBy: 'e_1'
      };

      chatRoomRepository.findOne.mockResolvedValue(mockChatRoom);
      chatRoomRepository.save.mockResolvedValue(mockChatRoom);

      const response = await service.updateChatRoom(mockId, mockUpdateChatUserRoomDto);
      
      expect(chatRoomRepository.findOne).toHaveBeenCalled();
      expect(chatRoomRepository.save).toHaveBeenCalled();
      expect(response).toEqual(mockChatRoom);

    })
    it('should throw error if chatRoom is not there', async() => {
      const mockChatRoomId: string = 'c_1';
      const mockUpdateChatUserRoomDto :UpdateChatUserRoomDto = {
        unreadMessage: 2
      }
      const error =  new InternalServerErrorException(
        `chatRoom with ID "${mockChatRoomId}" not found`
      );
      chatRoomRepository.findOne.mockResolvedValue(null);

      await expect(service.updateChatRoom(mockChatRoomId,mockUpdateChatUserRoomDto)).rejects.toThrow(error);

    })
  })

});
