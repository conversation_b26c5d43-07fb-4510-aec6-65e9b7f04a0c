import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyToOne,
	JoinColumn,
	CreateDateColumn,
	Index
} from 'typeorm';

// Re-define the enum here for use within the entity and application code
export enum AppointmentAuditLogAction {
	CREATE = 'CREATE',
	UPDATE = 'UPDATE',
	CANCEL = 'CANCEL'
}

@Entity('appointment_audit_log') // Ensure this matches the table name in the migration
@Index(['appointmentId'])
@Index(['userId'])
@Index(['timestamp'])
export class AppointmentAuditLog {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', comment: 'ID of the appointment being audited' })
	appointmentId!: string;

	@Column({
		type: 'uuid',
		comment: 'ID of the user (owner) performing the action'
	})
	userId!: string;

	@Column({
		type: 'varchar',
		enum: AppointmentAuditLogAction,
		comment: 'The action performed (CREATE, UPDATE, CANCEL)'
	})
	action!: AppointmentAuditLogAction;

	@Column({
		type: 'jsonb',
		nullable: true,
		comment: 'JSON object detailing the changes made'
	})
	changedFields!: Record<string, any> | null; // Store changes as a JSON object

	@Column({
		type: 'text',
		nullable: true,
		comment: 'Optional additional context'
	})
	context!: string | null;

	// Use CreateDateColumn for automatic timestamping on creation
	@CreateDateColumn({
		type: 'timestamp with time zone',
		default: () => 'CURRENT_TIMESTAMP',
		comment: 'Timestamp when the action occurred'
	})
	timestamp!: Date;
}
