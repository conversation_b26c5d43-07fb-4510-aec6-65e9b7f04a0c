import { Test, TestingModule } from '@nestjs/testing';
import { ClinicMedicationsService } from './clinic-medications.service';
import { ClinicMedicationEntity } from './entities/clinic-medication.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DeepPartial, DeleteResult, ILike, Repository } from 'typeorm';
import {
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { CreateClinicMedicationDto } from './dto/create-clinic-medication.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { UpdateClinicMedicationDto } from './dto/update-clinic-medication.dto';
import { BulkOperationDto } from './clinic-medications.service';
import { User } from '../users/entities/user.entity';

describe('ClinicMedicationsService', () => {
	let service: ClinicMedicationsService;
	let repository: jest.Mocked<Repository<ClinicMedicationEntity>>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockUser = {
		id: 'user1',
		email: '<EMAIL>',
		password: 'hashedPassword',
		firstName: 'Test',
		lastName: 'User',
		pin: '1234',
		roleId: 'role1',
		role: null,
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
		createdBy: null,
		updatedBy: null,
		createdByUser: null,
		updatedByUser: null,
		clinicId: 'clinic1',
		clinic: null,
		clinicUser: null,
		clinicUserRole: null
	} as unknown as User;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicMedicationsService,
				{
					provide: getRepositoryToken(ClinicMedicationEntity),
					useValue: {
						find: jest.fn(),
						save: jest.fn(),
						findOne: jest.fn(),
						findOneBy: jest.fn(),
						delete: jest.fn(),
						create: jest.fn(),
						count: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicMedicationsService>(
			ClinicMedicationsService
		);
		repository = module.get(getRepositoryToken(ClinicMedicationEntity));
		logger = module.get(WinstonLogger);
	});

	describe('getMedications', () => {
		const mockMedications: ClinicMedicationEntity[] = [
			{
				id: '1',
				name: 'Medication A',
				isAddedByUser: false,
				clinicId: 'clinic1',
				brandId: 'brand1',
				uniqueId: 'M-000001',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 10,
				isRestricted: 'NO',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: mockUser.id,
				updatedBy: mockUser.id,
				createdByUser: mockUser,
				updatedByUser: mockUser,
				longTermMedication: null
			} as unknown as ClinicMedicationEntity,
			{
				id: '2',
				name: 'Medication B',
				isAddedByUser: false,
				clinicId: 'clinic1',
				brandId: 'brand1',
				uniqueId: 'M-000002',
				chargeablePrice: 150,
				tax: 15,
				currentStock: 30,
				minimumQuantity: 5,
				isRestricted: 'NO',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: mockUser.id,
				updatedBy: mockUser.id,
				createdByUser: mockUser,
				updatedByUser: mockUser,
				longTermMedication: null
			} as unknown as ClinicMedicationEntity
		];

		it('should return all medications when no search keyword is provided', async () => {
			repository.find.mockResolvedValue(mockMedications);

			const result = await service.getMedications('clinic1');
			expect(result).toEqual(mockMedications);
			expect(repository.find).toHaveBeenCalledWith({
				where: { isAddedByUser: false, clinicId: 'clinic1' }
			});
		});

		it('should return filtered medications when search keyword is provided', async () => {
			const searchKeyword = 'Med';
			repository.find.mockResolvedValue([mockMedications[0]]);

			const result = await service.getMedications(
				'clinic1',
				searchKeyword
			);
			expect(result).toEqual([mockMedications[0]]);
			expect(repository.find).toHaveBeenCalledWith({
				where: {
					name: ILike(`%${searchKeyword}%`),
					isAddedByUser: false,
					clinicId: 'clinic1'
				}
			});
		});
	});

	describe('createNewMedication', () => {
		it('should create a new medication successfully', async () => {
			const createDto: CreateClinicMedicationDto = {
				name: 'New Medication',
				clinicId: 'clinic1',
				isAddedByUser: true
			};

			const uniqueId = 'M-000001';
			repository.count.mockResolvedValue(0);

			const savedMedication: ClinicMedicationEntity = {
				id: 'new_id',
				...createDto,
				uniqueId,
				isRestricted: 'NO',
				brandId: '',
				chargeablePrice: 0,
				tax: 0,
				currentStock: 0,
				minimumQuantity: 0,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: mockUser.id,
				updatedBy: mockUser.id,
				createdByUser: mockUser,
				updatedByUser: mockUser,
				longTermMedication: null
			} as unknown as ClinicMedicationEntity;

			repository.create.mockReturnValue(savedMedication);
			repository.save.mockResolvedValue(savedMedication);

			const result = await service.createNewMedication(createDto);

			expect(result).toEqual(savedMedication);
			expect(repository.create).toHaveBeenCalledWith({
				...createDto,
				uniqueId
			});
			expect(repository.save).toHaveBeenCalled();
		});

		it('should throw InternalServerErrorException when save fails', async () => {
			const createDto: CreateClinicMedicationDto = {
				name: 'New Medication',
				clinicId: 'clinic1',
				isAddedByUser: true
			};

			repository.count.mockResolvedValue(0);
			repository.save.mockRejectedValue(new Error('Database error'));

			await expect(
				service.createNewMedication(createDto)
			).rejects.toThrow(InternalServerErrorException);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating medication',
				{
					error: expect.any(Error)
				}
			);
		});
	});

	describe('findOne', () => {
		it('should return a medication if found', async () => {
			const mockMedication: ClinicMedicationEntity = {
				id: '1',
				name: 'Test Medication',
				clinicId: 'clinic1',
				brandId: '',
				uniqueId: 'M-000001',
				isRestricted: 'NO',
				chargeablePrice: 0,
				tax: 0,
				currentStock: 0,
				minimumQuantity: 0,
				isAddedByUser: true,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: mockUser.id,
				updatedBy: mockUser.id,
				createdByUser: mockUser,
				updatedByUser: mockUser,
				longTermMedication: null
			} as unknown as ClinicMedicationEntity;

			repository.findOne.mockResolvedValue(mockMedication);

			const result = await service.findOne('1');
			expect(result).toEqual(mockMedication);
			expect(repository.findOne).toHaveBeenCalledWith({
				where: { id: '1' }
			});
		});

		it('should throw NotFoundException if medication not found', async () => {
			repository.findOne.mockResolvedValue(null);

			await expect(service.findOne('1')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('update', () => {
		it('should update a medication successfully', async () => {
			const updateDto: UpdateClinicMedicationDto = {
				name: 'Updated Medication',
				isAddedByUser: true
			};

			const existingMedication: ClinicMedicationEntity = {
				id: '1',
				name: 'Old Name',
				clinicId: 'clinic1',
				brandId: '',
				uniqueId: 'M-000001',
				isRestricted: 'NO',
				chargeablePrice: 0,
				tax: 0,
				currentStock: 0,
				minimumQuantity: 0,
				isAddedByUser: true,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: mockUser.id,
				updatedBy: mockUser.id,
				createdByUser: mockUser,
				updatedByUser: mockUser,
				longTermMedication: null
			} as unknown as ClinicMedicationEntity;

			const updatedMedication = { ...existingMedication, ...updateDto };

			repository.findOne.mockResolvedValue(existingMedication);
			repository.save.mockResolvedValue(updatedMedication);

			const result = await service.update('1', updateDto);

			expect(result).toEqual(updatedMedication);
			expect(repository.save).toHaveBeenCalledWith(updatedMedication);
		});

		it('should throw NotFoundException when medication not found', async () => {
			repository.findOne.mockResolvedValue(null);

			await expect(
				service.update('1', { name: 'Updated' })
			).rejects.toThrow(NotFoundException);
		});
	});

	describe('remove', () => {
		it('should delete a medication successfully', async () => {
			const deleteResult: DeleteResult = {
				affected: 1,
				raw: {}
			};
			repository.delete.mockResolvedValue(deleteResult);

			await service.remove('1');

			expect(repository.delete).toHaveBeenCalledWith('1');
		});

		it('should throw NotFoundException when medication not found', async () => {
			const deleteResult: DeleteResult = {
				affected: 0,
				raw: {}
			};
			repository.delete.mockResolvedValue(deleteResult);

			await expect(service.remove('1')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('bulkInsert', () => {
		it('should successfully insert multiple medications', async () => {
			const items: BulkOperationDto[] = [
				{
					clinicId: 'clinic1',
					brandId: 'brand1',
					uniqueId: 'M-001',
					name: 'Med A',
					chargeablePrice: 100,
					tax: 10,
					currentStock: 50,
					minimumQuantity: 10
				},
				{
					clinicId: 'clinic1',
					brandId: 'brand1',
					uniqueId: 'M-002',
					name: 'Med B',
					chargeablePrice: 150,
					tax: 15,
					currentStock: 30,
					minimumQuantity: 5
				}
			];

			repository.count.mockResolvedValue(0);
			repository.findOne.mockResolvedValue(null);
			repository.save.mockImplementation((entity: any) =>
				Promise.resolve({
					id: 'generated_id',
					...entity,
					isRestricted: 'NO',
					isAddedByUser: true,
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: mockUser.id,
					updatedBy: mockUser.id,
					createdByUser: mockUser,
					updatedByUser: mockUser,
					longTermMedication: null
				} as unknown as ClinicMedicationEntity)
			);

			const result = await service.bulkInsert(items);

			expect(result).toBe(
				'Bulk insert of 2 medications completed successfully'
			);
			expect(repository.save).toHaveBeenCalled();
		});

		it('should throw InternalServerErrorException when bulk insert fails', async () => {
			const items: BulkOperationDto[] = [
				{
					clinicId: 'clinic1',
					brandId: 'brand1',
					uniqueId: 'M-001',
					name: 'Med A'
				}
			];

			repository.save.mockRejectedValue(new Error('Database error'));

			await expect(service.bulkInsert(items)).rejects.toThrow(
				InternalServerErrorException
			);
			expect(logger.error).toHaveBeenCalledWith('Error in bulk insert', {
				error: expect.any(Error)
			});
		});
	});

	describe('findOneEntry', () => {
		it('should find a medication by name and clinicId', async () => {
			const criteria = { name: 'Med A', clinicId: 'clinic1' };
			const expectedMedication: ClinicMedicationEntity = {
				id: '1',
				...criteria,
				brandId: 'brand1',
				uniqueId: 'M-000001',
				isRestricted: 'NO',
				chargeablePrice: 0,
				tax: 0,
				currentStock: 0,
				minimumQuantity: 0,
				isAddedByUser: true,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: mockUser.id,
				updatedBy: mockUser.id,
				createdByUser: mockUser,
				updatedByUser: mockUser,
				longTermMedication: null
			} as unknown as ClinicMedicationEntity;

			repository.findOne.mockResolvedValue(expectedMedication);

			const result = await service.findOneEntry(criteria);

			expect(result).toEqual(expectedMedication);
			expect(repository.findOne).toHaveBeenCalledWith({
				where: criteria
			});
		});

		it('should return null if no medication is found', async () => {
			const criteria = { name: 'NonExistent', clinicId: 'clinic1' };
			repository.findOne.mockResolvedValue(null);

			const result = await service.findOneEntry(criteria);

			expect(result).toBeNull();
		});
	});

	describe('deleteItem', () => {
		it('should delete the medication item', async () => {
			const deleteResult: DeleteResult = {
				affected: 1,
				raw: {}
			};
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('1');

			expect(result).toEqual(deleteResult);
			expect(repository.delete).toHaveBeenCalledWith('1');
		});

		it('should return affected: 0 when item not found', async () => {
			const deleteResult: DeleteResult = {
				affected: 0,
				raw: {}
			};
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('1');

			expect(result).toEqual(deleteResult);
		});
	});
});
