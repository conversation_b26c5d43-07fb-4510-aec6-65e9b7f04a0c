import {
	HttpException,
	HttpStatus,
	Injectable,
	NotFoundException
} from '@nestjs/common';
import { CreateClinicIdexxDto } from './dto/create-clinic-idexx.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { MoreThan, Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { delay, firstValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';
import { CreateClinicIdexxTestItemDto } from './dto/create_clinic-idexx-test-item.dto';
import { CreateIdexxOrderDto } from './dto/create-idexx-order.dto';
import { PatientsService } from '../../patients/patients.service';
import { ClinicLabReportService } from '../../clinic-lab-report/clinic-lab-report.service';
import { CreateLabReportDto } from '../../clinic-lab-report/dto/lab-report.dto';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { uuidv4 } from 'uuidv7';
import { ClinicIdexxUtilsService } from '../../utils/idexx/clinic-idexx-utils.service';
import { Cron } from '@nestjs/schedule';
import { AppointmentsService } from '../../appointments/appointments.service';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { AppointmentDetailsEntity } from '../../appointments/entities/appointment-details.entity';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { EnumAppointmentStatus } from '../../appointments/enums/enum-appointment-status';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import * as moment from 'moment';
// Import tough-cookie and axios-cookiejar-support
import { CookieJar } from 'tough-cookie';
import axios from 'axios';
import { wrapper } from 'axios-cookiejar-support';

@Injectable()
export class ClinicIdexxService {
	private deviceUnitsCache: Map<string, { units: any[]; timestamp: number }> =
		new Map();
	private readonly DEVICE_CACHE_TTL_MS = 15 * 60 * 1000; // 15 minutes cache time

	constructor(
		private readonly httpService: HttpService,
		private readonly patientService: PatientsService,
		private readonly s3Service: S3Service,
		private readonly logger: WinstonLogger,
		private readonly appointmentsService: AppointmentsService,
		private readonly clinisIdexxUtilsService: ClinicIdexxUtilsService,
		private readonly clinicLabReportService: ClinicLabReportService,
		@InjectRepository(ClinicLabReport)
		private readonly clinicLabReportRepository: Repository<ClinicLabReport>,
		@InjectRepository(CreateClinicIdexxEntity)
		private readonly clinicIdexxRepository: Repository<CreateClinicIdexxEntity>,
		@InjectRepository(LabReport)
		private readonly labReportRepository: Repository<LabReport>,
		@InjectRepository(AppointmentEntity)
		private appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentDetailsEntity)
		private appointmentDetailsRepository: Repository<AppointmentDetailsEntity>
	) {}

	async createIdexxEntry(
		createClinicIdexxDto: CreateClinicIdexxDto,
		brandId: string
	) {
		try {
			this.logger.log(
				'info',
				`Creating IDEXX entry for clinic ${createClinicIdexxDto.clinicId}`
			);

			const idexxItem = await this.clinicIdexxRepository.findOne({
				where: {
					clinicId: createClinicIdexxDto.clinicId
				}
			});

			// if (idexxItem) {
			// 	this.logger.log(
			// 		`Conflict: IDEXX entry with clinic ID ${idexxItem.clinicId} already exists`
			// 	);
			// 	throw new ConflictException(
			// 		`The idexx entry with clinic ${idexxItem.clinicId} already exists`
			// 	);
			// }

			const base64Auth = this.clinisIdexxUtilsService.getAuthKey(
				createClinicIdexxDto.userName,
				createClinicIdexxDto.password
			);

			const updatedData = {
				...createClinicIdexxDto,
				authKey: base64Auth
			};

			// this.logger.log(
			// 	'debug',
			// 	`Saving new IDEXX entry: ${JSON.stringify(updatedData)}`
			// );
			// const savedEntry = this.clinicIdexxRepository.save(updatedData);
			// this.logger.log(
			// 	'info',
			// 	`IDEXX entry created successfully for clinic ${createClinicIdexxDto.clinicId}`
			// );
			// return savedEntry;
			let savedEntry;
			if (idexxItem) {
				this.logger.log(
					'debug',
					`Updating existing IDEXX entry for clinic ${createClinicIdexxDto.clinicId}`
				);
				savedEntry = await this.clinicIdexxRepository.save({
					...idexxItem,
					...updatedData,
					brandId: brandId
				});
				this.logger.log(
					'info',
					`IDEXX entry updated successfully for clinic ${createClinicIdexxDto.clinicId}`
				);
			} else {
				this.logger.log(
					'debug',
					`Saving new IDEXX entry for clinic ${createClinicIdexxDto.clinicId}`
				);
				savedEntry = await this.clinicIdexxRepository.save({
					...updatedData,
					brandId: brandId
				});
				this.logger.log(
					'info',
					`IDEXX entry created successfully for clinic ${createClinicIdexxDto.clinicId}`
				);
			}

			return savedEntry;
		} catch (error) {
			this.logger.error('Unhandled error in createIdexxEntry', {
				error,
				clinicId: createClinicIdexxDto.clinicId
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async getIdexxEntries(clinicId: string) {
		try {
			this.logger.log(
				'info',
				`Fetching IDEXX entries for clinic ${clinicId}`
			);

			const [items, total] =
				await this.clinicIdexxRepository.findAndCount({
					where: {
						clinicId
					}
				});

			this.logger.log(
				'info',
				`Found ${total} IDEXX entries for clinic ${clinicId}`
			);
			return { items, total };
		} catch (error) {
			this.logger.error('Unhandled error in getIdexxEntries', {
				error,
				clinicId: clinicId
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async deletIdexxEntry(clinicIdexxId: string) {
		try {
			this.logger.log(
				'info',
				`Deleting IDEXX entry with ID ${clinicIdexxId}`
			);

			const item = await this.clinicIdexxRepository.findOne({
				where: { id: clinicIdexxId }
			});
			if (!item) {
				this.logger.log(
					`NotFound: IDEXX entry with ID ${clinicIdexxId} does not exist`
				);

				throw new NotFoundException(
					`This entry with ${clinicIdexxId} doesn't exist`
				);
			}

			const result = await this.clinicIdexxRepository.delete({
				id: clinicIdexxId
			});

			const affectedRows = result.affected ?? 0;

			if (result.affected === 0) {
				this.logger.log(
					`Delete operation found no records to delete for ID ${clinicIdexxId}`
				);

				// No rows were deleted
				return {
					status: false,
					message: 'No records found to delete.'
				};
			} else if (affectedRows > 1) {
				this.logger.error(
					`Unexpected: Multiple records deleted for ID ${clinicIdexxId}`
				);

				// More than one row was deleted (unlikely but possible)
				return {
					status: false,
					message: 'Multiple records deleted. Check your query.'
				};
			} else {
				this.logger.log(
					'info',
					`IDEXX entry with ID ${clinicIdexxId} deleted successfully`
				);

				// Exactly one row was deleted
				return { status: true };
			}
		} catch (error) {
			this.logger.error('Unhandled error in deletIdexxEntry', {
				error,
				clinicIdexxId: clinicIdexxId
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async getAllIDexxTestsList(clinicId: string) {
		try {
			this.logger.log(
				'info',
				`Fetching all IDEXX tests list for clinic ID ${clinicId}`
			);

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/'; //'https://integration.vetconnectplus.com/api/v1/';
			const apiUrl = baseIDEXXURL + 'ref/tests';

			this.logger.log('debug', `Constructed API URL: ${apiUrl}`);

			// Get autn key based on clinic id
			const clinicIdexxDetails = await this.clinicIdexxRepository.findOne(
				{
					where: {
						clinicId
					}
				}
			);

			this.logger.log(
				'info',
				`ClinicIDexx details are ${clinicIdexxDetails}`
			);

			if (!clinicIdexxDetails) {
				this.logger.log(
					`NotFound: No IDEXX entry found for clinic ID ${clinicId}`
				);

				throw new NotFoundException(
					`This entry with ${clinicId} doesn't exist`
				);
			}

			const authKey = clinicIdexxDetails.authKey;

			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			const config: AxiosRequestConfig = {
				headers: headers
			};

			this.logger.log(
				'debug',
				`Request headers set for API call to IDEXX`
			);

			const response = await firstValueFrom(
				this.httpService.get(apiUrl, config)
			);
			const testList =
				response.data.list && response.data.list.length > 0
					? response.data.list
					: [];

			this.logger.log(
				'info',
				`Received ${testList.length} IDEXX tests for clinic ID ${clinicId}`
			);

			return testList;
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.error(
					`Failed to fetch IDEXX tests for clinic ID ${clinicId}: ${error.message}`
				);
			} else {
				// Handle unexpected error type
				this.logger.error(
					`Failed to fetch IDEXX tests for clinic ID ${clinicId}: Unknown error`
				);
			}

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async createIdexxTestItem(
		createClinicIdexxTestItemDto: CreateClinicIdexxTestItemDto,
		brandId: string
	) {
		this.logger.log('info', `Creating a new IDEXX test item`);

		try {
			const savedItem = await this.clinicLabReportRepository.save({
				...createClinicIdexxTestItemDto,
				brandId: brandId
			});

			this.logger.log(
				'info',
				`IDEXX test item created with ID ${savedItem.id}`
			);
			return savedItem;
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.error(
					`Failed to create IDEXX test item: ${error.message}`
				);
			} else {
				// Handle unexpected error type
				this.logger.error(
					`Failed to create IDEXX test item: Unknown error`
				);
			}

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async deleteIdexxTestItem(clinicLabReportEntryId: string) {
		try {
			this.logger.log(
				'info',
				`Deleting IDEXX test item with ID ${clinicLabReportEntryId}`
			);

			const item = await this.clinicLabReportRepository.findOne({
				where: { id: clinicLabReportEntryId }
			});

			if (!item) {
				this.logger.log(
					`NotFound: No IDEXX test item found with ID ${clinicLabReportEntryId}`
				);

				throw new NotFoundException(
					`This entry with ${clinicLabReportEntryId} doesn't exist`
				);
			}

			const result = await this.clinicLabReportRepository.delete({
				id: clinicLabReportEntryId
			});

			const affectedRows = result.affected ?? 0;

			if (result.affected === 0) {
				this.logger.log(
					`Delete operation found no records to delete for ID ${clinicLabReportEntryId}`
				);

				// No rows were deleted
				return {
					status: false,
					message: 'No records found to delete.'
				};
			} else if (affectedRows > 1) {
				this.logger.error(
					`Unexpected: Multiple records deleted for ID ${clinicLabReportEntryId}`
				);

				// More than one row was deleted (unlikely but possible)
				return {
					status: false,
					message: 'Multiple records deleted. Check your query.'
				};
			} else {
				this.logger.log(
					'info',
					`IDEXX test item with ID ${clinicLabReportEntryId} deleted successfully`
				);

				// Exactly one row was deleted
				return { status: true };
			}
		} catch (error) {
			this.logger.error('Unhandled error in clinicLabReportEntryId', {
				error,
				clinicLabReportEntryId: clinicLabReportEntryId
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async createIdexxOrder(
		createIdexxOrderDto: CreateIdexxOrderDto,
		brandId: string
	) {
		try {
			this.logger.log(
				'info',
				`[IDEXX-START] Creating new IDEXX order for clinic ID ${createIdexxOrderDto.clinicId}`
			);

			const startTime = Date.now();

			// Parallelize initial database and setup queries
			const [appointmentDetails, patientDetail, clinicIdexxDetails] =
				await Promise.all([
					this.appointmentsService.getAppointmentDetails(
						createIdexxOrderDto.appointmentId
					),
					this.patientService.getPatientDetails(
						createIdexxOrderDto.patientId
					),
					this.clinicIdexxRepository.findOne({
						where: {
							clinicId: createIdexxOrderDto.clinicId
						}
					})
				]);

			// Check that we have the IDEXX credentials
			if (!clinicIdexxDetails) {
				this.logger.error(
					`[IDEXX-ERROR] No IDEXX details found for clinic ID ${createIdexxOrderDto.clinicId}`
				);
				throw new NotFoundException(
					`This entry with ${createIdexxOrderDto.clinicId} doesn't exist`
				);
			}

			// Get device units (needs auth key from clinicIdexxDetails) - WITH CACHING
			const authKey = clinicIdexxDetails.authKey;
			let deviceUnitSelected = '';

			// Check if we have a valid cache entry for device units
			const cacheKey = createIdexxOrderDto.clinicId;
			const cachedDeviceUnits = this.deviceUnitsCache.get(cacheKey);
			let responseDeviceUnits;

			if (
				cachedDeviceUnits &&
				Date.now() - cachedDeviceUnits.timestamp <
					this.DEVICE_CACHE_TTL_MS
			) {
				// Use cached device units
				responseDeviceUnits = cachedDeviceUnits.units;
				this.logger.log(
					'info',
					`[IDEXX-CACHE] Using cached device units for clinic ${createIdexxOrderDto.clinicId}`
				);
			} else {
				// Fetch device units from IDEXX API
				responseDeviceUnits = await this.getAllIDEXXDeviceUnits(
					createIdexxOrderDto.clinicId
				);

				// Cache the device units
				this.deviceUnitsCache.set(cacheKey, {
					units: responseDeviceUnits,
					timestamp: Date.now()
				});
				this.logger.log(
					'info',
					`[IDEXX-CACHE] Cached device units for clinic ${createIdexxOrderDto.clinicId}`
				);
			}

			if (responseDeviceUnits.length > 0) {
				deviceUnitSelected = responseDeviceUnits[0].deviceSerialNumber;
			}

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/';
			const apiUrl = baseIDEXXURL + 'order';

			// Get doctor name from appointment or use default "Doctor"
			const doctorName = appointmentDetails.appointmentDoctors?.find(
				doc => doc.primary
			)
				? `${appointmentDetails.appointmentDoctors.find(doc => doc.primary)?.doctor.firstName} ${appointmentDetails.appointmentDoctors.find(doc => doc.primary)?.doctor.lastName}`
				: 'Doctor';

			this.logger.log(
				'info',
				`[IDEXX-SETUP] Preparing IDEXX order for patient: ${patientDetail.patientName}, device: ${deviceUnitSelected}, doctor: ${doctorName}`
			);

			if (deviceUnitSelected.length > 0) {
				// Properly calculate age in years and months from birthdate for later use
				const birthDateString = patientDetail?.age
					? this.clinisIdexxUtilsService.getBirthDate(
							patientDetail?.age as string
						)
					: moment().format('YYYY-MM-DD');
				const birthDateMoment = moment(birthDateString, 'YYYY-MM-DD');
				const nowMoment = moment();
				const ageYearsCalc = Math.max(
					0,
					nowMoment.diff(birthDateMoment, 'years')
				);
				const ageMonthsCalc = Math.max(
					0,
					nowMoment.diff(birthDateMoment, 'months') % 12
				);

				// Format dates using the appropriate format for the current API
				const formattedBirthdateForPut =
					this.clinisIdexxUtilsService.formatDateForApi(
						birthDateString
					);
				const currentDateForPut =
					this.clinisIdexxUtilsService.formatDateForApi(
						moment().format('YYYY-MM-DD')
					);

				// Original payload for initial POST request
				const body = {
					patients: [
						{
							veterinarian: doctorName,
							name: patientDetail.patientName,
							patientId: createIdexxOrderDto.patientId,
							speciesCode: patientDetail.species?.toUpperCase(),
							genderCode: this.clinisIdexxUtilsService.getGender(
								patientDetail.gender as
									| 'Male'
									| 'Female'
									| 'Unknown'
							),
							birthdate: patientDetail?.age
								? this.clinisIdexxUtilsService.getBirthDate(
										patientDetail?.age as string
									)
								: moment().format('YYYY-MM-DD'),
							microchip: patientDetail.microchipId,
							client: {
								id: '',
								lastName:
									patientDetail?.patientOwners[0]?.ownerBrand
										.lastName,
								firstName:
									patientDetail?.patientOwners[0]?.ownerBrand
										.firstName
							}
						}
					],
					tests: [createIdexxOrderDto.integrationCode],
					ivls: [
						{
							serialNumber: deviceUnitSelected
						}
					]
				};

				const headers =
					this.clinisIdexxUtilsService.getHeaders(authKey);

				// Step 1: Initial IDEXX Order Creation (POST)
				this.logger.log(
					'info',
					`[IDEXX-API-CALL] Creating initial IDEXX order via API`
				);

				const response = await firstValueFrom(
					this.httpService.post(apiUrl, body, { headers })
				);

				this.logger.log(
					'info',
					`[IDEXX-RESPONSE] Initial API response status: ${response.status}`
				);

				if (response?.data?.idexxOrderId) {
					const idexxOrderId = response?.data?.idexxOrderId;
					const originalUiURL = response?.data?.uiURL;

					this.logger.log(
						'info',
						`[IDEXX-ORDER-CREATED] IDEXX order created with ID ${idexxOrderId}, proceeding to automation`
					);

					// Extract the token from the UI URL (needed for subsequent requests)
					const tokenMatch = originalUiURL.match(/token=([^&]+)/);
					const token = tokenMatch ? tokenMatch[1] : null;

					if (!token) {
						this.logger.error(
							`[IDEXX-TOKEN-ERROR] Failed to extract token from UI URL: ${originalUiURL}`
						);

						// Store the lab report and return the UI URL for manual completion
						const createLabReportDto: CreateLabReportDto = {
							appointmentId: createIdexxOrderDto.appointmentId,
							clinicLabReportId:
								createIdexxOrderDto.clinicLabReportId,
							clinicId: createIdexxOrderDto.clinicId,
							files: [],
							status: 'PENDING',
							integrationDetails: {
								orderId: idexxOrderId,
								uiURL: originalUiURL,
								automationError:
									'Failed to extract token from UI URL',
								requiresManualCompletion: true
							},
							patientId: createIdexxOrderDto.patientId,
							integrationOrderId: idexxOrderId,
							lineItemId: createIdexxOrderDto.lineItemId
						};

						const labReport =
							await this.clinicLabReportService.createOrUpdateLabReport(
								createLabReportDto,
								brandId
							);

						return {
							orderId: idexxOrderId,
							uiURL: originalUiURL,
							labReportId: labReport?.id,
							automationFailed: true,
							message:
								'Unable to automate IDEXX order completion. Please complete manually via the popup.'
						};
					}

					// Create a cookie jar for handling cookies
					const jar = new CookieJar();

					// Create a new axios instance with cookie jar support
					const axiosInstance = wrapper(axios.create({ jar }));

					const idexxUiBaseUrl = this.clinisIdexxUtilsService
						.getIntegrationBaseURL()
						?.replace('/api/', '');

					try {
						// Step 2: First load the UI URL to establish a session with cookies
						this.logger.log(
							'info',
							`[IDEXX-SESSION] Establishing session with IDEXX UI`
						);

						// Add headers that a browser would normally send
						const browserLikeHeaders = {
							Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
							'Accept-Language': 'en-US,en;q=0.9',
							'User-Agent':
								'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
							'Cache-Control': 'no-cache',
							Pragma: 'no-cache'
						};

						// Load the UI URL to establish a session
						const cookieResponse = await axiosInstance.get(
							originalUiURL,
							{
								headers: browserLikeHeaders
							}
						);

						this.logger.log(
							'info',
							`[IDEXX-SESSION] Session established, status: ${cookieResponse.status}`
						);

						// Try to construct our own payload without fetching order details
						const orderUrl = `${idexxUiBaseUrl}/ui/order/${idexxOrderId}`;

						// Construct the minimal payload directly
						const completeOrderPayload = {
							id: parseInt(idexxOrderId, 10),
							patients: [
								{
									patientId: createIdexxOrderDto.patientId,
									name: patientDetail.patientName,
									microchip:
										patientDetail.microchipId || null,
									client: {
										id: null,
										firstName:
											patientDetail?.patientOwners[0]
												?.ownerBrand.firstName || '',
										lastName:
											patientDetail?.patientOwners[0]
												?.ownerBrand.lastName || '',
										address: null
									},
									kennelClub: null,
									species: patientDetail.species || 'Canine',
									speciesCode: (
										patientDetail.species || 'Canine'
									).toUpperCase(),
									breed: patientDetail.breed || null,
									breedCode: null,
									gender: patientDetail.gender || 'Unknown',
									speciesType: (
										patientDetail.species || 'Canine'
									).toUpperCase(),
									ageYears: ageYearsCalc,
									ageMonths: ageMonthsCalc,
									birthdate: formattedBirthdateForPut
								}
							],
							practiceOrderId: null,
							followOn: false,
							localDate: currentDateForPut,
							external: false,
							vet: doctorName,
							ivls: [
								{
									serialNumber: deviceUnitSelected,
									displayName: null
								}
							],
							notes: {
								staff: null,
								collectionDate: currentDateForPut,
								urgent: null,
								clinicalDetails: '',
								fastedSample: null,
								idexxAdvantage: null,
								prevRefNum: null,
								patientTravel: null,
								zoonoticPathogen: null
							},
							tests: [
								{
									code: createIdexxOrderDto.integrationCode,
									sd: []
								}
							]
						};

						// Step 3: Submit the final order
						this.logger.log(
							'info',
							`[IDEXX-COMPLETE] Submitting order completion request`
						);

						const completeOrderHeaders = {
							Accept: 'application/json, text/plain, */*',
							'Content-Type': 'application/json',
							'User-Agent':
								'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
							Referer: originalUiURL,
							'X-Requested-With': 'XMLHttpRequest'
						};

						// Add the token as a query parameter if needed based on API version
						const completeUrl =
							this.clinisIdexxUtilsService.getUrlWithToken(
								orderUrl,
								token
							);
						const completeResponse = await axiosInstance.put(
							completeUrl,
							completeOrderPayload,
							{
								headers: completeOrderHeaders
							}
						);

						this.logger.log(
							'info',
							`[IDEXX-SUCCESS] Successfully completed IDEXX order ${idexxOrderId}, status: ${completeResponse.status}`
						);

						// Store this info in the lab report table - the order is now fully completed
						const createLabReportDto: CreateLabReportDto = {
							appointmentId: createIdexxOrderDto.appointmentId,
							clinicLabReportId:
								createIdexxOrderDto.clinicLabReportId,
							clinicId: createIdexxOrderDto.clinicId,
							files: [],
							status: 'PENDING', // This will get updated when results come in
							integrationDetails: {
								orderId: idexxOrderId,
								automated: true,
								selectedVet: doctorName // Store the appointment doctor name
							},
							patientId: createIdexxOrderDto.patientId,
							integrationOrderId: idexxOrderId,
							lineItemId: createIdexxOrderDto.lineItemId
						};

						const labReport =
							await this.clinicLabReportService.createOrUpdateLabReport(
								createLabReportDto,
								brandId
							);

						const totalTime = Date.now() - startTime;
						this.logger.log(
							'info',
							`[IDEXX-API-TIMING] Total IDEXX order process took ${totalTime}ms`
						);

						// Return success without the uiURL since we've automated the process
						return {
							orderId: idexxOrderId,
							labReportId: labReport?.id,
							message:
								'IDEXX order created and automatically completed',
							selectedVet: doctorName // Return the appointment doctor name
						};
					} catch (automationError: any) {
						// If automation fails, fall back to manual process by returning the uiURL
						this.logger.error(
							`[IDEXX-AUTOMATION-ERROR] Error during automated IDEXX order completion for order ${idexxOrderId}:`,
							{
								error: automationError.message,
								status: automationError.response?.status
							}
						);

						// Still create the lab report with a special status indicating manual completion needed
						const createLabReportDto: CreateLabReportDto = {
							appointmentId: createIdexxOrderDto.appointmentId,
							clinicLabReportId:
								createIdexxOrderDto.clinicLabReportId,
							clinicId: createIdexxOrderDto.clinicId,
							files: [],
							status: 'PENDING',
							integrationDetails: {
								orderId: idexxOrderId,
								uiURL: originalUiURL,
								automationError: automationError.message,
								requiresManualCompletion: true
							},
							patientId: createIdexxOrderDto.patientId,
							integrationOrderId: idexxOrderId,
							lineItemId: createIdexxOrderDto.lineItemId
						};

						const labReport =
							await this.clinicLabReportService.createOrUpdateLabReport(
								createLabReportDto,
								brandId
							);

						const totalTime = Date.now() - startTime;
						this.logger.log(
							'info',
							`[IDEXX-API-TIMING] Failed IDEXX order process took ${totalTime}ms`
						);

						// If we tried direct construction but it failed, try with fetching order details as a fallback
						if (automationError.response?.status === 400) {
							this.logger.log(
								'info',
								`[IDEXX-FALLBACK] Direct payload construction failed, trying with order details fetch`
							);

							try {
								// Fetch order details as a fallback
								const orderHeaders = {
									Accept: 'application/json, text/plain, */*',
									'Content-Type': 'application/json',
									'User-Agent':
										'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
									Referer: originalUiURL,
									'X-Requested-With': 'XMLHttpRequest'
								};

								// Add the token to the query string if needed based on API version
								const orderUrl = `${idexxUiBaseUrl}/ui/order/${idexxOrderId}`;
								const orderGetUrl =
									this.clinisIdexxUtilsService.getUrlWithToken(
										orderUrl,
										token
									);
								const orderResponse = await axiosInstance.get(
									orderGetUrl,
									{
										headers: orderHeaders
									}
								);

								const currentOrderData = orderResponse.data;

								// Prepare the payload with the fetched order details
								const fallbackOrderPayload = {
									id: parseInt(idexxOrderId, 10), // Ensure ID is in the expected format
									patients: currentOrderData.patients.map(
										(p: any) => ({
											patientId: p.patientId,
											name: p.name,
											microchip: p.microchip || null,
											client: {
												id: p.client?.id || null,
												firstName: p.client?.firstName,
												lastName: p.client?.lastName,
												address:
													p.client?.address || null
											},
											kennelClub: p.kennelClub || null,
											species: p.species,
											speciesCode: p.speciesCode,
											breed: p.breed || null,
											breedCode: p.breedCode || null,
											gender: p.gender,
											speciesType: p.speciesType,
											ageYears: p.ageYears,
											ageMonths: p.ageMonths,
											// Use the birthdate directly from IDEXX's response as it's already in the correct format
											birthdate: p.birthdate
										})
									),
									practiceOrderId:
										currentOrderData.practiceOrderId ||
										null,
									followOn:
										currentOrderData.followOn || false,
									localDate:
										currentOrderData.localDate ||
										this.clinisIdexxUtilsService.formatDateForApi(
											moment().format('YYYY-MM-DD')
										),
									external:
										currentOrderData.external || false,
									vet: doctorName, // Use appointment doctor directly
									ivls: currentOrderData.ivls,
									notes: {
										staff:
											currentOrderData.notes?.staff ||
											null,
										// Use collection date directly or format current date
										collectionDate:
											currentOrderData.notes
												?.collectionDate ||
											this.clinisIdexxUtilsService.formatDateForApi(
												moment().format('YYYY-MM-DD')
											),
										urgent:
											currentOrderData.notes?.urgent ||
											null,
										// Ensure clinicalDetails is empty string instead of null
										clinicalDetails:
											currentOrderData.notes
												?.clinicalDetails || '',
										fastedSample:
											currentOrderData.notes
												?.fastedSample || null,
										idexxAdvantage:
											currentOrderData.notes
												?.idexxAdvantage || null,
										prevRefNum:
											currentOrderData.notes
												?.prevRefNum || null,
										patientTravel:
											currentOrderData.notes
												?.patientTravel || null,
										zoonoticPathogen:
											currentOrderData.notes
												?.zoonoticPathogen || null
									},
									tests: currentOrderData.tests.map(
										(test: any) => ({
											code: test.code, // Only include the test code
											sd: test.sd || [] // And any sample data (sd)
										})
									)
								};

								// Try the fallback completion with token if needed
								const fallbackPutUrl =
									this.clinisIdexxUtilsService.getUrlWithToken(
										orderUrl,
										token
									);
								await axiosInstance.put(
									fallbackPutUrl,
									fallbackOrderPayload,
									{
										headers: orderHeaders // Use orderHeaders instead of completeOrderHeaders
									}
								);

								this.logger.log(
									'info',
									`[IDEXX-FALLBACK-SUCCESS] Successfully completed IDEXX order with fallback method`
								);

								// Update the lab report to remove the automation error flag
								if (labReport.integrationOrderId) {
									await this.clinicLabReportService.updateItemByIdexxOrderId(
										labReport.integrationOrderId,
										{
											integrationDetails: {
												orderId: idexxOrderId,
												automated: true,
												selectedVet: doctorName
											}
										}
									);
								}

								return {
									orderId: idexxOrderId,
									labReportId: labReport?.id,
									message:
										'IDEXX order created and automatically completed (with fallback)',
									selectedVet: doctorName
								};
							} catch (fallbackError: any) {
								this.logger.error(
									`[IDEXX-FALLBACK-ERROR] Fallback automation also failed: ${fallbackError.message}`
								);
							}
						}

						// Return the original UI URL so the frontend can still open the popup for manual completion
						return {
							orderId: idexxOrderId,
							uiURL: originalUiURL, // Include the UI URL for fallback manual completion
							labReportId: labReport?.id,
							message:
								'IDEXX order created but automatic completion failed. Please complete manually via the popup.',
							automationFailed: true
						};
					}
				} else {
					this.logger.error(
						`[IDEXX-ERROR] Failed to create IDEXX order for clinic ID ${createIdexxOrderDto.clinicId}`
					);
				}
			}
		} catch (error: any) {
			// Check if the error has response data (likely an Axios error from HttpService)
			if (error.response) {
				this.logger.error(
					`[IDEXX-ERROR] Error creating IDEXX order: Status ${error.response.status}`,
					{
						errorData: error.response.data,
						clinicId: createIdexxOrderDto.clinicId,
						patientId: createIdexxOrderDto.patientId
					}
				);
				// Re-throw an HttpException with the status and data from the IDEXX API response
				throw new HttpException(
					{
						message: error.response.data || 'IDEXX API Error',
						errorDetails: {
							status: error.response.status,
							data: error.response.data,
							source: 'IDEXX API'
						}
					},
					error.response.status || HttpStatus.BAD_GATEWAY // Use response status, or a fallback
				);
			} else if (error instanceof Error) {
				// Handle other types of errors (e.g., network issues, code errors)
				this.logger.error(
					`[IDEXX-ERROR] Error creating IDEXX order: ${error.message}`,
					{
						error,
						clinicId: createIdexxOrderDto.clinicId,
						patientId: createIdexxOrderDto.patientId
					}
				);
				// Throw a generic server error for non-API related issues
				throw new HttpException(
					{
						message: error.message,
						errorDetails: {
							name: error.name,
							source: 'Server'
						}
					},
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			} else {
				// Handle unexpected non-Error types
				this.logger.error(
					`[IDEXX-ERROR] Error creating IDEXX order: Unknown error`,
					{
						clinicId: createIdexxOrderDto.clinicId,
						patientId: createIdexxOrderDto.patientId
					}
				);
				throw new HttpException(
					{
						message:
							'An unknown error occurred while creating the IDEXX order.',
						errorDetails: {
							source: 'Unknown'
						}
					},
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		}

		return {};
	}

	async cancelIdexxOrder(clinicId: string, idexxOrderId: string) {
		try {
			this.logger.log(
				'info',
				`Cancelling IDEXX order with ID ${idexxOrderId} for clinic ID ${clinicId}`
			);

			console.log(
				'info',
				`Cancelling IDEXX order with ID ${idexxOrderId} for clinic ID ${clinicId}`
			);

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/'; //'https://integration.vetconnectplus.com/api/v1/';
			const apiUrl = baseIDEXXURL + 'order' + '/' + idexxOrderId;
			this.logger.log('info', `API url for cancel order ${apiUrl}`);

			// Get autn key based on clinic id
			const clinicIdexxDetails = await this.clinicIdexxRepository.findOne(
				{
					where: {
						clinicId
					}
				}
			);

			if (!clinicIdexxDetails) {
				this.logger.log(
					`No IDEXX details found for clinic ID ${clinicId}`
				);

				throw new NotFoundException(
					`This entry with ${clinicId} doesn't exist`
				);
			}

			const authKey = clinicIdexxDetails.authKey;

			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			const config: AxiosRequestConfig = {
				headers: headers,
				timeout: 10000
			};

			const response = await firstValueFrom(
				this.httpService.delete(apiUrl, config)
			);

			// Log only essential information
			this.logger.log('IDEXX order cancellation response', {
				status: response.status,
				statusText: response.statusText,
				orderId: idexxOrderId
			});

			return response.data;
		} catch (error: any) {
			// Log only essential error information
			this.logger.error('Error cancelling IDEXX order', {
				orderId: idexxOrderId,
				errorMessage: error?.message,
				errorStatus: error?.response?.status,
				errorData: error?.response?.data
			});

			// Check if the error has response data (likely an Axios error from HttpService)
			if (error.response) {
				throw new HttpException(
					{
						message: error.response.data || 'IDEXX API Error',
						errorDetails: {
							status: error.response.status,
							data: error.response.data,
							source: 'IDEXX API',
							orderId: idexxOrderId
						}
					},
					error.response.status || HttpStatus.BAD_GATEWAY
				);
			} else if (error instanceof Error) {
				throw new HttpException(
					{
						message: error.message,
						errorDetails: {
							name: error.name,
							stack: error.stack,
							source: 'Server',
							orderId: idexxOrderId
						}
					},
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			} else {
				throw new HttpException(
					{
						message:
							'An unknown error occurred while cancelling the IDEXX order.',
						errorDetails: {
							error: error,
							source: 'Unknown',
							orderId: idexxOrderId
						}
					},
					HttpStatus.INTERNAL_SERVER_ERROR
				);
			}
		}
	}

	@Cron('0 * * * *') // Run every 1 hour
	async runGetResults() {
		this.logger.log('runGetResults started.');
		console.log('runGetResults started.');
		try {
			const idexxLabReports = await this.clinicLabReportService
				.getIdexxLabReports()
				.catch(error => {
					this.logger.error('Failed to fetch IDEXX lab reports', {
						error: error.message,
						stack: error.stack
					});
					throw new HttpException(
						(error as Error).message,
						HttpStatus.BAD_REQUEST
					);
				});

			if (!idexxLabReports || idexxLabReports.length === 0) {
				this.logger.log('No pending lab reports found');
				return;
			}
			// console.log('idexx reports', idexxLabReports.length);
			this.logger.log(
				'Pending lab reports length',
				idexxLabReports.length
			);
			const reportsByClinic = new Map<string, LabReport[]>();
			this.logger.log('reports by clinic length', reportsByClinic.size);
			if (idexxLabReports.length > 0) {
				// Step 1: Group lab reports by clinic

				try {
					idexxLabReports.forEach(labReport => {
						if (!labReport.clinic?.id) {
							this.logger.log(
								'Invalid clinic data in lab report',
								{ labReportId: labReport.id }
							);
							return;
						}
						this.logger.log('LabReport inisde idexx', labReport);
						const clinicId = labReport.clinic.id;
						this.logger.log('ClinicId inisde idexx', clinicId);

						this.logger.log(
							'Reports by Clinic inside map',
							reportsByClinic.has(clinicId)
						);
						if (!reportsByClinic.has(clinicId)) {
							reportsByClinic.set(clinicId, []);
						}

						this.logger.log('reports by clinic after setting', {
							size: reportsByClinic.size,
							keys: Array.from(reportsByClinic.keys()),
							entries: Array.from(reportsByClinic.entries()).map(
								([key, value]) => ({
									clinicId: key,
									reportsCount: value.length
								})
							)
						});
						reportsByClinic.get(clinicId)!.push(labReport);
					});

					this.logger.log('Final map size', reportsByClinic.size);
					this.logger.log('Reports segregated per clinic', {
						size: reportsByClinic.size
						// clinics: Object.fromEntries(reportsByClinic)
					});
				} catch (error) {
					this.logger.error('Error grouping reports by clinic', {
						error
					});
					throw new HttpException(
						(error as Error).message,
						HttpStatus.BAD_REQUEST
					);
				}

				for (const [clinicId, clinicReports] of reportsByClinic) {
					const clinic = clinicReports[0].clinic;

					// Check if clinic has idexx integration
					if (!clinic.idexx || clinic.idexx.length === 0) continue;

					const authKey = clinic.idexx[0].authKey;
					let idexxApiResults;

					try {
						idexxApiResults =
							await this.getLatestIDEXXResults(authKey);
						console.log('idexxApi', idexxApiResults);

						this.logger.log('Fetched latest results', {
							count: idexxApiResults?.count,
							batchId: idexxApiResults?.batchId
						});
					} catch (error) {
						this.logger.error(
							'Error fetching results from Idexx API',
							{ clinicId, error }
						);
						continue;
					}

					const resultData = idexxApiResults?.results;
					if (resultData && resultData.length > 0) {
						this.logger.log('Processing results', {
							count: resultData.length
						});

						for (const labReport of clinicReports) {
							for (const item of resultData) {
								const resultId = item.resultId;
								const idexxOrderId = item.requisitionId;

								if (
									labReport.integrationOrderId !==
									idexxOrderId
								)
									continue;

								this.logger.log('Processing result item', {
									resultId,
									idexxOrderId
								});

								try {
									// Step 3: Download the PDF and upload to S3
									const s3Result =
										await this.downloadPDFanduploadToS3(
											resultId,
											authKey
										);
									this.logger.log('PDF uploaded to S3', {
										fileKey: s3Result.fileKey
									});

									if (s3Result.fileKey.length > 0) {
										// Update the lab report in the database
										await this.clinicLabReportService.updateItemByIdexxOrderId(
											idexxOrderId,
											{
												fileKey: s3Result.fileKey,
												fileName: s3Result.fileName,
												status: idexxApiResults?.hasMoreResults
													? 'PENDING'
													: 'COMPLETED'
											}
										);
										this.logger.log('Updated lab report', {
											idexxOrderId
										});
									}
								} catch (error) {
									if (error instanceof Error) {
										this.logger.error(
											'Error processing result item',
											{ resultId, error: error.message }
										);
									} else {
										this.logger.error(
											'Error processing result item: Unknown error'
										);
									}
								}
							}
						}
						//Acknoledge items
						// If there are more results, take the batch id and acknowledge

						this.logger.log('Acknowledging results', {
							batchId: idexxApiResults.batchId
						});

						this.acknowledgeResult(
							idexxApiResults.batchId,
							authKey
						);

						if (idexxApiResults?.hasMoreResults === true) {
							console.log(
								'More results available, recursively calling runGetResults.'
							);
							this.logger.log(
								'More results available, recursively calling runGetResults.'
							);

							// this.runGetResults(); // do we need to do this as it might get convered in the next cron cycle ??
						} else {
							this.logger.log('No more results to fetch.');
						}
					}
				}
			}
		} catch (error: any) {
			console.log('Error in fetching lab reports', error);
			this.logger.error('Error in fetching Idexx reports', error);
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	// Every Hour
	@Cron('0 * * * *')
	async syncIdexxLabReports() {
		console.log('Cron Running');
		try {
			this.logger.log('Starting IDEXX lab report sync');

			// Calculate timestamp for 1 hour ago
			const oneHourAgo = new Date();
			oneHourAgo.setHours(oneHourAgo.getHours() - 1);

			// Get all completed appointments from last hour
			const completedAppointments = await this.appointmentRepository.find(
				{
					where: {
						status: EnumAppointmentStatus.Completed,
						updatedAt: MoreThan(oneHourAgo)
					}
				}
			);

			this.logger.log(
				`Found ${completedAppointments.length} completed appointments`
			);

			for (const appointment of completedAppointments) {
				try {
					// Get lab reports for this appointment
					const labReports = await this.labReportRepository.find({
						where: { appointmentId: appointment.id },
						relations: ['clinicLabReport']
					});

					for (const labReport of labReports) {
						if (
							labReport.clinicLabReport.integrationType ===
							'IDEXX'
						) {
							const files = labReport.files;

							if (files && files.length > 0) {
								let appointmentDetails =
									await this.appointmentDetailsRepository.findOne(
										{
											where: {
												appointmentId: appointment.id
											}
										}
									);

								if (!appointmentDetails) {
									appointmentDetails =
										this.appointmentDetailsRepository.create(
											{
												appointmentId: appointment.id,
												details: {}
											}
										);
								}

								const currentDetails =
									appointmentDetails.details as {
										objective?: {
											labReports?: any[];
										};
									};

								// Map files to required format
								const formattedFiles = files.map(file => ({
									id: file.id,
									s3Url: file.s3Url,
									fileKey: file.fileKey,
									fileName: file.fileName,
									fileSize: file.fileSize,
									uploadDate: new Date().toISOString()
								}));

								const updatedDetails = {
									...currentDetails,
									objective: {
										...(currentDetails.objective || {}),
										labReports:
											currentDetails.objective?.labReports?.map(
												(report: any) => {
													if (
														report.value ===
														labReport
															.clinicLabReport.id
													) {
														return {
															...report,
															files: formattedFiles,
															labReportId:
																labReport.id,
															integrationCode:
																labReport
																	.clinicLabReport
																	.integrationCode,
															integrationType:
																labReport
																	.clinicLabReport
																	.integrationType
														};
													}
													return report;
												}
											) || []
									}
								};

								appointmentDetails.details = updatedDetails;
								await this.appointmentDetailsRepository.save(
									appointmentDetails
								);

								this.logger.log(
									`Updated appointment ${appointment.id} with IDEXX lab report files`
								);
							}
						}
					}
				} catch (error) {
					this.logger.error(
						`Error processing appointment ${appointment.id}`,
						error
					);
					continue;
				}
			}

			this.logger.log('Completed IDEXX lab report sync');
		} catch (error) {
			this.logger.error('Error in IDEXX lab report sync', error);
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}
	// async runGetResults() {
	// 	this.logger.log('runGetResults started.');
	// 	console.log('runGetResults started.');
	// 	try {
	// 		const idexxLabReports =
	// 			await this.clinicLabReportService.getIdexxLabReports();
	// 		console.log(idexxLabReports);
	// 		this.logger.log('Pending lab reports', idexxLabReports);
	// 		if (idexxLabReports.length > 0) {
	// 			for (const labReport of idexxLabReports) {
	// 				console.log('Current lab report', labReport);
	// 				if (!labReport.clinic.idexx) return;
	// 				const authKey = labReport?.clinic?.idexx[0].authKey;
	// 				const idexxApiResults =
	// 					await this.getLatestIDEXXResults(authKey);
	// 				console.log('idexxApi', idexxApiResults);

	// 				this.logger.log('Fetched latest results', {
	// 					count: idexxApiResults?.count,
	// 					batchId: idexxApiResults?.batchId
	// 				});
	// 				const resultData = idexxApiResults?.results;
	// 				if (resultData?.length > 0) {
	// 					this.logger.log('Processing results', {
	// 						count: resultData.length
	// 					});

	// 					resultData.map(async (item: any) => {
	// 						const resultId = item.resultId;
	// 						const idexxOrderId = item.requisitionId;
	// 						console.log({ resultId, idexxOrderId });

	// 						this.logger.log('Processing result item', {
	// 							resultId,
	// 							idexxOrderId
	// 						});

	// 						try {
	// 							// Download the PDF and upload to S3
	// 							const s3Result =
	// 								await this.downloadPDFanduploadToS3(
	// 									resultId,
	// 									authKey
	// 								);
	// 							this.logger.log('PDF uploaded to S3', {
	// 								fileKey: s3Result.fileKey
	// 							});

	// 							if (s3Result.fileKey.length > 0) {
	// 								// Get the url and store in our database
	// 								//const tempUpdatedData =
	// 								await this.clinicLabReportService.updateItemByIdexxOrderId(
	// 									idexxOrderId,
	// 									{
	// 										fileKey: s3Result.fileKey,
	// 										fileName: s3Result.fileName
	// 									}
	// 								);
	// 								this.logger.log('Updated lab report', {
	// 									idexxOrderId
	// 								});

	// 								// If we need to handle this, then uncomment it
	// 								// if(tempUpdatedData) {
	// 								// 	const tempUpdatedFiles = await this.appointmentsService.updateFileDetailsForAppointmentId(tempUpdatedData?.appointmentId, tempUpdatedData.files, tempUpdatedData.clinicLabReportId);
	// 								// 	console.log("tempUpdatedFiles = ", tempUpdatedFiles);
	// 								// }
	// 							}
	// 						} catch (error) {
	// 							// Check if the error is an instance of Error
	// 							if (error instanceof Error) {
	// 								this.logger.error(
	// 									'Error processing result item',
	// 									{ resultId, error: error.message }
	// 								);
	// 							} else {
	// 								// Handle unexpected error type
	// 								this.logger.error(
	// 									'Error processing result item : Unknown error'
	// 								);
	// 							}
	// 						}
	// 					});
	// 				}
	// 			}
	// 		}
	// 		// if (idexxDetailsList.total > 0) {
	// 		// 	// We will see to loop later for all clinics
	// 		// 	const itemDetail = idexxDetailsList.items[0];
	// 		// 	this.logger.log('Processing itemDetail', {
	// 		// 		clinicId: itemDetail.clinicId
	// 		// 	});

	// 		// 	const result = await this.getLatestIDEXXResults(
	// 		// 		itemDetail.authKey
	// 		// 	);
	// 		// 	this.logger.log('Fetched latest results', {
	// 		// 		count: result?.count,
	// 		// 		batchId: result?.batchId
	// 		// 	});

	// 		// We take the result, update whatever needed and send a acknowlegemnt to the server
	// 		// const resultData = result?.results;
	// 		// if (resultData?.length > 0) {
	// 		// 	this.logger.log('Processing results', {
	// 		// 		count: resultData.length
	// 		// 	});

	// 		// 	resultData.map(async (item: any) => {
	// 		// 		const resultId = item.resultId;
	// 		// 		const idexxOrderId = item.requisitionId;
	// 		// 		this.logger.log('Processing result item', {
	// 		// 			resultId,
	// 		// 			idexxOrderId
	// 		// 		});

	// 		// 		try {
	// 		// 			// Download the PDF and upload to S3
	// 		// 			const s3Result =
	// 		// 				await this.downloadPDFanduploadToS3(
	// 		// 					resultId,
	// 		// 					itemDetail.authKey
	// 		// 				);
	// 		// 			this.logger.log('PDF uploaded to S3', {
	// 		// 				fileKey: s3Result.fileKey
	// 		// 			});

	// 		// 			if (s3Result.fileKey.length > 0) {
	// 		// 				// Get the url and store in our database
	// 		// 				//const tempUpdatedData =
	// 		// 				await this.clinicLabReportService.updateItemByIdexxOrderId(
	// 		// 					idexxOrderId,
	// 		// 					{
	// 		// 						fileKey: s3Result.fileKey,
	// 		// 						fileName: s3Result.fileName
	// 		// 					}
	// 		// 				);
	// 		// 				this.logger.log('Updated lab report', {
	// 		// 					idexxOrderId
	// 		// 				});

	// 		// 				// If we need to handle this, then uncomment it
	// 		// 				// if(tempUpdatedData) {
	// 		// 				// 	const tempUpdatedFiles = await this.appointmentsService.updateFileDetailsForAppointmentId(tempUpdatedData?.appointmentId, tempUpdatedData.files, tempUpdatedData.clinicLabReportId);
	// 		// 				// 	console.log("tempUpdatedFiles = ", tempUpdatedFiles);
	// 		// 				// }
	// 		// 			}
	// 		// 		} catch (error) {
	// 		// 			// Check if the error is an instance of Error
	// 		// 			if (error instanceof Error) {
	// 		// 				this.logger.error(
	// 		// 					'Error processing result item',
	// 		// 					{ resultId, error: error.message }
	// 		// 				);
	// 		// 			} else {
	// 		// 				// Handle unexpected error type
	// 		// 				this.logger.error(
	// 		// 					'Error processing result item : Unknown error'
	// 		// 				);
	// 		// 			}
	// 		// 		}
	// 		// 	});
	// 		// } else {
	// 		// 	this.logger.log('No results found to process.');
	// 		// }

	// 		/***********************************************************************/
	// 		// If there are more results, take the batch id and acknowledge
	// 		// 	if (result?.count > 0) {
	// 		// 		this.logger.log('Acknowledging results', {
	// 		// 			batchId: result.batchId
	// 		// 		});

	// 		// 		this.acknowledgeResult(result.batchId, itemDetail.authKey);

	// 		// 		if (result?.hasMoreResults == true) {
	// 		// 			this.logger.log(
	// 		// 				'More results available, recursively calling runGetResults.'
	// 		// 			);

	// 		// 			this.runGetResults();
	// 		// 		} else {
	// 		// 			this.logger.log('No more results to fetch.');
	// 		// 		}
	// 		// 	} else {
	// 		// 		this.logger.log('No batch id to acknowledge.');
	// 		// 	}
	// 		// 	/***********************************************************************/
	// 		// }
	// 	} catch (error) {
	// 		// Check if the error is an instance of Error
	// 		if (error instanceof Error) {
	// 			this.logger.error('Error in runGetResults', {
	// 				error: error.message
	// 			});
	// 		} else {
	// 			// Handle unexpected error type
	// 			this.logger.error('Error in runGetResults : Unknown error');
	// 		}
	// 	}
	// }

	private async downloadPDFanduploadToS3(resultId: string, authKey: string) {
		try {
			this.logger.log('Starting PDF download and upload', { resultId });

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getPartnerBaseURL() + 'v3/'; // 'https://partner.vetconnectplus.com//api/v3/';
			const apiUrl = baseIDEXXURL + 'results/' + resultId + '/pdf';

			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			// const response = await firstValueFrom(
			// 	this.httpService.get(apiUrl, {
			// 		responseType: 'arraybuffer',
			// 		headers: headers,
			// 		timeout: 10000
			// 	})
			// );
			const response = await this.downloadWithRetry(apiUrl, headers);

			const buffer = Buffer.from(response?.data);

			if (buffer.length === 0) {
				this.logger.log('Received empty PDF buffer', { resultId });
			}

			// Create a file
			const fileName: string = uuidv4() + '.pdf';
			const idexxFileKey: string = `idexx/${fileName}`;

			await this.s3Service.uploadPdfToS3(buffer, idexxFileKey);
			this.logger.log('PDF uploaded to S3', { fileKey: idexxFileKey });

			const tempUploadedData = {
				fileKey: idexxFileKey,
				fileName: fileName
			};

			return tempUploadedData;
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.error('Error during PDF download/upload', {
					resultId,
					error: error.message
				});
			} else {
				// Handle unexpected error type
				this.logger.error(
					'Error during PDF download/upload : Unknown error'
				);
			}
		}

		return {
			fileKey: '',
			fileName: ''
		};
	}

	private async getLatestIDEXXResults(authKey: string) {
		try {
			this.logger.log('Fetching latest IDEXX results', { authKey });

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getPartnerBaseURL() + 'v3/'; //'https://partner.vetconnectplus.com//api/v3/';
			const apiUrl = baseIDEXXURL + 'results/latest?limit=100';

			// TBD Need to get the auth key from the table

			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			const config: AxiosRequestConfig = {
				headers: headers
			};

			const response = await firstValueFrom(
				this.httpService.get(apiUrl, config)
			);

			this.logger.log('Fetched latest results successfully', {
				resultCount: response.data?.count
			});

			return response.data;
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.error('Error fetching latest IDEXX results', {
					error: error.message
				});
			} else {
				// Handle unexpected error type
				this.logger.error(
					'Error fetching latest IDEXX results : Unknown error'
				);
			}

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			); // re-throw to handle in the caller if needed
		}
	}

	private async acknowledgeResult(batchId: string, authKey: string) {
		try {
			this.logger.log('Acknowledging results', { batchId });

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getPartnerBaseURL() + 'v3/'; //'https://partner.vetconnectplus.com//api/v3/';
			const apiUrl = baseIDEXXURL + 'results/latest/confirm/' + batchId;

			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			const config: AxiosRequestConfig = {
				headers: headers
			};

			await firstValueFrom(this.httpService.post(apiUrl, null, config));

			this.logger.log('Acknowledgement successful', { batchId });

			return true;
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.warn('Warning in acknowledging results', {
					batchId
				});
			} else {
				// Handle unexpected error type
				this.logger.warn('Warning in acknowledging results : unknown');
			}

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	private async getAllIdexxEntries() {
		this.logger.log('Fetching all IDEXX entries from the database.');

		try {
			const [items, total] =
				await this.clinicIdexxRepository.findAndCount();
			this.logger.log('Fetched IDEXX entries successfully', { total });
			return { items, total };
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.error('Error fetching IDEXX entries', {
					error: error.message
				});
			} else {
				// Handle unexpected error type
				this.logger.error(
					'Error fetching IDEXX entries : Unknown error'
				);
			}

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	// Right now, this is used only in this class. But have removed private as it needs to be mocked in test clas
	async getAllIDEXXDeviceUnits(clinicId: string) {
		this.logger.log('Fetching all IDEXX device units', { clinicId });

		try {
			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/';
			const apiUrl = baseIDEXXURL + 'ivls/devices';

			// Get autn key based on clinic id
			const clinicIdexxDetails = await this.clinicIdexxRepository.findOne(
				{
					where: {
						clinicId
					}
				}
			);

			if (!clinicIdexxDetails) {
				this.logger.log('Clinic IDEXX entry not found', { clinicId });

				throw new NotFoundException(
					`This entry with ${clinicId} doesn't exist`
				);
			}

			const authKey = clinicIdexxDetails.authKey;

			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			const config: AxiosRequestConfig = {
				headers: headers
			};

			const response = await firstValueFrom(
				this.httpService.get(apiUrl, config)
			);

			const deviceList =
				response.data.ivlsDeviceList &&
				response.data.ivlsDeviceList.length > 0
					? response.data.ivlsDeviceList
					: [];

			this.logger.log('Fetched IDEXX device units successfully', {
				deviceCount: deviceList.length
			});
			return deviceList;
		} catch (error) {
			// Check if the error is an instance of Error
			if (error instanceof Error) {
				this.logger.error('Error fetching IDEXX device units', {
					clinicId,
					error: error.message
				});
			} else {
				// Handle unexpected error type
				this.logger.error(
					'Error fetching IDEXX device units : Unknown error'
				);
			}

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	async downloadWithRetry(
		url: string,
		headers: any,
		retries = 3,
		delayMs = 2000
	) {
		for (let i = 0; i < retries; i++) {
			try {
				return await firstValueFrom(
					this.httpService.get(url, {
						responseType: 'arraybuffer',
						headers: headers,
						timeout: 10000
					})
				);
			} catch (error: any) {
				if (i === retries - 1)
					throw new HttpException(
						(error as Error).message,
						HttpStatus.BAD_REQUEST
					);
				this.logger.warn(
					`Retrying download (attempt ${i + 1}/${retries}) due to error: ${error.message}`
				);
				console.log(
					`Retrying download (attempt ${i + 1}/${retries}) due to error: ${error.message}`
				);
				await delay(delayMs);
			}
		}
		return null;
	}

	/**
	 * Check if IDEXX orders can be deleted by verifying their status
	 * @param clinicId - The clinic ID
	 * @param labReportIds - Array of lab report IDs to check
	 * @returns Promise<{ canBeDeleted: boolean, details: Array<{ labReportId: string, idexxOrderId: string, status: string, canDelete: boolean }> }>
	 */
	async checkIdexxOrdersCanBeDeleted(
		clinicId: string,
		labReportIds: string[]
	): Promise<{
		canBeDeleted: boolean;
		details: Array<{
			labReportId: string;
			idexxOrderId: string | null;
			status: string | null;
			canDelete: boolean;
			error?: string;
		}>;
	}> {
		try {
			this.logger.log(
				'info',
				`Checking if IDEXX orders can be deleted for lab reports: ${labReportIds.join(', ')} in clinic ID ${clinicId}`
			);

			const baseIDEXXURL =
				this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/';

			// Get clinic IDEXX details for authentication
			const clinicIdexxDetails = await this.clinicIdexxRepository.findOne(
				{
					where: {
						clinicId
					}
				}
			);

			if (!clinicIdexxDetails) {
				this.logger.log(
					`No IDEXX details found for clinic ID ${clinicId}`
				);

				throw new NotFoundException(
					`This entry with ${clinicId} doesn't exist`
				);
			}

			const authKey = clinicIdexxDetails.authKey;
			const headers = this.clinisIdexxUtilsService.getHeaders(authKey);

			const config: AxiosRequestConfig = {
				headers: headers,
				timeout: 10000
			};

			const details: Array<{
				labReportId: string;
				idexxOrderId: string | null;
				status: string | null;
				canDelete: boolean;
				error?: string;
			}> = [];

			// Get all lab reports and their IDEXX order IDs
			for (const labReportId of labReportIds) {
				try {
					// Get the lab report to extract IDEXX order ID
					const labReport =
						await this.clinicLabReportService.findLabReportById(
							labReportId
						);

					if (!labReport) {
						details.push({
							labReportId,
							idexxOrderId: null,
							status: null,
							canDelete: false,
							error: 'Lab report not found'
						});
						continue;
					}

					const idexxOrderId = labReport.integrationOrderId;

					if (!idexxOrderId) {
						details.push({
							labReportId,
							idexxOrderId: null,
							status: null,
							canDelete: false,
							error: 'No IDEXX order ID found'
						});
						continue;
					}

					// Check the IDEXX order status using GET API
					const apiUrl = baseIDEXXURL + 'order' + '/' + idexxOrderId;

					try {
						const response = await firstValueFrom(
							this.httpService.get(apiUrl, config)
						);

						// Extract status from response.data.status.data.status
						const orderStatus = response.data?.status;

						if (!orderStatus) {
							details.push({
								labReportId,
								idexxOrderId,
								status: null,
								canDelete: false,
								error: 'Unable to determine order status'
							});
							continue;
						}

						// Check if status is "CREATED" - only these can be deleted
						const canDelete = orderStatus === 'CREATED';

						details.push({
							labReportId,
							idexxOrderId,
							status: orderStatus,
							canDelete
						});

						this.logger.log(
							'info',
							`IDEXX order ${idexxOrderId} status: ${orderStatus}, can delete: ${canDelete}`
						);
					} catch (orderError: any) {
						this.logger.error('Error checking IDEXX order status', {
							orderId: idexxOrderId,
							labReportId,
							errorMessage: orderError?.message,
							errorStatus: orderError?.response?.status
						});

						details.push({
							labReportId,
							idexxOrderId,
							status: null,
							canDelete: false,
							error: `API error: ${orderError?.response?.status || orderError?.message}`
						});
					}
				} catch (labReportError: any) {
					this.logger.error('Error processing lab report', {
						labReportId,
						errorMessage: labReportError?.message
					});

					details.push({
						labReportId,
						idexxOrderId: null,
						status: null,
						canDelete: false,
						error: `Lab report error: ${labReportError?.message}`
					});
				}
			}

			// All orders can be deleted only if every single one has status "CREATED"
			const canBeDeleted = details.every(
				detail => detail.canDelete === true
			);

			this.logger.log(
				'info',
				`Overall deletion check result: ${canBeDeleted}, checked ${details.length} lab reports`
			);

			return {
				canBeDeleted,
				details
			};
		} catch (error: any) {
			this.logger.error('Error in checkIdexxOrdersCanBeDeleted', {
				labReportIds,
				clinicId,
				errorMessage: error?.message
			});

			throw new HttpException(
				{
					message:
						error.message ||
						'Failed to check IDEXX order deletion status',
					errorDetails: {
						source: 'checkIdexxOrdersCanBeDeleted',
						labReportIds,
						clinicId
					}
				},
				error instanceof HttpException
					? error.getStatus()
					: HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
