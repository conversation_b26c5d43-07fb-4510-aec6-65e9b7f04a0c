import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DiagnosticTemplate } from './entities/diagnostic-template.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { DiagnosticTemplatesController } from './diagnostic-note.controlller';
import { DiagnosticTemplatesService } from './diagnostic-note.service';
import { RoleModule } from '../roles/role.module';
import { DiagnosticNote } from './entities/diagnostic-note.entity';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';
import { PatientsService } from '../patients/patients.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { Patient } from '../patients/entities/patient.entity';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { OwnersService } from '../owners/owners.service';
import { GlobalReminderService } from '../patient-global-reminders/global-reminders.service';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { GlobalReminderRule } from '../patient-global-reminders/entities/global-reminder-rule.entity';
import { PatientReminder } from '../patient-reminders/entities/patient-reminder.entity';
import { Brand } from '../brands/entities/brand.entity';
import { WhatsappModule } from '../utils/whatsapp-integration/whatsapp.module';
import { SESModule } from '../utils/aws/ses/ses.module';
import { PetTransferHistory } from '../owners/entities/pet-transfer-history.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { SocketModule } from '../socket/socket.module';
import { ClinicEntity } from '../clinics/entities/clinic.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			DiagnosticTemplate,
			DiagnosticNote,
			LabReport,
			ClinicLabReport,
			Patient,
			PatientOwner,
			GlobalOwner,
			OwnerBrand,
			GlobalReminderRule,
			PatientReminder,
			Brand,
			PetTransferHistory,
			PaymentDetailsEntity,
			InvoiceEntity,
			AppointmentEntity,
			AppointmentDetailsEntity,
			ClinicEntity
		]),
		RoleModule,
		WhatsappModule,
		SESModule,
		SocketModule
	],
	providers: [
		DiagnosticTemplatesService,
		WinstonLogger,
		PatientsService,
		S3Service,
		OwnersService,
		GlobalReminderService
	],
	controllers: [DiagnosticTemplatesController],
	exports: [DiagnosticTemplatesService]
})
export class DiagnosticTemplatesModule {}
