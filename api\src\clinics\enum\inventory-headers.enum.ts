export enum ConsumableHeaders {
	// UNIQUE_ID = 'Unique ID',
	PRODUCT_NAME = 'Consumable Name',
	CURRENT_STOCK = 'Current Stock',
	MINIMUM_QUANTITY = 'Minimum Quantity'
	// REORDER_VALUE = 'Re-order Value',
	// DESCRIPTION = 'Description',
	// PURCHASE_PRICE = 'Purchase Price (INR)'
}

export enum ProductHeaders {
	// UNIQUE_ID = 'Unique ID',
	PRODUCT_NAME = 'Product Name',
	// PURCHASE_PRICE = 'Purchase Price Per Unit (INR)',
	CHARGEABLE_PRICE = 'Chargeable Price Per Unit (INR)',
	TAX = 'Tax %',
	CURRENT_STOCK = 'Current Stock',
	MINIMUM_QUANTITY = 'Minimum Quantity'
	// REORDER_VALUE = 'Re-Order Value',
	// DESCRIPTION = 'Description'
}

export enum ServiceHeaders {
	// UNIQUE_ID = 'Unique ID',
	SERVICE_NAME = 'Service Name',
	CHARGEABLE_PRICE = 'Chargeable Price Per Unit (INR)',
	TAX = 'Tax %'
	// DESCRIPTION = 'Description'
}

export enum DiagnosticHeaders {
	// UNIQUE_ID = 'Unique ID',
	SERVICE_NAME = 'Diagnostic Name',
	CHARGEABLE_PRICE = 'Chargeable Price Per Unit (INR)',
	TAX = 'Tax %'
	// DESCRIPTION = 'Description'
}

export enum VaccinationHeaders {
	// UNIQUE_ID = 'Unique ID',
	PRODUCT_NAME = 'Vaccination Name',
	// PURCHASE_PRICE = 'Purchase Price Per Unit (INR)',
	CHARGEABLE_PRICE = 'Chargeable Price Per Unit (INR)',
	TAX = 'Tax %',
	CURRENT_STOCK = 'Current Stock',
	MINIMUM_QUANTITY = 'Minimum Quantity'
	// REORDER_VALUE = 'Re-Order Value',
	// BATCH = 'Batch',
	// EXPIRATION = 'Expiration',
	// DESCRIPTION = 'Description'
}

export enum MedicationHeaders {
	// UNIQUE_ID = 'Unique ID',
	MEDICATION_NAME = 'Medication Name',
	// DRUG = 'Drug',
	RESTRICTED_SUBSTANCE = 'Restricted Substance',
	// FORM = 'Form',
	// STRENGTH = 'Strength',
	// UNIT = 'Unit',
	// DOSAGE_MINIMUM = 'Dosage Minimum Per Kilogram',
	// DOSAGE_MAXIMUM = 'Dosage Maximum Per Kilogram',
	// PURCHASE_PRICE = 'Purchase Price Per Unit (INR)',
	CHARGEABLE_PRICE = 'Chargeable Price Per Unit (INR)',
	TAX = 'Tax %',
	CURRENT_STOCK = 'Current Stock',
	MINIMUM_QUANTITY = 'Minimum Quantity'
	// REORDER_VALUE = 'Re-Order Value',
	// DESCRIPTION = 'Description'
}
