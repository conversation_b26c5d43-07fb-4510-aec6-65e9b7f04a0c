import {
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    CreateDateColumn,
    UpdateDateColumn,
    PrimaryGeneratedColumn,
    ManyToOne,
    JoinColumn,
    Index
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';
import { DiagnosticTemplate } from './diagnostic-template.entity';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';

@Entity('diagnostic_notes')
@Index(['clinicId', 'labReportId'])
export class DiagnosticNote {
    @PrimaryGeneratedColumn('uuid')
    id!: string;

    @Column({ name: 'clinic_id',type:'uuid'})
    clinicId!: string;

    @ManyToOne(() => ClinicEntity)
    @JoinColumn({ name: 'clinic_id' })
    clinic!: ClinicEntity;

    @Column({ name: 'patient_id',type:'uuid' })
    patientId!: string;

    @ManyToOne(() => Patient)
    @JoinColumn({ name: 'patient_id' })
    patient!: Patient;

    @Column({name:'appointment_id',type:'uuid'})
    appointmentId!: string;

    @ManyToOne(() => AppointmentEntity)
    @JoinColumn({ name: 'appointment_id' })
    appointment!: AppointmentEntity;

    @Column({name:'lab_report_id',type:'uuid'})
    labReportId!: string;

    @ManyToOne(() => LabReport)
    @JoinColumn({ name: 'lab_report_id' })
    labReport!: LabReport;

    @Column({name:'template_id',type:'uuid'})
    templateId!: string;

    @Column({ length: 100,name:'template_name' })
    templateName!: string;

    @ManyToOne(() => DiagnosticTemplate)
    @JoinColumn({ name: 'template_id' })
    template!: DiagnosticTemplate;

    @Column({type: 'jsonb', name: 'note_data'})
    noteData: {
        notes?: string;
        values: Record<string, any>;
    } = {
        notes: '',
        values: {}
    };

    @Column({ nullable: true, name: 'created_by' })
	createdBy!: string;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'created_by' })
    creator?: User;

    @Column({ nullable: true, name: 'updated_by' })
	updatedBy!: string;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'updated_by' })
    updater?: User;

    @Column({ type: 'int', default: 1 })
    version?: number;

    @CreateDateColumn({ name: 'created_at' })
    createdAt!: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt!: Date;

    @Column({ type: 'varchar', length: 255, nullable: true, name: 'file_key' })
    fileKey?: string;

    @Column({ type: 'varchar', length: 255, nullable: true, name: 'file_name' })
    fileName?: string;

    @Column({ name: 'diagnostic_number' })
	diagnosticNumber?: string;

    @Column({name:'clinic_lab_report_id',type:'uuid'})
    clinicLabReportId?: string;

}
