import { Controller, Get, Query } from '@nestjs/common';
import { ClinicPlansService } from './clinic-plans.service';
import { ClinicPlan } from './entities/clinic-plan.entity';
import { EnumPlanType } from './enums/enum-plan-type';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@Controller('clinic-plans')
export class ClinicPlansController {
	constructor(private readonly clinicPlanService: ClinicPlansService) {}

	@Get()
	@TrackMethod('getClinicPlans-clinic-plans')
	async getClinicPlans(
		@Query('clinicId') clinicId: string,
		@Query('search') search?: string,
		@Query('exclude') exclude?: EnumPlanType
	) {
		// Parse the exclude parameter into an array of EnumPlanType
		const excludeTypes: EnumPlanType[] = exclude
			? (exclude.split(',') as EnumPlanType[])
			: [];

		return this.clinicPlanService.findAll(clinicId, search, excludeTypes);
	}
}
