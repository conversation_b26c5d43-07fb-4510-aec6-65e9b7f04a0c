import { validate } from 'class-validator';
import { CreateClinicAlertDto } from './create-clinicAlerts.dto';

describe('Create-ClinicAlertDto', () => {
	it('should validate successfully with valid data', async () => {
		const dto = new CreateClinicAlertDto();
		dto.clinicId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
		dto.alertName = 'fever';
		dto.severity = 'High';
		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should fail if alertName and severity is not passed', async () => {
		const dto = new CreateClinicAlertDto();
		dto.clinicId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
		dto.alertName = '';
		dto.severity = '';

		const errors = await validate(dto);
		expect(errors.length).toBe(2);
	});
});
