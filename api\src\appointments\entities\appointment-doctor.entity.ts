import {
	<PERSON>umn,
	CreateDate<PERSON><PERSON>umn,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>um<PERSON>,
	ManyToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicUser } from '../../clinics/entities/clinic-user.entity';
import { AppointmentEntity } from './appointment.entity';

@Entity('appointment_doctors')
export class AppointmentDoctorsEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'appointment_id' })
	appointmentId!: string;

	@Column({ type: 'uuid', name: 'clinic_user_id' })
	doctorId!: string;

	@Column({ type: 'boolean' })
	primary!: boolean;

	@Column({ type: 'uuid', name: 'created_by', nullable: true })
	createdBy?: string;

	@Column({ type: 'uuid', name: 'updated_by', nullable: true })
	updatedBy?: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@ManyToOne(() => ClinicUser, clinicUser => clinicUser.appointmentDoctors)
	@JoinColumn({ name: 'clinic_user_id' })
	clinicUser!: ClinicUser;

	@ManyToOne(
		() => AppointmentEntity,
		appointment => appointment.appointmentDoctors
	)
	@JoinColumn({ name: 'appointment_id' })
	appointment!: AppointmentEntity;
}
