import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicVaccinationsController } from './clinic-vaccinations.controller';
import { ClinicVaccinationsService } from './clinic-vaccinations.service';
import { ClinicVaccinationEntity } from './entities/clinic-vaccination.entity';
import { RoleService } from '../roles/role.service';
import { CreateVaccinationDto } from './dto/create-vaccinations.dto';
import { UpdateVaccinationDto } from './dto/update-vaccinations.dto';

describe('ClinicVaccinationsController', () => {
	let controller: ClinicVaccinationsController;
	let vaccinationService: jest.Mocked<ClinicVaccinationsService>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockClinicVaccinations = {
		getVaccinations: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		findOne: jest.fn(),
		remove: jest.fn(),
		bulkInsert: jest.fn()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicVaccinationsController],
			providers: [
				{
					provide: ClinicVaccinationsService,
					useValue: mockClinicVaccinations
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicVaccinationsController>(
			ClinicVaccinationsController
		);
		vaccinationService = module.get(ClinicVaccinationsService);
		logger = module.get(WinstonLogger);

		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('Get vaccinations for a clinic - GET - /clinic_vaccinations', () => {
		const vaccinationDataList: ClinicVaccinationEntity[] = [
			{
				id: 'vaccination_uuid_1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				productName: 'vaccination_name_A',
				chargeablePrice: 0,
				tax: 10,
				currentStock: 0,
				minimumQuantity: 0
			},
			{
				id: 'vaccination_uuid_2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_2',
				productName: 'vaccination_name_B',
				chargeablePrice: 0,
				tax: 10,
				currentStock: 0,
				minimumQuantity: 0
			}
		];

		it('should have a getAllVaccinations function', () => {
			expect(controller.getAllVaccinations).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the getAllVaccinations call', async () => {
			const error = new Error('Error fetching all the vaccinations');
			vaccinationService.getVaccinations.mockRejectedValue(error);

			await expect(
				controller.getAllVaccinations('clinicId')
			).rejects.toThrowError('Error fetching all the vaccinations');

			expect(vaccinationService.getVaccinations).toHaveBeenCalled();
			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching all the vaccinations',
				error
			);
		});

		it('should return a list of vaccinations without a search keyword', async () => {
			vaccinationService.getVaccinations.mockResolvedValue(
				vaccinationDataList
			);

			const result = await controller.getAllVaccinations('clinicId');

			expect(result).toEqual(vaccinationDataList);
			expect(vaccinationService.getVaccinations).toHaveBeenCalledWith(
				'clinicId',
				undefined
			);
			expect(logger.log).toHaveBeenCalledWith(
				'Fetching all vaccinations'
			);
		});

		it('should return a list of vaccinations for a clinic id with search keyword', async () => {
			const searchKeyword = 'vaccination_name_A';
			const clinicId = 'clinic_uuid_1';

			const filteredData = vaccinationDataList.filter(vaccination =>
				vaccination.productName.includes(searchKeyword)
			);

			vaccinationService.getVaccinations.mockResolvedValue(filteredData);
			const result = await controller.getAllVaccinations(
				clinicId,
				searchKeyword
			);

			expect(result).toEqual(filteredData);
			expect(vaccinationService.getVaccinations).toHaveBeenCalledWith(
				clinicId,
				searchKeyword
			);
		});
	});

	describe('create', () => {
		const createVaccinationDto: CreateVaccinationDto = {
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			productName: 'Test Vaccination',
			chargeablePrice: 100,
			tax: 10,
			currentStock: 50,
			minimumQuantity: 5
		};

		it('should create a new vaccination', async () => {
			const createdVaccination = {
				id: 'new_uuid',
				...createVaccinationDto,
				uniqueId: 'V_000001'
			};
			vaccinationService.create.mockResolvedValue(createdVaccination);

			const result = await controller.create(createVaccinationDto);

			expect(result).toEqual(createdVaccination);
			expect(vaccinationService.create).toHaveBeenCalledWith(
				createVaccinationDto
			);
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle creation errors', async () => {
			const error = new Error('Creation failed');
			vaccinationService.create.mockRejectedValue(error);

			await expect(
				controller.create(createVaccinationDto)
			).rejects.toThrow(
				new HttpException(
					'Error creating vaccination',
					HttpStatus.BAD_REQUEST
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating vaccination',
				{ error }
			);
		});

		it('should validate required fields', async () => {
			const invalidDto = {
				clinicId: 'clinic_uuid'
				// missing required fields
			};

			await expect(
				controller.create(invalidDto as CreateVaccinationDto)
			).rejects.toThrow();
		});
	});

	describe('update', () => {
		const updateVaccinationDto: UpdateVaccinationDto = {
			productName: 'Updated Vaccination',
			chargeablePrice: 150,
			currentStock: 75
		};

		it('should update a vaccination', async () => {
			const updatedVaccination: ClinicVaccinationEntity = {
				id: 'vaccination_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'V_000001',
				productName: 'Updated Vaccination',
				chargeablePrice: 150,
				currentStock: 75,
				tax: 10,
				minimumQuantity: 5
			};
			vaccinationService.update.mockResolvedValue(updatedVaccination);

			const result = await controller.update(
				'vaccination_uuid',
				updateVaccinationDto
			);

			expect(result).toEqual(updatedVaccination);
			expect(vaccinationService.update).toHaveBeenCalledWith(
				'vaccination_uuid',
				updateVaccinationDto
			);
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle update errors', async () => {
			const error = new Error('Update failed');
			vaccinationService.update.mockRejectedValue(error);

			await expect(
				controller.update('vaccination_uuid', updateVaccinationDto)
			).rejects.toThrow(
				new HttpException(
					'Error updating vaccination',
					HttpStatus.BAD_REQUEST
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating vaccination',
				{ error }
			);
		});
	});

	describe('findOne', () => {
		it('should return a vaccination by id', async () => {
			const vaccination = {
				id: 'vaccination_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'V_000001',
				productName: 'Test Vaccination',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			};
			vaccinationService.findOne.mockResolvedValue(vaccination);

			const result = await controller.findOne('vaccination_uuid');

			expect(result).toEqual(vaccination);
			expect(vaccinationService.findOne).toHaveBeenCalledWith(
				'vaccination_uuid'
			);
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle findOne errors', async () => {
			const error = new Error('Vaccination not found');
			vaccinationService.findOne.mockRejectedValue(error);

			await expect(
				controller.findOne('vaccination_uuid')
			).rejects.toThrow(
				new HttpException(
					'Error fetching vaccination',
					HttpStatus.NOT_FOUND
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching vaccination',
				{ error }
			);
		});
	});

	describe('remove', () => {
		it('should delete a vaccination and return success message', async () => {
			vaccinationService.remove.mockResolvedValue(undefined);

			const result = await controller.remove('vaccination_uuid');

			expect(result).toEqual({
				message: 'Vaccination deleted successfully'
			});
			expect(vaccinationService.remove).toHaveBeenCalledWith(
				'vaccination_uuid'
			);
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle deletion errors', async () => {
			const error = new Error('Deletion failed');
			vaccinationService.remove.mockRejectedValue(error);

			await expect(controller.remove('vaccination_uuid')).rejects.toThrow(
				new HttpException(
					'Error deleting vaccination',
					HttpStatus.BAD_REQUEST
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error deleting vaccination',
				{ error }
			);
		});
	});
});
