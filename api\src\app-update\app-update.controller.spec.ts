import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppUpdateController } from './app-update.controller';
import { AppUpdateDto } from './app-update.dto';

describe('AppUpdateController', () => {
	let app: INestApplication;

	beforeAll(async () => {
		const moduleFixture: TestingModule = await Test.createTestingModule({
			controllers: [AppUpdateController]
		}).compile();

		app = moduleFixture.createNestApplication();
		app.useGlobalPipes(new ValidationPipe()); // Ensure global validation pipe is used
		await app.init();
	});

	afterAll(async () => {
		await app.close();
	});

	it('/app-update (POST) should return forceUpdate false for valid input', async () => {
		const validDto: AppUpdateDto = {
			buildType: 'Android',
			majorVersion: '1',
			minorVersion: '0',
			patchVersion: '0'
		};
		// Expecting Port 201 because the default HTTP status code for a successful POST request in NestJS is 201 (Created).
		const response = await request(app.getHttpServer())
			.post('/app-update')
			.send(validDto)
			.expect(201);

		expect(response.body).toEqual({ forceUpdate: false });
	});

	it('/app-update (POST) should return 400 for invalid input', async () => {
		const invalidDto = {
			buildType: 'Android',
			majorVersion: '', // Invalid because it's empty
			minorVersion: '0',
			patchVersion: '0'
		};

		await request(app.getHttpServer())
			.post('/app-update')
			.send(invalidDto)
			.expect(400);
	});
});
