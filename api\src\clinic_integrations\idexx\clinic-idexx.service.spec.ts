import { Test, TestingModule } from '@nestjs/testing';
import { ClinicIdexxService } from './clinic-idexx.service';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { Repository, UpdateResult } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateClinicIdexxDto } from './dto/create-clinic-idexx.dto';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { AxiosRequestHeaders, AxiosResponse } from 'axios';
import { CreateClinicIdexxTestItemDto } from './dto/create_clinic-idexx-test-item.dto';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';
import { User } from '../../users/entities/user.entity';
import { PatientsService } from '../../patients/patients.service';
import { ClinicLabReportService } from '../../clinic-lab-report/clinic-lab-report.service';
import { CreateIdexxOrderDto } from './dto/create-idexx-order.dto';
import { Patient } from '../../patients/entities/patient.entity';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { AppointmentsService } from '../../appointments/appointments.service';
import { ClinicIdexxUtilsService } from '../../utils/idexx/clinic-idexx-utils.service';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';

describe.skip('ClinicIdexxService', () => {
	let service: ClinicIdexxService;
	let httpService: HttpService;
	let patientService: PatientsService;
	let s3Service: S3Service;
	let logger: jest.Mocked<WinstonLogger>;
	let clinicIdexxUtilsService: ClinicIdexxUtilsService;
	let clinicLabReportService: ClinicLabReportService;
	let clinicIdexxRepository: jest.Mocked<Repository<CreateClinicIdexxEntity>>;
	let clinicLabReportRepository: jest.Mocked<Repository<ClinicLabReport>>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicIdexxService,
				{
					provide: getRepositoryToken(CreateClinicIdexxEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						findAndCount: jest.fn(),
						delete: jest.fn()
					}
				},
				{
					provide: HttpService,
					useValue: {
						get: jest.fn(),
						post: jest.fn(),
						delete: jest.fn()
					}
				},
				{
					provide: PatientsService,
					useValue: {
						getPatientDetails: jest.fn()
					}
				},
				{
					provide: S3Service,
					useValue: {}
				},
				{
					provide: ClinicLabReportService,
					useValue: {
						createOrUpdateLabReport: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(ClinicLabReport),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn()
					}
				},
				{
					provide: AppointmentsService,
					useValue: {}
				},
				{
					provide: ClinicIdexxUtilsService,
					useValue: {
						getIntegrationBaseURL: jest
							.fn()
							.mockReturnValue(
								'https://integration.vetconnectplus.com/api/'
							),
						getAuthKey: jest
							.fn()
							.mockReturnValue(
								'Basic bmlkYW5hX3phMTpMOG02OFBXTTBxM2s='
							),
						getHeaders: jest.fn().mockReturnValue({
							Authorization:
								'Basic bmlkYW5hX3phMTpMOG02OFBXTTBxM2s=',
							'x-pims-id': 'dfeba0c3-f2f0-4bbe-a8fc-413b8ac06d05',
							'x-pims-version': 1
						})
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicIdexxService>(ClinicIdexxService);
		httpService = module.get<HttpService>(HttpService);
		patientService = module.get<PatientsService>(PatientsService);
		s3Service = module.get<S3Service>(S3Service);
		clinicIdexxUtilsService = module.get<ClinicIdexxUtilsService>(
			ClinicIdexxUtilsService
		);
		clinicLabReportService = module.get<ClinicLabReportService>(
			ClinicLabReportService
		);
		clinicIdexxRepository = module.get(
			getRepositoryToken(CreateClinicIdexxEntity)
		);
		clinicLabReportRepository = module.get(
			getRepositoryToken(ClinicLabReport)
		);
	});

	const mockEntries: CreateClinicIdexxEntity[] = [
		{
			id: 'entry_uuid_1',
			clinicId: 'clinic_uuid',
			userName: '',
			password: '',
			type: '',
			authKey: '',
			clinic: new ClinicEntity()
		},
		{
			id: 'entry_uuid_2',
			clinicId: 'clinic_uuid',
			userName: '',
			password: '',
			type: '',
			authKey: '',
			clinic: new ClinicEntity()
		}
	];

	const clinicId = 'clinic_uuid';

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('Create new idexx entry', () => {
		const mockInputDto: CreateClinicIdexxDto = {
			clinicId: 'clinic_uuid',
			userName: 'ABC',
			password: 'DEF',
			type: ''
		};

		const mockOutputEntity: CreateClinicIdexxEntity = {
			clinicId: mockInputDto.clinicId,
			id: 'idexx_uuid',
			userName: 'ABC',
			password: 'DEF',
			type: '',
			authKey: '',
			clinic: new ClinicEntity()
		};

		it('should have createIdexxEntry function', () => {
			expect(service.createIdexxEntry).toBeDefined();
		});

		it('should check for idexx item that is already present', async () => {
			clinicIdexxRepository.findOne.mockResolvedValue(mockOutputEntity);

			await expect(
				service.createIdexxEntry(mockInputDto)
			).rejects.toThrow(ConflictException);

			expect(clinicIdexxRepository.findOne).toHaveBeenCalledWith({
				where: { clinicId: mockInputDto.clinicId }
			});
		});

		it('should create a new entry item', async () => {
			clinicIdexxRepository.findOne.mockResolvedValue(null);

			const userName = mockOutputEntity.userName;
			const password = mockOutputEntity.password;
			const base64Auth = btoa(userName + ':' + password);
			clinicIdexxUtilsService.getAuthKey = jest
				.fn()
				.mockReturnValue(base64Auth);

			const savedData = { ...mockInputDto, authKey: base64Auth };
			const tempOutputEntity = {
				...savedData,
				id: mockOutputEntity.id,
				clinic: new ClinicEntity()
			};
			clinicIdexxRepository.save.mockResolvedValue(tempOutputEntity);

			const result = await service.createIdexxEntry(mockInputDto);

			expect(result).toEqual(tempOutputEntity);
			expect(clinicIdexxRepository.save).toHaveBeenCalledWith(savedData);
			expect(clinicIdexxUtilsService.getAuthKey).toHaveBeenCalledWith(
				userName,
				password
			);
		});
	});

	describe('Get all idexx entries', () => {
		it('should have getIdexxEntries function', () => {
			expect(service.getIdexxEntries).toBeDefined();
		});

		it('should return the list of entry items', async () => {
			clinicIdexxRepository.findAndCount.mockResolvedValue([
				mockEntries,
				mockEntries.length
			]);

			const result = await service.getIdexxEntries(clinicId);

			expect(result).toEqual({
				items: mockEntries,
				total: mockEntries.length
			});

			expect(clinicIdexxRepository.findAndCount).toHaveBeenCalledWith({
				where: {
					clinicId
				}
			});
		});
	});

	describe('Delete a item entry for a given entry id', () => {
		const clinicIdexxId = 'entry_id';

		it('should have deletIdexxEntry function', () => {
			expect(service.deletIdexxEntry).toBeDefined();
		});

		it('should throw an error if the entry with the given entry id is not found ', async () => {
			await expect(
				service.deletIdexxEntry(clinicIdexxId)
			).rejects.toThrow(NotFoundException);
			expect(clinicIdexxRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicIdexxId }
			});
		});

		it('should delete the entry successfully(when one record is found)', async () => {
			clinicIdexxRepository.findOne.mockResolvedValue(mockEntries[0]);

			const mockDeketeResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 1 // Assuming one row is updated
			};

			clinicIdexxRepository.delete.mockResolvedValue(mockDeketeResult);
			const result = await service.deletIdexxEntry(clinicIdexxId);
			expect(result).toEqual({ status: true });
			expect(clinicIdexxRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicIdexxId }
			});
		});

		it('should return false if delete fails ( say more than one record found', async () => {
			const mockEntryResult = {
				status: false,
				message: 'Multiple records deleted. Check your query.'
			};
			clinicIdexxRepository.findOne.mockResolvedValue(mockEntries[0]);

			const mockDeketeResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 2 // Assuming one row is updated
			};

			clinicIdexxRepository.delete.mockResolvedValue(mockDeketeResult);
			const result = await service.deletIdexxEntry(clinicIdexxId);
			expect(result).toEqual(mockEntryResult);
			expect(clinicIdexxRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicIdexxId }
			});
		});

		it('should return false if record is not found to delete', async () => {
			const mockEntryResult = {
				status: false,
				message: 'No records found to delete.'
			};
			clinicIdexxRepository.findOne.mockResolvedValue(mockEntries[0]);

			const mockDeketeResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 0 // Assuming one row is updated
			};

			clinicIdexxRepository.delete.mockResolvedValue(mockDeketeResult);
			const result = await service.deletIdexxEntry(clinicIdexxId);
			expect(result).toEqual(mockEntryResult);
			expect(clinicIdexxRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicIdexxId }
			});
		});
	});

	describe('Get IDEXX test list by pinging the IDEXX api', () => {
		it('should have getAllIDexxTestsList function', () => {
			expect(service.getAllIDexxTestsList).toBeDefined();
		});

		it('should fetch idexx results successfully', async () => {
			const baseIDEXXURL =
				'https://integration.vetconnectplus.com/api/v1/';
			const apiUrl = baseIDEXXURL + 'ref/tests';

			const userName = 'nidana_za1';
			const passowrd = 'L8m68PWM0q3k';

			const base64Auth = btoa(userName + ':' + passowrd);

			const headers = {
				Authorization: 'Basic ' + base64Auth, // Base64 of user name and password
				'x-pims-version': 1,
				'x-pims-id': 'dfeba0c3-f2f0-4bbe-a8fc-413b8ac06d05'
			} as unknown as AxiosRequestHeaders; // Cast to AxiosRequestHeaders

			const mockResponse: AxiosResponse = {
				data: [], //  Expected response data
				status: 200,
				statusText: 'OK',
				headers: {},
				config: {
					headers: headers
				},
				request: {}
			};

			const mockCreateClinicIdexxEntity: CreateClinicIdexxEntity = {
				id: '',
				clinicId: 'clinic_uuid',
				userName: userName,
				password: passowrd,
				authKey: base64Auth,
				type: 'IDEXX',
				clinic: new ClinicEntity()
			};

			clinicIdexxRepository.findOne.mockResolvedValue(
				mockCreateClinicIdexxEntity
			);

			jest.spyOn(httpService, 'get').mockReturnValue(of(mockResponse)); // Mock the HTTP call

			const result = await service.getAllIDexxTestsList(clinicId);

			expect(result).toEqual(mockResponse.data);
			expect(httpService.get).toHaveBeenCalledWith(apiUrl, {
				headers: headers
			});
		});

		it('should throw a NotFoundException when clinic entry is not found', async () => {
			clinicIdexxRepository.findOne.mockResolvedValue(null);

			await expect(
				service.getAllIDexxTestsList(clinicId)
			).rejects.toThrow(NotFoundException);
			await expect(
				service.getAllIDexxTestsList(clinicId)
			).rejects.toThrow(`This entry with ${clinicId} doesn't exist`);
		});

		it('should throw an error when the request fails', async () => {
			const mockCreateClinicIdexxEntity: CreateClinicIdexxEntity = {
				id: '',
				clinicId: 'clinic_uuid',
				userName: '',
				password: '',
				authKey: '',
				type: 'IDEXX',
				clinic: new ClinicEntity()
			};

			clinicIdexxRepository.findOne.mockResolvedValue(
				mockCreateClinicIdexxEntity
			);

			const errorResponse = new Error(
				'Error fetching results: Network error'
			);
			jest.spyOn(httpService, 'get').mockReturnValue(
				throwError(() => errorResponse)
			); // Mock an error

			await expect(
				service.getAllIDexxTestsList(clinicId)
			).rejects.toThrow('Error fetching results: Network error'); // Expect the error to be thrown
		});
	});

	describe('Create new idexx test list enrty', () => {
		const mockInput: CreateClinicIdexxTestItemDto = {
			integrationType: 'IDEXX',
			clinicId: 'clinic_uuid',
			name: 'test item',
			description: 'test item description',
			chargeablePrice: 230,
			tax: 20
		};

		it('should have createIdexxTestItem function', () => {
			expect(service.createIdexxTestItem).toBeDefined();
		});

		it('should create a new IDEXX test list item', async () => {
			const createdDate = new Date();

			const mockOutput: ClinicLabReport = {
				id: '',
				clinicId: '',
				name: '',
				createdAt: createdDate,
				updatedAt: createdDate,
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				uniqueId: '',
				chargeablePrice: 0,
				tax: 0,
				associatedLab: 0,
				description: '',
				brandId: '',
				integrationType: 'IDEXX'
			};

			clinicLabReportRepository.save.mockResolvedValue(mockOutput);

			const result = await service.createIdexxTestItem(mockInput);

			expect(result).toEqual(mockOutput);
			expect(clinicLabReportRepository.save).toHaveBeenCalledWith(
				mockInput
			);
		});
	});

	describe('Delete a item entry for a given entry id', () => {
		const createdDate = new Date();

		const mockEntries: ClinicLabReport[] = [
			{
				id: 'entry_id_1',
				clinicId: 'clinic_uuid',
				name: 'Test 1',
				createdAt: createdDate,
				updatedAt: createdDate,
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				uniqueId: '',
				chargeablePrice: 0,
				tax: 0,
				associatedLab: 0,
				description: '',
				brandId: '',
				integrationType: 'IDEXX'
			},
			{
				id: 'entry_id_1',
				clinicId: 'clinic_uuid',
				name: 'Test 2',
				createdAt: createdDate,
				updatedAt: createdDate,
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				uniqueId: '',
				chargeablePrice: 0,
				tax: 0,
				associatedLab: 0,
				description: '',
				brandId: '',
				integrationType: 'IDEXX'
			}
		];

		const clinicLabReportEntryId = 'entry_id';

		it('should have deleteIdexxTestItem function', () => {
			expect(service.deleteIdexxTestItem).toBeDefined();
		});

		it('should throw an error if the entry with the given entry id is not found ', async () => {
			await expect(
				service.deleteIdexxTestItem(clinicLabReportEntryId)
			).rejects.toThrow(NotFoundException);
			expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicLabReportEntryId }
			});
		});

		it('should delete the entry successfully(when one record is found)', async () => {
			clinicLabReportRepository.findOne.mockResolvedValue(mockEntries[0]);

			const mockDeketeResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 1 // Assuming one row is updated
			};

			clinicLabReportRepository.delete.mockResolvedValue(
				mockDeketeResult
			);
			const result = await service.deleteIdexxTestItem(
				clinicLabReportEntryId
			);
			expect(result).toEqual({ status: true });
			expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicLabReportEntryId }
			});
		});

		it('should return false if delete fails ( say more than one record found', async () => {
			const mockEntryResult = {
				status: false,
				message: 'Multiple records deleted. Check your query.'
			};
			clinicLabReportRepository.findOne.mockResolvedValue(mockEntries[0]);

			const mockDeketeResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 2 // Assuming one row is updated
			};

			clinicLabReportRepository.delete.mockResolvedValue(
				mockDeketeResult
			);
			const result = await service.deleteIdexxTestItem(
				clinicLabReportEntryId
			);
			expect(result).toEqual(mockEntryResult);
			expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicLabReportEntryId }
			});
		});

		it('should return false if record is not found to delete', async () => {
			const mockEntryResult = {
				status: false,
				message: 'No records found to delete.'
			};
			clinicLabReportRepository.findOne.mockResolvedValue(mockEntries[0]);

			const mockDeketeResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 0 // Assuming one row is updated
			};

			clinicLabReportRepository.delete.mockResolvedValue(
				mockDeketeResult
			);
			const result = await service.deleteIdexxTestItem(
				clinicLabReportEntryId
			);
			expect(result).toEqual(mockEntryResult);
			expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({
				where: { id: clinicLabReportEntryId }
			});
		});
	});

	describe('Create an IDEXX order', () => {
		const mockInputOrderDto: CreateIdexxOrderDto = {
			patientId: 'patient_uuid',
			appointmentId: 'appointment_uuid',
			clinicLabReportId: 'clinic_lab_report_uuid',
			clinicId: 'clinic_uuid',
			integrationCode: 'INTEGRATION_CODE'
		};

		const OwnerDummyData = {
			clientId: '',
			isDataImported: false
		}

		const DummyData = {
			patientId: '',
			isDataImported: false,
			isBalanceUpdated: false
		}
		const mockPatientDetails: Patient = {
			patientName: 'Fluffy',
			microchipId: '**********',
			patientOwners: [
				{
					owner: {
						lastName: 'Doe',
						firstName: 'John',
						id: '',
						email: '',
						phoneNumber: '',
						countryCode: '',
						address: '',
						patientOwners: [],
						createdAt: new Date(),
						updatedAt: new Date(),
						dummyData: OwnerDummyData
					},
					id: '',
					patientId: '',
					ownerId: '',
					isPrimary: false,
					createdAt: new Date(),
					updatedAt: new Date(),
					patient: new Patient()
				}
			],
			id: 'patient_uuid',
			breed: '',
			clinicId: 'clinic_uuid',
			updatedByUser: new User(),
			patientAlerts: [],
			balance: 0,
			dummyData: DummyData
		};

		it('should create a new order', async () => {
			const baseIDEXXURL =
				'https://integration.vetconnectplus.com/api/v1/';
			const apiUrl = baseIDEXXURL + 'order'; // Correct endpoint for creating an order

			const userName = 'nidana_za1';
			const password = 'L8m68PWM0q3k';

			const base64Auth = Buffer.from(userName + ':' + password).toString(
				'base64'
			);

			const headers = {
				Authorization: 'Basic ' + base64Auth, // Base64 of user name and password
				'x-pims-version': 1,
				'x-pims-id': 'dfeba0c3-f2f0-4bbe-a8fc-413b8ac06d05'
			} as unknown as AxiosRequestHeaders; // Cast to AxiosRequestHeaders

			const mockResponse: AxiosResponse = {
				data: {}, //{orderId: 'order_id', uiURL:"test_url"}, //  Expected response data
				status: 200,
				statusText: 'OK',
				headers: {},
				config: {
					headers: headers
				},
				request: {}
			};

			const mockLabReportOutput: LabReport = {
				id: 'lab_report_uuid',
				appointmentId: 'appointment_uuid',
				appointment: new AppointmentEntity(),
				patientId: 'patient_uuid',
				patient: mockPatientDetails,
				clinicLabReportId: 'clinic_lab_report_uuid',
				clinicLabReport: new ClinicLabReport(),
				clinicId: 'clinic_uuid',
				clinic: new ClinicEntity(),
				files: [],
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				status: 'PENDING'
			};

			const mockCreateClinicIdexxEntity: CreateClinicIdexxEntity = {
				id: '',
				clinicId: 'clinic_uuid',
				userName: userName,
				password: password,
				authKey: base64Auth,
				type: 'IDEXX',
				clinic: new ClinicEntity()
			};

			const mockDeviceUnits = [
				{
					deviceSerialNumber: 'DEVICE123'
				}
			];

			jest.spyOn(patientService, 'getPatientDetails').mockResolvedValue(
				mockPatientDetails
			);
			jest.spyOn(service, 'getAllIDEXXDeviceUnits').mockResolvedValue(
				mockDeviceUnits
			);
			clinicIdexxRepository.findOne.mockResolvedValue(
				mockCreateClinicIdexxEntity
			);
			jest.spyOn(httpService, 'post').mockReturnValue(of(mockResponse));
			jest.spyOn(
				clinicLabReportService,
				'createOrUpdateLabReport'
			).mockResolvedValue(mockLabReportOutput);

			const result = await service.createIdexxOrder(mockInputOrderDto);

			expect(result).toEqual(mockResponse.data);
			expect(httpService.post).toHaveBeenCalledWith(
				apiUrl,
				expect.objectContaining({
					patients: expect.any(Array),
					tests: [mockInputOrderDto.integrationCode],
					ivls: [
						{
							serialNumber: mockDeviceUnits[0].deviceSerialNumber
						}
					]
				}),
				expect.any(Object)
			);
		});

		it('should return error if order creation fails', async () => {
			const mockDeviceUnits = [
				{
					deviceSerialNumber: 'DEVICE123'
				}
			];

			jest.spyOn(patientService, 'getPatientDetails').mockResolvedValue(
				mockPatientDetails
			);
			jest.spyOn(service, 'getAllIDEXXDeviceUnits').mockResolvedValue(
				mockDeviceUnits
			);

			const mockCreateClinicIdexxEntity: CreateClinicIdexxEntity = {
				id: '',
				clinicId: 'clinic_uuid',
				userName: '',
				password: '',
				authKey: '',
				type: 'IDEXX',
				clinic: new ClinicEntity()
			};

			clinicIdexxRepository.findOne.mockResolvedValue(
				mockCreateClinicIdexxEntity
			);

			const errorResponse = new Error(
				'Error fetching results: Network error'
			);
			jest.spyOn(httpService, 'post').mockReturnValue(
				throwError(() => errorResponse)
			); // Mock an error

			await expect(
				service.createIdexxOrder(mockInputOrderDto)
			).rejects.toThrow('Error fetching results: Network error'); // Expect the error to be thrown
		});
	});

	describe('Cancel a idexx order for a given oreder id', () => {
		const idexxOrderId = 'oreder_id';

		it('should have cancelIdexxOrder function', () => {
			expect(service.cancelIdexxOrder).toBeDefined();
		});

		it('should throw a NotFoundException when clinic entry details are not found', async () => {
			clinicIdexxRepository.findOne.mockResolvedValue(null);

			await expect(
				service.cancelIdexxOrder(clinicId, idexxOrderId)
			).rejects.toThrow(NotFoundException);
			await expect(
				service.cancelIdexxOrder(clinicId, idexxOrderId)
			).rejects.toThrow(`This entry with ${clinicId} doesn't exist`);
		});

		it('should cancel an order with the given idexx order id', async () => {
			const baseIDEXXURL =
				'https://integration.vetconnectplus.com/api/v1/';
			const apiUrl = baseIDEXXURL + 'order' + '/' + idexxOrderId;

			const userName = 'nidana_za1';
			const password = 'L8m68PWM0q3k';

			const base64Auth = Buffer.from(userName + ':' + password).toString(
				'base64'
			);

			const headers = {
				Authorization: 'Basic ' + base64Auth, // Base64 of user name and password
				'x-pims-version': 1,
				'x-pims-id': 'dfeba0c3-f2f0-4bbe-a8fc-413b8ac06d05'
			} as unknown as AxiosRequestHeaders; // Cast to AxiosRequestHeaders

			const mockResponse: AxiosResponse = {
				data: {},
				status: 200,
				statusText: 'OK',
				headers: {},
				config: {
					headers: headers
				},
				request: {}
			};

			const mockCreateClinicIdexxEntity: CreateClinicIdexxEntity = {
				id: '',
				clinicId: 'clinic_uuid',
				userName: '',
				password: '',
				authKey: '',
				type: 'IDEXX',
				clinic: new ClinicEntity()
			};

			clinicIdexxRepository.findOne.mockResolvedValue(
				mockCreateClinicIdexxEntity
			);

			jest.spyOn(httpService, 'delete').mockReturnValue(of(mockResponse));

			const result = await service.cancelIdexxOrder(
				clinicId,
				idexxOrderId
			);

			expect(result).toEqual({
				status: mockResponse.data.status
			});
			expect(httpService.delete).toHaveBeenCalledWith(apiUrl, {
				headers: headers
			});
		});
	});
});
