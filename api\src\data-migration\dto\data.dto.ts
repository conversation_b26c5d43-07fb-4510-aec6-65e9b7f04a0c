interface DummyData {
	patientId: string;
	isDataImported: boolean;
	isBalanceUpdated: boolean;
}

interface OwnerDummyData {
	clientId: string;
	isDataImported: boolean;
}

export class CreateOwnerDto {
	firstName!: string;
	lastName!: string;
	phoneNumber!: string;
	address!: string;
	email!: string;
	dummyData?: OwnerDummyData;
	countryCode!: string;
	brandId!: string;
	ownerBalance!: number;
	openingBalance!: number;
}
export class CreatePatientDto {
	name!: string;
	species!: string;
	breed!: string;
	gender!: 'Male' | 'Female' | 'Unknown';
	weight!: string;
	clinicId!: string;
	balance!: number;
	dummyData?: DummyData;
}

export class PatientLookupDto {
	clinicId!: string;
	dummyData!: {
		patientId: string;
		isDataImported: boolean;
	};
}
export class CreatePatientOwnerDto {
	patientId!: string;
	ownerId!: string;
	isPrimary!: boolean;
}

export class CreateAppointmentDto {
	clinicId!: string;
	doctorIds!: string[];
	providerIds?: string[];
	patientId!: string;
	roomId?: string | null;
	date!: string;
	startTime!: string;
	endTime!: string;
	reason!: string;
	type!: string;
	isBlocked!: boolean;
	weight!: number | null;
	status?: string;
}

export class CreateAppointmentDetailsDto {
	appointmentId!: string;
	details: any; // You might want to define a more specific type for the details
}

export class InvoiceDataDto {
	date!: string;
	patientId!: string;
	pdfUrl?: string;
	s3Key!: string;
	clinicId!: string;
	brandId!: string;
	metadata?: Record<string, any>; // Optional metadata field that can store any JSON object
}

export class DiagnosticsDataDto {
	patientId!: string; // old patient ID
	date!: string;
	pdfUrl!: string;
	fileName!: string;
	labReportTypeId!: string;
}

export class MigrateAppointmentDto {
	clinicId?: string;
	doctorIds!: string[];
	providerIds?: string[];
	patientId!: string; // This is the old patient ID
	roomId?: string | null;
	date!: string;
	startTime!: string;
	endTime!: string;
	reason?: string;
	type!: string;
	status!: string;
	isBlocked!: boolean;
	weight?: number | null;
}

export class UpdateOwnerOpeningBalanceDto {
	clientId!: string;
	openingBalance!: number;
}

export class BulkUpdateOpeningBalanceDto {
	owners!: UpdateOwnerOpeningBalanceDto[];
}
