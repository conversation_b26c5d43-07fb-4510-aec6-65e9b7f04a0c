import { Module, forwardRef } from '@nestjs/common';
import { ClinicService } from './clinic.service';
import { ClinicController } from './clinic.controller';
import { ClinicEntity } from './entities/clinic.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { RoleModule } from '../roles/role.module';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicConsumablesModule } from '../clinic-consumables/clinic-consumables.module';
import { ClinicUser } from './entities/clinic-user.entity';
import { ClinicMedicationsModule } from '../clinic-medications/clinic-medications.module';
import { ClinicLabReportModule } from '../clinic-lab-report/clinic-lab-report.module';
import { ClinicProductsModule } from '../clinic-products/clinic-products.module';
import { ClinicServicesModule } from '../clinic-services/clinic-services.module';
import { ClinicVaccinationsModule } from '../clinic-vaccinations/clinic-vaccinations.module';
import { UsersModule } from '../users/users.module';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { BrandsModule } from '../brands/brands.module';
import { BrandService } from '../brands/brands.service';

@Module({
	imports: [
		TypeOrmModule.forFeature([ClinicEntity, ClinicRoomEntity, ClinicUser]),
		RoleModule,
		ClinicConsumablesModule,
		ClinicMedicationsModule,
		ClinicLabReportModule,
		ClinicProductsModule,
		ClinicServicesModule,
		ClinicVaccinationsModule,
		forwardRef(() => UsersModule),
		forwardRef(() => BrandsModule)
	],
	controllers: [ClinicController],
	providers: [ClinicService, WinstonLogger, SESMailService],
	exports: [ClinicService]
})
export class ClinicModule {}
