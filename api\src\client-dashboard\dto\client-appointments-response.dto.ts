import { ApiProperty } from '@nestjs/swagger';
import { TimeDuration } from '../../clinics/entities/clinic.entity';

export class AppointmentBaseDto {
	@ApiProperty({
		description: 'The ID of the appointment',
		example: '123e4567-e89b-12d3-a456-426614174000'
	})
	id!: string;

	@ApiProperty({
		description: 'The date of the appointment',
		example: '2025-02-02'
	})
	date!: string;

	@ApiProperty({
		description: 'The start time of the appointment',
		example: '15:00:00'
	})
	startTime!: string;

	@ApiProperty({
		description: 'The end time of the appointment',
		example: '16:00:00'
	})
	endTime!: string;

	@ApiProperty({ description: 'The name of the patient', example: '<PERSON>' })
	patientName!: string;

	@ApiProperty({
		description: 'The name of the doctor',
		example: 'Dr. <PERSON>'
	})
	doctorName!: string;

	@ApiProperty({
		description: 'How the appointment was booked',
		example: 'Online',
		enum: ['Online', 'Clinic']
	})
	mode!: 'Online' | 'Clinic';

	@ApiProperty({
		description: 'The status of the appointment',
		example: 'Completed',
		enum: ['Completed', 'Missed', 'Cancelled', 'Scheduled']
	})
	status!: 'Completed' | 'Missed' | 'Cancelled' | 'Scheduled';

	@ApiProperty({
		description:
			'Whether the appointment can be modified or cancelled based on deadline settings',
		example: true,
		type: Boolean
	})
	canModifyOrCancel!: boolean;

	@ApiProperty({
		description:
			'The time until the appointment can be modified or cancelled',
		example: '1 hour',
		required: false
	})
	modificationDeadline?: TimeDuration | null;
}

export class UpcomingAppointmentDto extends AppointmentBaseDto {}

export class PreviousAppointmentDto extends AppointmentBaseDto {
	@ApiProperty({
		description: 'The type of visit',
		example: 'Vaccination',
		required: false
	})
	visitType?: string;

	@ApiProperty({
		description: 'The status of the appointment',
		example: 'Completed',
		enum: ['Completed', 'Missed', 'Cancelled', 'Scheduled']
	})
	status!: 'Completed' | 'Missed' | 'Cancelled' | 'Scheduled';
}

export class ClientAppointmentsResponseDto {
	@ApiProperty({
		description: 'List of upcoming appointments',
		type: [UpcomingAppointmentDto]
	})
	upcoming!: UpcomingAppointmentDto[];

	@ApiProperty({
		description: 'List of previous appointments',
		type: [PreviousAppointmentDto]
	})
	previous!: PreviousAppointmentDto[];
}
