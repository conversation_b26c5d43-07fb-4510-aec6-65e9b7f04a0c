import { Test, TestingModule } from '@nestjs/testing';
import { ClientDashboardController } from './client-dashboard.controller';
import { ClientDashboardService } from './client-dashboard.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { DirectLoginDto } from './dto/direct-login.dto';
import { ClientDashboardResponseDto } from './dto/client-dashboard-response.dto';
import { ClientAppointmentsResponseDto } from './dto/client-appointments-response.dto';
import { Role } from '../roles/role.enum';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
// ValidationPipe is handled by NestJS

// Mock ClientDashboardService
const mockClientDashboardService = {
	directLogin: jest.fn(),
	getClientDashboard: jest.fn(),
	getClientAppointments: jest.fn(),
	getClientClinics: jest.fn(),
	getClientDoctors: jest.fn()
};

// Mock WinstonLogger
const mockLogger = {
	log: jest.fn(),
	error: jest.fn(),
	warn: jest.fn(),
	debug: jest.fn(),
	verbose: jest.fn()
};

// Mock Guards
const mockJwtAuthGuard = { canActivate: jest.fn(() => true) };
const mockRolesGuard = { canActivate: jest.fn(() => true) };

// Define the expanded RequestWithUser interface locally for the test
interface RequestWithUser {
	user: {
		brandId?: string;
		sub?: string; // Owner ID (OwnerBrand ID)
		userId?: string; // Could be OwnerBrand ID or GlobalOwner ID depending on JWT payload
		phoneNumber?: string;
		role?: string;
		globalOwnerId?: string;
	};
}

describe('ClientDashboardController', () => {
	let controller: ClientDashboardController;
	// Using mockClientDashboardService directly in tests
	let logger: WinstonLogger;

	const mockOwnerId = 'owner-brand-uuid-123';
	const mockBrandId = 'brand-uuid-456';
	const mockGlobalOwnerId = 'global-owner-uuid-789';

	const mockRequest: RequestWithUser = {
		user: {
			sub: mockOwnerId, // Typically 'sub' holds the primary identifier (OwnerBrand ID here)
			userId: mockOwnerId, // Let's assume userId also holds OwnerBrand ID for consistency
			brandId: mockBrandId,
			role: Role.OWNER,
			globalOwnerId: mockGlobalOwnerId
		}
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClientDashboardController],
			providers: [
				{
					provide: ClientDashboardService,
					useValue: mockClientDashboardService
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				},
				Reflector // For RolesGuard
			]
		})
			.overrideGuard(JwtAuthGuard)
			.useValue(mockJwtAuthGuard)
			.overrideGuard(RolesGuard)
			.useValue(mockRolesGuard)
			// No need to override ValidationPipe, NestJS handles it if applied globally or via @UsePipes
			.compile();

		controller = module.get<ClientDashboardController>(
			ClientDashboardController
		);
		// Get service for reference but use mockClientDashboardService in tests
		module.get<ClientDashboardService>(ClientDashboardService);
		logger = module.get<WinstonLogger>(WinstonLogger);

		// Reset mocks
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	// --- Test cases for directLogin ---
	describe('directLogin', () => {
		it('should return token and owner info on successful login', async () => {
			const directLoginDto: DirectLoginDto = {
				phoneNumber: '**********',
				countryCode: '91',
				brandId: mockBrandId
			};

			const expectedResponse = {
				token: 'jwt-token-123',
				owner: {
					id: mockOwnerId,
					phoneNumber: directLoginDto.phoneNumber,
					fullName: 'Test Owner'
				}
			};

			mockClientDashboardService.directLogin.mockResolvedValue(
				expectedResponse
			);

			const result = await controller.directLogin(directLoginDto);

			expect(result).toEqual(expectedResponse);
			expect(mockClientDashboardService.directLogin).toHaveBeenCalledWith(
				directLoginDto
			);
			expect(logger.log).toHaveBeenCalledTimes(2); // Called for initial log and success log
		});

		it('should throw HttpException if service throws NotFoundException', async () => {
			const directLoginDto: DirectLoginDto = {
				phoneNumber: '**********',
				countryCode: '91',
				brandId: 'non-existent-brand'
			};

			const notFoundError = new HttpException(
				'Brand not found',
				HttpStatus.NOT_FOUND
			);
			mockClientDashboardService.directLogin.mockRejectedValue(
				notFoundError
			);

			await expect(
				controller.directLogin(directLoginDto)
			).rejects.toThrow(notFoundError);
			expect(logger.error).toHaveBeenCalled();
		});

		it('should wrap non-HttpException errors in a 500 HttpException', async () => {
			const directLoginDto: DirectLoginDto = {
				phoneNumber: '**********',
				countryCode: '91',
				brandId: mockBrandId
			};

			const genericError = new Error('Database connection failed');
			mockClientDashboardService.directLogin.mockRejectedValue(
				genericError
			);

			await expect(
				controller.directLogin(directLoginDto)
			).rejects.toThrow(
				new HttpException(
					'Failed to login',
					HttpStatus.INTERNAL_SERVER_ERROR
				)
			);
			expect(logger.error).toHaveBeenCalled();
		});
	});

	// --- Test cases for getClientDashboard ---
	describe('getClientDashboard', () => {
		it('should return dashboard data successfully', async () => {
			// Using 'as' to bypass strict type checking for test mocks
			const expectedResponse = {
				owner: {
					id: mockOwnerId,
					firstName: 'Test',
					lastName: 'Owner',
					fullName: 'Test Owner',
					phoneNumber: '**********',
					email: '<EMAIL>',
					address: '123 Main St',
					ownerBalance: 1000,
					ownerCredits: 500
				},
				pets: [
					{
						id: 'pet-1',
						name: 'Max',
						breed: 'Labrador',
						species: 'Dog'
					},
					{
						id: 'pet-2',
						name: 'Whiskers',
						breed: 'Persian',
						species: 'Cat'
					}
				]
			} as ClientDashboardResponseDto;

			mockClientDashboardService.getClientDashboard.mockResolvedValue(
				expectedResponse
			);

			const result = await controller.getClientDashboard(
				mockOwnerId,
				mockRequest
			);

			expect(result).toEqual(expectedResponse);
			expect(
				mockClientDashboardService.getClientDashboard
			).toHaveBeenCalledWith(mockOwnerId, mockBrandId);
		});

		it('should throw HttpException if brandId is missing in request context', async () => {
			const requestWithoutBrandId = {
				user: { ...mockRequest.user, brandId: undefined }
			} as RequestWithUser;

			await expect(
				controller.getClientDashboard(
					mockOwnerId,
					requestWithoutBrandId
				)
			).rejects.toThrow(
				new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(
				mockClientDashboardService.getClientDashboard
			).not.toHaveBeenCalled();
		});

		it('should throw HttpException if service throws an error', async () => {
			const notFoundError = new HttpException(
				'Owner not found',
				HttpStatus.NOT_FOUND
			);
			mockClientDashboardService.getClientDashboard.mockRejectedValue(
				notFoundError
			);

			await expect(
				controller.getClientDashboard(mockOwnerId, mockRequest)
			).rejects.toThrow(notFoundError);

			expect(logger.error).toHaveBeenCalled();
		});
	});

	// --- Test cases for getClientAppointments ---
	describe('getClientAppointments', () => {
		it('should return appointments successfully', async () => {
			// Using 'as unknown as' to bypass strict type checking for test mocks
			const expectedResponse = {
				upcoming: [
					{
						id: 'appt-1',
						date: '2024-09-20',
						startTime: '10:00:00',
						endTime: '10:30:00',
						time: '10:00 AM - 10:30 AM',
						patientName: 'Max',
						doctorName: 'Dr. Smith',
						mode: 'Online',
						status: 'Completed' // Using valid enum value
					}
				],
				previous: [
					{
						id: 'appt-2',
						date: '2024-09-10',
						startTime: '14:30:00',
						endTime: '15:00:00',
						time: '2:30 PM - 3:00 PM',
						patientName: 'Max',
						doctorName: 'Dr. Smith',
						mode: 'Clinic',
						visitType: 'Vaccination',
						status: 'Completed'
					}
				]
			} as unknown as ClientAppointmentsResponseDto;

			mockClientDashboardService.getClientAppointments.mockResolvedValue(
				expectedResponse
			);

			const result = await controller.getClientAppointments(
				mockOwnerId,
				mockRequest
			);

			expect(result).toEqual(expectedResponse);
			expect(
				mockClientDashboardService.getClientAppointments
			).toHaveBeenCalledWith(mockOwnerId, mockBrandId, {
				date: undefined,
				status: undefined
			});
		});

		it('should handle date filter', async () => {
			const date = '2024-09-20';
			mockClientDashboardService.getClientAppointments.mockResolvedValue({
				upcoming: [],
				previous: []
			});

			await controller.getClientAppointments(
				mockOwnerId,
				mockRequest,
				date
			);

			expect(
				mockClientDashboardService.getClientAppointments
			).toHaveBeenCalledWith(mockOwnerId, mockBrandId, {
				date,
				status: undefined
			});
		});

		it('should handle status filter (parsing JSON)', async () => {
			const statusJson = '["Scheduled","Completed"]';
			const parsedStatus = ['Scheduled', 'Completed'];

			mockClientDashboardService.getClientAppointments.mockResolvedValue({
				upcoming: [],
				previous: []
			});

			await controller.getClientAppointments(
				mockOwnerId,
				mockRequest,
				undefined,
				statusJson
			);

			expect(
				mockClientDashboardService.getClientAppointments
			).toHaveBeenCalledWith(mockOwnerId, mockBrandId, {
				date: undefined,
				status: parsedStatus
			});
		});

		it('should handle invalid status JSON gracefully', async () => {
			const invalidStatusJson = 'not-valid-json';

			mockClientDashboardService.getClientAppointments.mockResolvedValue({
				upcoming: [],
				previous: []
			});

			await controller.getClientAppointments(
				mockOwnerId,
				mockRequest,
				undefined,
				invalidStatusJson
			);

			expect(
				mockClientDashboardService.getClientAppointments
			).toHaveBeenCalledWith(mockOwnerId, mockBrandId, {
				date: undefined,
				status: undefined
			});
			expect(logger.error).toHaveBeenCalled();
		});

		it('should throw HttpException if brandId is missing in request context', async () => {
			const requestWithoutBrandId = {
				user: { ...mockRequest.user, brandId: undefined }
			} as RequestWithUser;

			await expect(
				controller.getClientAppointments(
					mockOwnerId,
					requestWithoutBrandId
				)
			).rejects.toThrow(
				new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(
				mockClientDashboardService.getClientAppointments
			).not.toHaveBeenCalled();
		});
	});

	// --- Test cases for getClientClinics ---
	describe('getClientClinics', () => {
		it('should return clinics successfully', async () => {
			const expectedClinics = [
				{ id: 'clinic-1', name: 'Main Clinic' },
				{ id: 'clinic-2', name: 'Branch Clinic' }
			];

			mockClientDashboardService.getClientClinics.mockResolvedValue(
				expectedClinics
			);

			const result = await controller.getClientClinics(mockRequest);

			expect(result).toEqual(expectedClinics);
			expect(
				mockClientDashboardService.getClientClinics
			).toHaveBeenCalledWith(mockBrandId);
		});

		it('should throw HttpException if brandId is missing in request context', async () => {
			const requestWithoutBrandId = {
				user: { ...mockRequest.user, brandId: undefined }
			} as RequestWithUser;

			await expect(
				controller.getClientClinics(requestWithoutBrandId)
			).rejects.toThrow(
				new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(
				mockClientDashboardService.getClientClinics
			).not.toHaveBeenCalled();
		});
	});

	// --- Test cases for getClientDoctors ---
	describe('getClientDoctors', () => {
		it('should return doctors successfully', async () => {
			const expectedDoctors = [
				{ id: 'doctor-1', name: 'Dr. Smith' },
				{ id: 'doctor-2', name: 'Dr. Johnson' }
			];

			mockClientDashboardService.getClientDoctors.mockResolvedValue(
				expectedDoctors
			);

			const result = await controller.getClientDoctors(mockRequest);

			expect(result).toEqual(expectedDoctors);
			expect(
				mockClientDashboardService.getClientDoctors
			).toHaveBeenCalledWith(mockBrandId, undefined);
		});

		it('should handle clinicId filter', async () => {
			const clinicId = 'clinic-1';
			const expectedDoctors = [{ id: 'doctor-1', name: 'Dr. Smith' }];

			mockClientDashboardService.getClientDoctors.mockResolvedValue(
				expectedDoctors
			);

			const result = await controller.getClientDoctors(
				mockRequest,
				clinicId
			);

			expect(result).toEqual(expectedDoctors);
			expect(
				mockClientDashboardService.getClientDoctors
			).toHaveBeenCalledWith(mockBrandId, clinicId);
		});

		it('should throw HttpException if brandId is missing in request context', async () => {
			const requestWithoutBrandId = {
				user: { ...mockRequest.user, brandId: undefined }
			} as RequestWithUser;

			await expect(
				controller.getClientDoctors(requestWithoutBrandId)
			).rejects.toThrow(
				new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(
				mockClientDashboardService.getClientDoctors
			).not.toHaveBeenCalled();
		});
	});
});
