import { parse } from 'csv-parse';
import { createReadStream } from 'fs';
import moment from 'moment';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api/data-migration';

async function readCSV(filePath) {
    const parser = createReadStream(filePath).pipe(parse({
      columns: true,
      skip_empty_lines: true
    }));
  
    const records = [];
    for await (const record of parser) {
      records.push(record);
    }
    return records;
}

async function processInvoiceCSV() {
  // Parse CSV 
  const results = await readCSV('invoices.csv');
 
  
//   Format data
  const formattedData = results.map(row => ({
    date: moment(row.Date, 'MMM DD, YYYY').format('YYYY-MM-DD'),
    invoiceNumber: row['Invoice Number'],
    patientId: row.PatientId.replace('-', ''),
    pdfUrl: row.url
  }));
  console.log(formattedData)
//   // Send data to API
  try {
    const response = await axios.post(`${API_BASE_URL}/process`, formattedData);
    console.log('Processing results:', response.data);
  } catch (error) {
    console.error('Error processing invoices:', error);
  }
}

processInvoiceCSV()