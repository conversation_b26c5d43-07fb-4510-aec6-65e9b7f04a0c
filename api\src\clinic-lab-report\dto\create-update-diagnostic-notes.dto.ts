import { IsString, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class BaseDiagnosticNoteDto {
  @IsString()
  id!: string;
}

export class CreateDiagnosticNoteDto extends BaseDiagnosticNoteDto {
    @IsString()
    type!: string;
  
    @ValidateNested()
    @Type(() => Object)
    data!: {
      title: string;
      notes: any;
    };
    labReportId?: string;
  }

  export class EditDiagnosticNoteDto extends BaseDiagnosticNoteDto {
    @IsOptional()
    @ValidateNested()
    @Type(() => Object)
    data?: {
      title?: string;
      notes?: any;
    };
  }

  export class DeleteDiagnosticNoteDto extends BaseDiagnosticNoteDto {}
