import { Test, TestingModule } from '@nestjs/testing';
import { Chat<PERSON>oomController } from './chat-room.controller';
import { ChatRoomService } from './chat-room.service';
import { CreateChatRoomDto } from './dto/create-chat-room.dto';
import { ChatRoom } from './chat-room.entity';
import { ChatRoomUser } from './chat-room-users.entity';
import { CreateChatMessageDto } from './dto/create-chat-message.dto';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { User } from '../users/entities/user.entity';
import { UpdateChatUserRoomDto } from './dto/update-chat-user-room.dto';

describe('ChatRoomController', () => {
	let controller: ChatRoomController;
	let chatRoomService : jest.Mocked<ChatRoomService>;
	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ChatRoomController],
			providers: [
				{
					provide: ChatRoomService,
					useValue: {
						create: jest.fn(),
						findAllForUser: jest.fn(),
						sendMessage: jest.fn(),
						getChatRoomDetails: jest.fn(),
						updateChatRoom: jest.fn(),
					}
				},
			]
		}).compile();

		controller = module.get<ChatRoomController>(ChatRoomController);
		chatRoomService = module.get(ChatRoomService);

	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create', () => {
		it('should have the create function in the controller', () => {
			expect(controller.create).toBeDefined();
		})
		it('should create the chatRoom', async() => {
			const mockCreateChatRoomDto :CreateChatRoomDto = {
				userIds: ['a1','b_1']
			}
			const mockChatRoom : ChatRoom = {
				id: 'c_1',
				lastMessageSender: 'a_1',
				lastMessage: 'hi',
				unreadMessage: 0,
				users: [],
				messages: [],
				createdAt: new Date(),
				createdBy: 'd_1',
				updatedAt: new Date(),
				updatedBy: 'e_1'
			  };

			  chatRoomService.create.mockResolvedValue(mockChatRoom);

			  const result = await controller.create(mockCreateChatRoomDto);

			  expect(chatRoomService.create).toHaveBeenCalled();
		})
		
	})

	describe('findAllForUser', () => {
		it('findAllForUser should be defined', () => {
			expect(controller.findAllForUser).toBeDefined();
		})
		it('should return all the chatroom for user', async() => {
			const mockUserId = 'c_1';
  			const mockChatRooms: ChatRoomUser[] = [];

			chatRoomService.findAllForUser.mockResolvedValue(mockChatRooms);

  			const result = await controller.findAllForUser(mockUserId);

  			expect(chatRoomService.findAllForUser).toHaveBeenCalledWith(mockUserId);
  			expect(result).toEqual(mockChatRooms);

		})
	})

	describe('sendMessage', () => {
		it('sendMessage should be defined', () => {
			expect(controller.sendMessage).toBeDefined();
		})
		it('should send the message', async() => {
			const mockCreateChatMessageDto :CreateChatMessageDto = {
				chatRoomId: 'c_1',
				message: 'hi',
				otherUserId: 'c_1',
				senderId: 'd_1',
			}

			const mockChatRoomMessage :ChatRoomMessage = {
				chatRoom: {} as ChatRoom,
				chatRoomId: 'c_1',
				createdAt: new Date(),
				file: {},
				id: 's_1',
				message: 'hi',
				meta: {},
				senderId: 'd_1',
				updatedAt: new Date(),
				user: {} as User
			}

			chatRoomService.sendMessage.mockResolvedValue(mockChatRoomMessage)

			const response = await controller.sendMessage(mockCreateChatMessageDto);

			expect(chatRoomService.sendMessage).toHaveBeenCalled();
			expect(response).toEqual(mockChatRoomMessage)

		})
	})

	describe('getChatRoomDetails', () => {
		it('getChatRoomDetails should be defined', () => {
			expect(controller.getChatRoomDetails).toBeDefined();
		})

		it('should return the detail of chatRoom', async() => {
			const mockChatRoomId: string = 'c_1';
			const mockChatRoom : ChatRoom = {
				id: 'c_1',
				lastMessageSender: 'a_1',
				lastMessage: 'hi',
				unreadMessage: 0,
				users: [],
				messages: [],
				createdAt: new Date(),
				createdBy: 'd_1',
				updatedAt: new Date(),
				updatedBy: 'e_1'
			  };

			  chatRoomService.getChatRoomDetails.mockResolvedValue(mockChatRoom);

			  const response = await controller.getChatRoomDetails(mockChatRoomId);

			  expect(chatRoomService.getChatRoomDetails).toHaveBeenCalled();
			  expect(response).toEqual(mockChatRoom);
		})

	})

	describe('updateChatRoom', () => {
		it('updateChatRoom should be defined', () => {
			expect(controller.updateChatRoom).toBeDefined();
		})
		it('should update the chatRoom', async() => {
			const mockId = 'c_1';
      		const mockUpdateChatUserRoomDto :UpdateChatUserRoomDto = {
        		unreadMessage: 2
      		}
      		const mockChatRoom : ChatRoom = {
        			id: 'c_1',
        			lastMessageSender: 'a_1',
        			lastMessage: 'hi',
        			unreadMessage: 0,
        			users: [],
        			messages: [],
        			createdAt: new Date(),
        			createdBy: 'd_1',
        			updatedAt: new Date(),
        			updatedBy: 'e_1'
      		};
			chatRoomService.updateChatRoom.mockResolvedValue(mockChatRoom);
			const response = await controller.updateChatRoom(mockId, mockUpdateChatUserRoomDto);
			expect(chatRoomService.updateChatRoom).toBeDefined();
			expect(response).toEqual(mockChatRoom)
		})
	})

});
