import { CartEntity } from './cart.entity';

describe('CartEntity', () => {
	let cartEntity: CartEntity;

	beforeEach(() => {
		cartEntity = new CartEntity();
	});

	it('should be defined', () => {
		expect(cartEntity).toBeDefined();
	});

	it('should have all required properties after setting them', () => {
		// Set basic properties
		cartEntity.id = 'test-id';
		cartEntity.appointmentId = 'test-appointment-id';
		cartEntity.createdAt = new Date();
		cartEntity.updatedAt = new Date();
		cartEntity.createdBy = 'test-user';
		cartEntity.updatedBy = 'test-user';

		// Set relationship properties
		cartEntity.appointment = { id: 'test' } as any;
		cartEntity.invoice = [];

		expect(cartEntity).toHaveProperty('id');
		expect(cartEntity).toHaveProperty('appointmentId');
		expect(cartEntity).toHaveProperty('createdAt');
		expect(cartEntity).toHaveProperty('updatedAt');
		expect(cartEntity).toHaveProperty('createdBy');
		expect(cartEntity).toHaveProperty('updatedBy');
		expect(cartEntity).toHaveProperty('appointment');
		expect(cartEntity).toHaveProperty('invoice');
	});

	it('should allow setting and getting basic properties', () => {
		const testId = 'test-cart-uuid';
		const testAppointmentId = 'test-appointment-uuid';
		const testDate = new Date();
		const testUserId = 'test-user-uuid';

		cartEntity.id = testId;
		cartEntity.appointmentId = testAppointmentId;
		cartEntity.createdAt = testDate;
		cartEntity.updatedAt = testDate;
		cartEntity.createdBy = testUserId;
		cartEntity.updatedBy = testUserId;

		expect(cartEntity.id).toBe(testId);
		expect(cartEntity.appointmentId).toBe(testAppointmentId);
		expect(cartEntity.createdAt).toBe(testDate);
		expect(cartEntity.updatedAt).toBe(testDate);
		expect(cartEntity.createdBy).toBe(testUserId);
		expect(cartEntity.updatedBy).toBe(testUserId);
	});

	it('should allow appointmentId to be undefined', () => {
		cartEntity.appointmentId = undefined;
		expect(cartEntity.appointmentId).toBeUndefined();
	});

	it('should allow setting appointment relationship', () => {
		const mockAppointment = { id: 'appointment-uuid' } as any;
		cartEntity.appointment = mockAppointment;
		expect(cartEntity.appointment).toBe(mockAppointment);
		expect(cartEntity.appointment?.id).toBe('appointment-uuid');
	});

	it('should allow appointment to be undefined', () => {
		cartEntity.appointment = undefined;
		expect(cartEntity.appointment).toBeUndefined();
	});

	it('should allow setting invoice relationship as array', () => {
		const mockInvoice1 = { id: 'invoice-uuid-1' } as any;
		const mockInvoice2 = { id: 'invoice-uuid-2' } as any;

		cartEntity.invoice = [mockInvoice1, mockInvoice2];
		expect(cartEntity.invoice).toHaveLength(2);
		expect(cartEntity.invoice[0]).toBe(mockInvoice1);
		expect(cartEntity.invoice[1]).toBe(mockInvoice2);
		expect(cartEntity.invoice[0].id).toBe('invoice-uuid-1');
		expect(cartEntity.invoice[1].id).toBe('invoice-uuid-2');
	});

	it('should allow empty invoice array', () => {
		cartEntity.invoice = [];
		expect(cartEntity.invoice).toHaveLength(0);
		expect(Array.isArray(cartEntity.invoice)).toBe(true);
	});

	it('should handle relationship properties correctly', () => {
		// First set the properties so they exist
		cartEntity.appointment = { id: 'test' } as any;
		cartEntity.invoice = [];

		// Test that the relationship properties exist and can be accessed
		expect('appointment' in cartEntity).toBe(true);
		expect('invoice' in cartEntity).toBe(true);

		// Test that they can be set to undefined/empty
		cartEntity.appointment = undefined;
		cartEntity.invoice = [];

		expect(cartEntity.appointment).toBeUndefined();
		expect(cartEntity.invoice).toEqual([]);
	});
});
