import { Test, TestingModule } from '@nestjs/testing';
import { CartsController } from './carts.controller';
import { CartsService } from './carts.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CartEntity } from './entites/cart.entity';
import { CartItemEntity } from '../cart-items/entities/cart-item.entity';
import { RoleService } from '../roles/role.service';
import { AppointmentsService } from '../appointments/appointments.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('CartsController', () => {
	let controller: CartsController;
	let cartsService: jest.Mocked<CartsService>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [CartsController],
			providers: [
				{
					provide: CartsService,
					useValue: {
						createCart: jest.fn(),
						findCartById: jest.fn(),
						deleteCart: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(CartEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						softDelete: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(CartItemEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						find: jest.fn(),
						update: jest.fn()
					}
				},
				{
					provide: RoleService,
					useValue: {
						find: jest.fn(),
						findOne: jest.fn()
					}
				},
				{
					provide: AppointmentsService,
					useValue: {
						findOne: jest.fn(),
						deleteAppointment: jest.fn(),
						createAppointment: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<CartsController>(CartsController);
		cartsService = module.get(CartsService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('deleteCart', () => {
		const mockCartId = 'cart_uuid';

		it('should have deleteCart function', () => {
			expect(controller.deleteCart).toBeDefined();
		});

		it('should delete cart successfully', async () => {
			const mockResponse = {
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) deleted successfully'
			};

			cartsService.deleteCart.mockResolvedValue(mockResponse);

			const result = await controller.deleteCart(mockCartId);

			expect(result).toEqual(mockResponse);
			expect(cartsService.deleteCart).toHaveBeenCalledWith(mockCartId);
		});

		it('should handle errors gracefully', async () => {
			const errorMessage = 'Cart not found';
			cartsService.deleteCart.mockRejectedValue(new Error(errorMessage));

			await expect(controller.deleteCart(mockCartId)).rejects.toThrow(
				Error
			);
			expect(cartsService.deleteCart).toHaveBeenCalledWith(mockCartId);
		});

		it('should propagate service errors', async () => {
			const httpError = new HttpException(
				'Internal server error',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
			cartsService.deleteCart.mockRejectedValue(httpError);

			await expect(controller.deleteCart(mockCartId)).rejects.toThrow(
				HttpException
			);
			expect(cartsService.deleteCart).toHaveBeenCalledWith(mockCartId);
		});
	});
});
