import {
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Post,
	Put,
	Query,
	UseGuards,
	UsePipes,
	ValidationPipe,
	Req
} from '@nestjs/common';
import { ApiOkResponse, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicAlertsService } from './clinic-alerts.service';
import { CreateClinicAlertDto } from './dto/create-clinicAlerts.dto';
import { UpdateClinicAlertsDto } from './dto/update-clinicAlerts.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Clinic-alerts')
@Controller('clinic-alerts')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicAlertsController extends ApiDocumentationBase {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly clinicAlertsService: ClinicAlertsService
	) {
		super();
	}

	@ApiOkResponse({
		description: 'create clinic alerts',
		type: ClinicAlertsService
	})
	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR,Role.LAB_TECHNICIAN,Role.RECEPTIONIST)
	@UsePipes(new ValidationPipe())
	@TrackMethod('createClinicAlerts-clinic-alerts')
	async createClinicAlerts(
		@Body() createClinicAlertDto: CreateClinicAlertDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('creating clinic-alerts', {
				dto: createClinicAlertDto
			});
			return await this.clinicAlertsService.createClinicAlerts(
				createClinicAlertDto,
				req.user.brandId
			);
		} catch (error) {
			this.logger.error('failed to create Clinic-alerts', {
				error,
				createClinicAlertDto
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'get-clinic alerts',
		type: ClinicAlertsService
	})
	@Get(':clinicId')
	@Roles(Role.ADMIN, Role.DOCTOR,Role.LAB_TECHNICIAN,Role.RECEPTIONIST)
	@ApiParam({ name: 'clinicId', type: 'string' })
	@ApiQuery({ name: 'search', type: 'string', required: false })
	@TrackMethod('getClinicAlerts-clinic-alerts')
	async getClinicAlerts(
		@Param('clinicId') clinicId: string,
		@Query('search') search?: string
	) {
		try {
			this.logger.log('creating clinic-alerts', {
				id: clinicId
			});
			return await this.clinicAlertsService.getClinicAlerts(
				clinicId,
				search
			);
		} catch (error) {
			this.logger.error('failed to return Clinic-alerts', {
				error,
				clinicId
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'delete-clinic alerts',
		type: ClinicAlertsService
	})
	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR,Role.LAB_TECHNICIAN,Role.RECEPTIONIST)
	@TrackMethod('deleteClinicAlert-clinic-alerts')
	async deleteClinicAlert(@Param('id') id: string) {
		try {
			this.logger.log('Delete the clinic-alert', { id });

			return await this.clinicAlertsService.deleteClinicAlert(id);
		} catch (error) {
			this.logger.error('Error removing clinic-alert by ID', { error });

			throw new HttpException(
				'Error removing clinic-alert',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'delete-clinic alerts',
		type: ClinicAlertsService
	})
	@Put(':id')
	@Roles(Role.ADMIN, Role.DOCTOR,Role.LAB_TECHNICIAN,Role.RECEPTIONIST)
	@TrackMethod('updateClinicAlert-clinic-alerts')
	async updateClinicAlert(
		@Param('id') id: string,
		@Body() updateClinicAlertDto: UpdateClinicAlertsDto
	) {
		const updatedClinicAlert = this.clinicAlertsService.updateClinicAlerts(
			id,
			updateClinicAlertDto
		);
		this.logger.log('clinic-alert updated successfully', {
			clinicAlertId: id
		});
		return updatedClinicAlert;
	}
}
