"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrackMethod = TrackMethod;
const newrelic = require("newrelic");
function TrackMethod(name) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = function (...args) {
            const methodName = name || `${target.constructor.name}.${propertyKey}`;
            return newrelic.startSegment(methodName, true, async () => {
                try {
                    const result = await originalMethod.apply(this, args);
                    return result;
                }
                catch (error) {
                    newrelic.noticeError(error);
                    throw error;
                }
            });
        };
        return descriptor;
    };
}
//# sourceMappingURL=track-method.decorator.js.map