import {
	<PERSON>umn,
	CreateDate<PERSON><PERSON>umn,
	DeleteDateColumn,
	Entity,
	JoinColumn,
	OneToMany,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';

@Entity('carts')
export class CartEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'appointment_id', nullable: true })
	appointmentId?: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@OneToOne(() => AppointmentEntity, appointmentEntity => appointmentEntity)
	@JoinColumn({ name: 'appointment_id' })
	appointment?: AppointmentEntity;

	@OneToMany(() => InvoiceEntity, invoiceEntity => invoiceEntity.cart)
	invoice!: InvoiceEntity[];

	@DeleteDateColumn({
		name: 'deleted_at',
		type: 'timestamp with time zone',
		nullable: true
	})
	deletedAt?: Date;
}
