import { getAuth } from '@/app/services/identity.service';
import EmptyLayout from './EmptyLayout';
import PatientLayout from './PatientLayout';

const getLayoutConfig = ({ pathname }: { pathname: string }) => {
    // Paths that should use EmptyLayout
    const emptyLayoutPaths = [
        '/signin/pin',
        '/signin/forgot-password',
        '/onboard',
        '/signin/email',
        '/signedDoc',
        '/',
    ];

    if (
        emptyLayoutPaths.some((path) => pathname === path) ||
        pathname.startsWith('/signedDoc/') ||
        pathname.startsWith('/estimate-signed-doc/')
    ) {
        return { Layout: EmptyLayout };
    }

    return { Layout: PatientLayout };
};

export { getLayoutConfig };
