import {
	BadRequestException,
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { ClinicLabReport } from './entities/clinic-lab-report.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
	<PERSON><PERSON><PERSON>,
	ILike,
	IsNull,
	Not,
	Repository,
	EntityManager,
	DataSource
} from 'typeorm';
import { LabReport } from './entities/lab-report.entity';
import { CreateLabReportDto, UpdateLabReportDto } from './dto/lab-report.dto';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { uuidv4 } from 'uuidv7';
import { S3Service } from '../utils/aws/s3/s3.service';
import { UpdateStatusDto } from './dto/update-clinic-lab-report-status.dto';
import { <PERSON><PERSON>ogger } from '../utils/logger/winston-logger.service';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { CreateClinicLabReportDto } from './dto/create-clinic-lab-report.dto';
import { UpdateClinicLabReportDto } from './dto/update-clinic-lab-report.dto';
import {
	CreateDiagnosticNoteDto,
	DeleteDiagnosticNoteDto,
	EditDiagnosticNoteDto
} from './dto/create-update-diagnostic-notes.dto';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';

const MAX_FILE_SIZE = 1024 * 1024 * 1024; // 5MB in bytes
@Injectable()
export class ClinicLabReportService {
	constructor(
		@InjectRepository(ClinicLabReport)
		private readonly clinicLabReportRepository: Repository<ClinicLabReport>,
		@InjectRepository(AppointmentEntity)
		private appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentDetailsEntity)
		private readonly appointmentDetailsRepository: Repository<AppointmentDetailsEntity>,
		@InjectRepository(LabReport)
		private readonly labReportRepository: Repository<LabReport>,
		private readonly s3Service: S3Service,
		private readonly logger: WinstonLogger,
		private readonly appointmentGateway: AppointmentGateway,
		private readonly dataSource: DataSource
	) {}

	private async generateUniqueId(
		prefix: string,
		clinicId: string
	): Promise<string> {
		try {
			const count = await this.clinicLabReportRepository.count({
				where: { clinicId }
			});
			const nextNumber = count + 1;
			return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
		} catch (error) {
			this.logger.error('Error generating unique ID', { error });
			throw new InternalServerErrorException(
				'Failed to generate unique ID'
			);
		}
	}

	async getLabReports(
		clinicId: string,
		searchKeyword?: string,
		integrationType?: string
	): Promise<ClinicLabReport[]> {
		// If integrationType is passed as empty string - it searches for null values
		// If integrationType is passed as undefined - it searches for all values
		// If integrationType is passed as specific string - it searches for values with that specific string
		if (searchKeyword) {
			return this.clinicLabReportRepository.find({
				where: {
					name: ILike(`%${searchKeyword}%`),
					clinicId,
					deletedAt: IsNull(),
					...(integrationType !== undefined && {
						integrationType:
							integrationType === '' ? IsNull() : integrationType
					})
				}
			});
		}

		return this.clinicLabReportRepository.find({
			where: {
				clinicId,
				deletedAt: IsNull(),
				...(integrationType !== undefined && {
					integrationType:
						integrationType === '' ? IsNull() : integrationType
				})
			}
		});
	}

	async bulkInsert(items: CreateClinicLabReportDto[]): Promise<string> {
		try {
			this.logger.log(
				`Starting bulk insert for ${items.length} lab reports`
			);

			for (const item of items) {
				this.logger.log(
					`Processing item with name: ${item.name}, clinicId: ${item.clinicId}`
				);

				// Check if item exists for the clinic
				const existingLabReport =
					await this.clinicLabReportRepository.findOne({
						where: {
							clinicId: item.clinicId,
							name: item.name
						}
					});

				if (existingLabReport) {
					this.logger.log(
						`Found existing lab report with id: ${existingLabReport.id}, name: ${existingLabReport.name}`
					);
					this.logger.log('Current values:', existingLabReport);
					this.logger.log('New values:', item);

					// Update existing item
					Object.assign(existingLabReport, {
						...item,
						uniqueId: existingLabReport.uniqueId // Preserve the uniqueId
					});

					const updatedReport =
						await this.clinicLabReportRepository.save(
							existingLabReport
						);
					this.logger.log(
						`Updated lab report with id: ${updatedReport.id}`
					);
				} else {
					this.logger.log(
						`No existing lab report found for name: ${item.name}, creating new`
					);

					// Create new item with unique ID
					const uniqueId = await this.generateUniqueId(
						'LR-',
						item.clinicId
					);
					this.logger.log(`Generated new uniqueId: ${uniqueId}`);

					const newReport = await this.clinicLabReportRepository.save(
						{
							...item,
							uniqueId
						}
					);
					this.logger.log(
						`Created new lab report with id: ${newReport.id}`
					);
				}
			}

			const message = `Bulk insert of ${items.length} lab reports completed successfully`;
			this.logger.log(message);
			return message;
		} catch (error: unknown) {
			const err = error as Error;
			this.logger.error('Error during bulk insert of lab reports', {
				message: err.message,
				stack: err.stack,
				items: JSON.stringify(items)
			});
			throw new InternalServerErrorException(
				'Failed to perform bulk insert'
			);
		}
	}

	async findOneEntry(criteria: { name: string; clinicId: string }) {
		return this.clinicLabReportRepository.findOne({ where: criteria });
	}

	async findOne(id: string): Promise<ClinicLabReport | null> {
		try {
			return await this.clinicLabReportRepository.findOne({
				where: { id }
			});
		} catch (error) {
			this.logger.error('Error finding lab report entry', { error });
			throw error;
		}
	}

	/**
	 * Find lab report (not clinic lab report) by ID
	 * Used for getting IDEXX order details and integration information
	 * Returns minimal data needed for deletion flow
	 */
	async findLabReportById(id: string): Promise<any> {
		try {
			const labReport = await this.labReportRepository.findOne({
				where: { id },
				select: [
					'id',
					'integrationOrderId',
					'integrationDetails',
					'status',
					'lineItemId',
					'appointmentId',
					'patientId',
					'clinicLabReportId'
				]
			});

			if (!labReport) {
				return null;
			}

			// Parse integrationDetails if it's a string
			let parsedIntegrationDetails = labReport.integrationDetails;
			if (typeof labReport.integrationDetails === 'string') {
				try {
					parsedIntegrationDetails = JSON.parse(
						labReport.integrationDetails
					);
				} catch (parseError) {
					this.logger.warn('Failed to parse integrationDetails', {
						labReportId: id,
						integrationDetails: labReport.integrationDetails
					});
					parsedIntegrationDetails = {};
				}
			}

			return {
				...labReport,
				integrationDetails: parsedIntegrationDetails
			};
		} catch (error) {
			this.logger.error('Error finding lab report by ID', {
				error,
				labReportId: id
			});
			throw error;
		}
	}

	async deleteItem(itemId: string): Promise<void> {
		try {
			const result = await this.clinicLabReportRepository.update(itemId, {
				deletedAt: new Date()
			});
			if (result.affected === 0) {
				throw new NotFoundException(
					`Lab report with ID ${itemId} not found`
				);
			}
		} catch (error) {
			this.logger.error('Error deleting lab report item', { error });
			throw error;
		}
	}
	async create(
		createLabReportDto: CreateClinicLabReportDto,
		brandId: string
	): Promise<ClinicLabReport> {
		try {
			const uniqueId = await this.generateUniqueId(
				'LR-',
				createLabReportDto.clinicId
			);
			const newLabReport = this.clinicLabReportRepository.create({
				...createLabReportDto,
				uniqueId,
				brandId: brandId
			});

			const savedLabReport =
				await this.clinicLabReportRepository.save(newLabReport);

			if (!savedLabReport) {
				throw new InternalServerErrorException(
					'Failed to create lab report'
				);
			}

			return savedLabReport;
		} catch (error) {
			this.logger.error('Error creating lab report', { error });
			throw new InternalServerErrorException(
				'Failed to create lab report'
			);
		}
	}

	async update(
		id: string,
		updateLabReportDto: UpdateClinicLabReportDto
	): Promise<ClinicLabReport> {
		try {
			const labReport = await this.clinicLabReportRepository.findOne({
				where: { id }
			});
			if (!labReport) {
				throw new NotFoundException(
					`Lab report with ID ${id} not found`
				);
			}

			Object.assign(labReport, updateLabReportDto);
			return await this.clinicLabReportRepository.save(labReport);
		} catch (error) {
			this.logger.error('Error updating lab report', { error });
			throw error;
		}
	}

	async createOrUpdateLabReport(
		createLabReportDto: CreateLabReportDto,
		brandId: string,
		isCreate: boolean = false // Add new parameter with default value
	): Promise<LabReport> {
		const {
			appointmentId,
			clinicLabReportId,
			files,
			clinicId,
			status,
			patientId,
			lineItemId
		} = createLabReportDto;
		if (!lineItemId) {
			throw new BadRequestException('lineItemId is required');
		}

		files.forEach(file => {
			if (file.fileSize === undefined) {
				throw new BadRequestException(
					`File size is missing for "${file.fileName}".`
				);
			}
			if (file.fileSize > MAX_FILE_SIZE) {
				throw new BadRequestException(
					`File "${file.fileName}" exceeds the ${MAX_FILE_SIZE / (1024 * 1024)}MB size limit.`
				);
			}
		});

		const filesWithId = files.map(file => ({ ...file, id: uuidv4() }));

		const appointment = await this.appointmentRepository.findOne({
			where: { id: appointmentId }
		});
		if (!appointment) {
			throw new NotFoundException(
				`Appointment with ID "${appointmentId}" not found`
			);
		}

		const clinicLabReport = await this.clinicLabReportRepository.findOne({
			where: { id: clinicLabReportId }
		});
		if (!clinicLabReport) {
			throw new NotFoundException(`Clinic Lab Report not found`);
		}

		// Always find by lineItemId to ensure we're updating the correct instance
		let labReport = await this.labReportRepository.findOne({
			where: { lineItemId },
			relations: ['appointment', 'clinicLabReport']
		});

		if (labReport) {
			// Update existing lab report
			labReport.files = [...labReport.files, ...filesWithId];
			labReport.status = status;
		} else {
			// Create new lab report
			labReport = this.labReportRepository.create({
				appointment,
				clinicLabReport,
				clinicId,
				files: filesWithId,
				status,
				patientId,
				lineItemId,
				brandId,
				integrationDetails: createLabReportDto.integrationDetails,
				integrationOrderId: createLabReportDto.integrationOrderId
			});
		}

		const savedLabReport = await this.labReportRepository.save(labReport);

		// Only broadcast appointment details update if it's not a create operation
		// and it's not an IDEXX integrated item
		if (!isCreate && !savedLabReport.integrationDetails) {
			await this.updateAppointmentDetailsAndBroadcast(
				appointmentId,
				savedLabReport
			);
		}

		return savedLabReport;
	}

	async deleteFile(
		labReportId: string,
		fileId: string,
		lineItemId: string
	): Promise<LabReport> {
		const labReport = await this.labReportRepository.findOne({
			where: { id: labReportId, lineItemId }
		});

		if (!labReport) {
			throw new NotFoundException(
				`Lab report with ID "${labReportId}" and lineItemId "${lineItemId}" not found`
			);
		}

		const fileToDelete = labReport.files.find(file => file.id === fileId);

		if (!fileToDelete) {
			throw new NotFoundException(
				`File with ID "${fileId}" not found in lab report "${labReportId}"`
			);
		}

		try {
			// Check if the file has a fileUuid property (new format)
			let fileKey;
			if (fileToDelete.fileUuid) {
				// New format with UUID in the fileKey
				fileKey = `/labReports/${labReport.appointmentId}/${labReport.clinicLabReportId}/${lineItemId}/${fileToDelete.fileUuid}-${fileToDelete.fileName}`;
			} else if (fileToDelete.fileKey) {
				// If the fileKey is already stored, use it directly
				fileKey = fileToDelete.fileKey;
			} else {
				// Original format without UUID (backward compatibility)
				fileKey = `/labReports/${labReport.appointmentId}/${labReport.clinicLabReportId}/${lineItemId}/${fileToDelete.fileName}`;
			}

			await this.s3Service.deleteFile(fileKey);

			// Remove file from the lab report
			labReport.files = labReport.files.filter(
				file => file.id !== fileId
			);

			// Save the updated lab report
			const savedLabReport =
				await this.labReportRepository.save(labReport);

			// non IDEXX items only
			if (!savedLabReport.integrationDetails) {
				await this.updateAppointmentDetailsAndBroadcast(
					labReport.appointmentId,
					savedLabReport
				);
			}

			return savedLabReport;
		} catch (error: any) {
			throw new BadRequestException(
				`Failed to delete file: ${error.message}`
			);
		}
	}

	async deleteLabReport(
		clinicLabReportId: string,
		appointmentId: string,
		lineItemId: string
	): Promise<void> {
		const labReport = await this.labReportRepository.findOne({
			where: {
				clinicLabReportId,
				appointmentId,
				lineItemId // Add lineItemId to find the exact instance
			}
		});

		if (!labReport) {
			throw new NotFoundException(
				`Lab report with ID "${clinicLabReportId}" not found`
			);
		}

		try {
			for (const file of labReport.files) {
				// Determine the proper fileKey using the same logic as in deleteFile method
				let fileKey;
				if (file.fileUuid) {
					// New format with UUID in the fileKey
					fileKey = `/labReports/${appointmentId}/${clinicLabReportId}/${lineItemId}/${file.fileUuid}-${file.fileName}`;
				} else if (file.fileKey) {
					// If the fileKey is already stored, use it directly
					fileKey = file.fileKey;
				} else {
					// Original format without UUID (backward compatibility)
					fileKey = `/labReports/${appointmentId}/${clinicLabReportId}/${lineItemId}/${file.fileName}`;
				}
				await this.s3Service.deleteFile(fileKey);
			}

			await this.labReportRepository.remove(labReport);

			// Update appointment.details.objective.labReports to remove the deleted lab report
			await this.removeLabReportFromAppointmentDetailsAndBroadcast(
				appointmentId,
				lineItemId
			);
		} catch (error) {
			throw new InternalServerErrorException(
				`Failed to delete lab report`
			);
		}
	}

	async getLabReportsForClinic(
		clinicId: string,
		page: number = 1,
		limit: number = 10,
		startDate?: Date,
		endDate?: Date,
		searchTerm?: string,
		status: string = 'PENDING'
	) {
		console.log({ clinicId });

		const queryBuilder = this.labReportRepository
			.createQueryBuilder('labReport')
			.leftJoinAndSelect('labReport.appointment', 'appointment')
			.leftJoinAndSelect('appointment.patient', 'patient')
			.leftJoinAndSelect('patient.patientOwners', 'patientOwners')
			.leftJoinAndSelect('patientOwners.ownerBrand', 'ownerBrand')
			.leftJoinAndSelect(
				'appointment.appointmentDoctors',
				'appointmentDoctors'
			)
			.leftJoinAndSelect('appointmentDoctors.clinicUser', 'doctor')
			.leftJoinAndSelect('doctor.user', 'user')
			.leftJoinAndSelect('labReport.clinicLabReport', 'clinicLabReport')
			.leftJoinAndSelect(
				'labReport.diagnosticNotesData',
				'diagnosticNotesData'
			)
			.select([
				'labReport.id',
				'labReport.createdAt',
				'labReport.files',
				'labReport.diagnosticNotes',
				'labReport.updatedAt',
				'labReport.status',
				'labReport.lineItemId',
				'appointment.id',
				'appointment.startTime',
				'patient.id',
				'patient.patientName',
				'patient.breed',
				'patient.species',
				'patientOwners.id',
				'patientOwners.isPrimary',
				'ownerBrand.id',
				'ownerBrand.firstName',
				'ownerBrand.lastName',
				'appointmentDoctors.id',
				'appointmentDoctors.primary',
				'doctor.id',
				'user.id',
				'user.firstName',
				'user.lastName',
				'clinicLabReport.id',
				'clinicLabReport.name',
				'labReport.integrationOrderId',
				'diagnosticNotesData'
			])
			.where('labReport.clinicId = :clinicId', { clinicId })
			.andWhere('labReport.status =:status', { status });

		if (status === 'COMPLETED') {
			queryBuilder.andWhere(
				"labReport.updatedAt >= NOW() - INTERVAL '24 hours'"
			);
		}

		if (searchTerm) {
			queryBuilder.andWhere(
				new Brackets(qb => {
					qb.where('patient.patientName ILIKE :searchTerm', {
						searchTerm: `%${searchTerm}%`
					})
						.orWhere('user.firstName ILIKE :searchTerm', {
							searchTerm: `%${searchTerm}%`
						})
						.orWhere('user.lastName ILIKE :searchTerm', {
							searchTerm: `%${searchTerm}%`
						})
						.orWhere('clinicLabReport.name ILIKE :searchTerm', {
							searchTerm: `%${searchTerm}%`
						})
						.orWhere(
							"concat(user.firstName, ' ', user.lastName) ILIKE :searchTerm",
							{ searchTerm: `%${searchTerm}%` }
						);
				})
			);
		}

		const [data, total] = await queryBuilder
			.skip((page - 1) * limit)
			.take(limit)
			.orderBy('labReport.createdAt', 'DESC')
			.getManyAndCount();
		const formattedData = data.map((item: LabReport) => {
			return {
				...item,
				appointment: {
					...item.appointment,
					appointmentDoctors: item.appointment.appointmentDoctors.map(
						(ad: AppointmentDoctorsEntity) => {
							return {
								id: ad.id,
								doctor: {
									firstName: ad.clinicUser.user.firstName,
									lastName: ad.clinicUser.user.lastName,
									primary: ad.primary,
									id: ad.clinicUser.user.id
								}
							};
						}
					)
				}
			};
		});
		return {
			data: formattedData,
			total
		};
	}

	async getLabReportForPatient(patientId: string) {
		const reports = await this.labReportRepository.find({
			where: { patientId },
			relations: [
				'appointment',
				'appointment.patient',
				'appointment.patient.patientOwners',
				'appointment.patient.patientOwners.ownerBrand',
				'appointment.appointmentDoctors',
				'appointment.appointmentDoctors.clinicUser',
				'appointment.appointmentDoctors.clinicUser.user',
				'clinicLabReport'
			]
		});

		// Sort by: 1) appointment date DESC, 2) appointment startTime (HH:MM) DESC, 3) labReport createdAt DESC
		reports.sort((a, b) => {
			// First sort by appointment date (DESC)
			const dateA = new Date(a.appointment?.date || 0);
			const dateB = new Date(b.appointment?.date || 0);
			if (dateA.getTime() !== dateB.getTime()) {
				return dateB.getTime() - dateA.getTime();
			}

			// Second sort by appointment startTime (HH:MM part only) DESC
			const startTimeA = a.appointment?.startTime
				? new Date(a.appointment.startTime)
				: new Date('1970-01-01T00:00:00');
			const startTimeB = b.appointment?.startTime
				? new Date(b.appointment.startTime)
				: new Date('1970-01-01T00:00:00');
			const timeA = startTimeA.toTimeString().substring(0, 5); // Extract HH:MM
			const timeB = startTimeB.toTimeString().substring(0, 5); // Extract HH:MM
			if (timeA !== timeB) {
				return timeB.localeCompare(timeA);
			}

			// Third sort by labReport createdAt DESC
			const createdAtA = new Date(a.createdAt || 0);
			const createdAtB = new Date(b.createdAt || 0);
			return createdAtB.getTime() - createdAtA.getTime();
		});
		const formatterLabReports = reports.map((report: LabReport) => {
			return {
				...report,
				appointment: {
					...report.appointment,
					appointmentDoctors:
						report.appointment.appointmentDoctors.map(ad => ({
							id: ad.id,
							appointmentId: ad.appointmentId,
							doctorId: ad.clinicUser.id,
							primary: ad.primary,
							doctor: {
								id: ad.clinicUser.id,
								firstName: ad.clinicUser.user.firstName,
								lastName: ad.clinicUser.user.lastName,
								email: ad.clinicUser.user.email
							}
						}))
				}
			};
		});
		return formatterLabReports;
	}
	async updateLabReportStatus(updateStatusDto: UpdateStatusDto) {
		const { id, status } = updateStatusDto;

		const labReport = await this.labReportRepository.findOne({
			where: { id }
		});

		if (!labReport) {
			throw new NotFoundException(
				'No lab reports found with the given ID'
			);
		}

		labReport.status = status;
		labReport.updatedAt = new Date();
		await this.labReportRepository.save(labReport);

		return labReport;
	}

	async deleteLabReportsByAppointmentId(appointmentId: string) {
		const labReports = await this.labReportRepository.find({
			where: {
				appointmentId
			}
		});

		if (labReports.length) {
			await Promise.all(
				labReports.map(async (report: LabReport) => {
					if (report.files.length) {
						await Promise.all(
							report.files.map(async (file: any) => {
								// Determine the proper fileKey using our standard resolution logic
								let fileKey;
								if (file.fileKey) {
									// If the fileKey is already stored, use it directly
									fileKey = file.fileKey;
								} else if (file.fileUuid) {
									// New format with UUID in the fileKey
									fileKey = `/labReports/${appointmentId}/${report.clinicLabReportId}/${report.lineItemId}/${file.fileUuid}-${file.fileName}`;
								} else {
									// Original format without UUID (backward compatibility)
									fileKey = `/labReports/${appointmentId}/${report.clinicLabReportId}/${report.lineItemId}/${file.fileName}`;
								}
								return this.s3Service.deleteFile(fileKey);
							})
						);
					}
				})
			);
		}

		return await this.labReportRepository.delete({ appointmentId });
	}

	async updateItemByIdexxOrderId(idexxOrderId: string, s3Data: any) {
		console.log('idexxOrderId = ', idexxOrderId);

		const result = await this.labReportRepository.findOne({
			where: {
				integrationOrderId: idexxOrderId
			}
		});

		if (result) {
			const currentDetails =
				typeof result.integrationDetails === 'string'
					? JSON.parse(result.integrationDetails)
					: result.integrationDetails;

			const updatedIntegrationDetails = {
				...currentDetails,
				fileKey: s3Data.fileKey
			};

			// Update the result object
			result.integrationDetails = updatedIntegrationDetails;
			result.files = [
				{
					id: uuidv4(),
					s3Url: '',
					fileKey: s3Data.fileKey,
					fileName: s3Data.fileName,
					fileSize: 0,
					uploadDate: ''
				}
			];
			result.status = 'COMPLETED';

			// Save the changes to database
			const updatedData = await this.labReportRepository.save(result);

			return updatedData;
		} else {
			console.log('could not find the data');
		}

		return null;
	}

	async updateClinicLabReport(
		id: string,
		updateLabportDto: UpdateLabReportDto
	): Promise<ClinicLabReport> {
		const { price, tax } = updateLabportDto;

		const labReport = await this.clinicLabReportRepository.findOne({
			where: { id }
		});

		if (!labReport) {
			throw new NotFoundException(
				'No clinic lab reports found with the given ID'
			);
		}

		labReport.chargeablePrice = price;
		labReport.tax = tax;
		await this.clinicLabReportRepository.save(labReport);

		return labReport;
	}

	async getIdexxLabReports(): Promise<LabReport[]> {
		this.logger.log('Fetching all pendfing idexx lab reports');
		let labReports: LabReport[] = [];
		try {
			labReports = await this.labReportRepository.find({
				where: { integrationOrderId: Not(IsNull()), status: 'PENDING' },
				relations: ['clinic', 'clinic.idexx']
			});
		} catch (error: any) {
			console.log('Error in fetching lab reports', error);

			this.logger.error('Error in fetching idexx reports', error);
		}
		return labReports;
	}

	async handleCreateDiagnosticNotes(
		appointmentId: string,
		operation: 'insert' | 'edit' | 'delete',
		note:
			| CreateDiagnosticNoteDto
			| EditDiagnosticNoteDto
			| DeleteDiagnosticNoteDto
	) {
		const labReports = await this.labReportRepository.find({
			where: { appointmentId }
		});
		if (!labReports) {
			throw new NotFoundException('Lab report not found');
		}

		let labReport;
		if (operation === 'insert') {
			const notes = note as CreateDiagnosticNoteDto;
			labReport = labReports.find(
				report => report.id === notes.labReportId
			);
		} else {
			labReport = labReports.find(report =>
				report.diagnosticNotes?.some(
					diagnostic => diagnostic.id === note.id
				)
			);
		}

		if (!labReport) {
			throw new NotFoundException(
				'Lab report with matching diagnostic note not found'
			);
		}

		let diagnosticNotes = labReport?.diagnosticNotes || [];
		switch (operation) {
			case 'insert':
				const insertNote = note as CreateDiagnosticNoteDto;

				if (
					diagnosticNotes.some(
						existingNote => existingNote.id === insertNote.id
					)
				) {
					throw new BadRequestException(
						'A diagnostic note with this ID already exists'
					);
				}

				diagnosticNotes.push(insertNote);
				break;

			case 'edit':
				const editNote = note as EditDiagnosticNoteDto;
				const noteIndex = diagnosticNotes.findIndex(
					existingNote => existingNote.id === editNote.id
				);
				if (noteIndex === -1) {
					throw new NotFoundException(
						'Diagnostic note not found for editing'
					);
				}

				diagnosticNotes[noteIndex] = {
					...diagnosticNotes[noteIndex],
					...editNote,
					data: {
						...diagnosticNotes[noteIndex].data,
						...editNote.data
					}
				};
				break;

			case 'delete':
				const deleteNote = note as DeleteDiagnosticNoteDto;
				const filteredNotes = diagnosticNotes.filter(
					existingNote => existingNote.id !== deleteNote.id
				);
				if (filteredNotes.length === diagnosticNotes.length) {
					throw new NotFoundException(
						'Diagnostic note not found for deletion'
					);
				}

				diagnosticNotes = filteredNotes;
				break;

			default:
				throw new BadRequestException('Invalid operation');
		}
		labReport.diagnosticNotes = diagnosticNotes;

		// Update appointment.details.objective.labReports and broadcast via socket
		await this.updateAppointmentDetailsAndBroadcast(
			appointmentId,
			labReport
		);

		return this.labReportRepository.save(labReport);
	}

	/**
	 * Helper method to update appointment.details.objective.labReports and broadcast changes via socket
	 * Uses selective updates to only modify changed fields instead of recreating entire objects.
	 */
	private async updateAppointmentDetailsAndBroadcast(
		appointmentId: string,
		updatedLabReport: LabReport
	): Promise<void> {
		// Log the start of the process
		this.logger.log(
			`Attempting to update appointment details and broadcast for appointment ${appointmentId}`,
			{ appointmentId, updatedLabReportId: updatedLabReport.id }
		);
		try {
			// Fetch the appointment with appointment details relation
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId },
				relations: ['appointmentDetails']
			});

			// If appointment not found, log a warning and return
			if (!appointment) {
				this.logger.warn(
					`Appointment ${appointmentId} not found for socket broadcast. Aborting update and broadcast.`,
					{ appointmentId }
				);
				return;
			}

			// Get current appointment details or initialize if not exists
			const currentDetails =
				appointment.appointmentDetails?.details || {};
			const currentObjective = (currentDetails as any).objective || {};
			const currentLabReports = currentObjective.labReports || [];

			// Debug logging to understand current state
			this.logger.log(
				`Current appointment details state for appointment ${appointmentId}`,
				{
					appointmentId,
					hasAppointmentDetails: !!appointment.appointmentDetails,
					appointmentDetailsId: appointment.appointmentDetails?.id,
					currentLabReportsCount: currentLabReports.length,
					updatedLabReportLineItemId: updatedLabReport.lineItemId
				}
			);

			// Find the specific lab report in the array based on lineItemId
			const labReportIndex = currentLabReports.findIndex(
				(report: any) =>
					report.lineItemId === updatedLabReport.lineItemId
			);

			// Debug logging for diagnostic notes preservation
			if (labReportIndex >= 0) {
				const existingReport = currentLabReports[labReportIndex];
				this.logger.log(
					`Diagnostic notes preservation check for lab report ${updatedLabReport.id}`,
					{
						appointmentId,
						lineItemId: updatedLabReport.lineItemId,
						existingDiagnosticNotesCount:
							existingReport.diagnosticNotes?.length || 0,
						updatedLabReportDiagnosticNotesCount:
							updatedLabReport.diagnosticNotes?.length || 0,
						willPreserveExisting:
							!updatedLabReport.diagnosticNotes &&
							existingReport.diagnosticNotes?.length > 0
					}
				);
			}

			let updatedLabReports;
			if (labReportIndex >= 0) {
				// Update existing lab report - only modify changed fields
				updatedLabReports = [...currentLabReports];
				const existingReport = updatedLabReports[labReportIndex];

				// Selectively update only the fields that might have changed
				updatedLabReports[labReportIndex] = {
					...existingReport, // Preserve existing fields
					// Only update fields that are likely to change
					files: updatedLabReport.files,
					// Preserve existing diagnostic notes unless explicitly updated
					diagnosticNotes:
						updatedLabReport.diagnosticNotes ||
						existingReport.diagnosticNotes ||
						[],
					// Update label if clinic lab report name changed
					...(updatedLabReport.clinicLabReport?.name && {
						label: updatedLabReport.clinicLabReport.name
					}),
					// Update price if it changed
					...(updatedLabReport.clinicLabReport?.chargeablePrice !==
						undefined && {
						chargeablePrice:
							updatedLabReport.clinicLabReport.chargeablePrice
					})
				};
			} else {
				// Add new lab report to the array - only create full object for new entries
				const newLabReportForDetails = {
					labReportId: updatedLabReport.id,
					lineItemId: updatedLabReport.lineItemId,
					label: updatedLabReport.clinicLabReport?.name || '',
					value: updatedLabReport.clinicLabReportId,
					files: updatedLabReport.files,
					diagnosticNotes: updatedLabReport.diagnosticNotes || [],
					chargeablePrice:
						updatedLabReport.clinicLabReport?.chargeablePrice || 0
				};
				updatedLabReports = [
					...currentLabReports,
					newLabReportForDetails
				];
			}

			// Construct the updated appointment details object - preserve other objective fields
			const updatedDetails = {
				...currentDetails,
				objective: {
					...currentObjective,
					labReports: updatedLabReports
				}
			};

			// Debug log the data being saved
			this.logger.log(
				`Preparing to save appointment details for appointment ${appointmentId}`,
				{
					appointmentId,
					labReportIndex,
					isNewLabReport: labReportIndex < 0,
					updatedLabReportsCount: updatedLabReports.length,
					updatedFields:
						labReportIndex >= 0 ? 'selective_update' : 'new_entry'
				}
			);

			// Update or create the appointment details entity
			if (appointment.appointmentDetails) {
				// Update existing details - save the appointmentDetails entity directly
				appointment.appointmentDetails.details = updatedDetails;
				const savedDetails =
					await this.appointmentDetailsRepository.save(
						appointment.appointmentDetails
					);
				this.logger.log(
					`Updated existing appointment details for appointment ${appointmentId}. Details ID: ${savedDetails.id}`,
					{
						appointmentId,
						detailsId: savedDetails.id,
						updatedLabReportsCount: updatedLabReports.length
					}
				);

				// Verify the save by fetching the data back
				const verifyDetails =
					await this.appointmentDetailsRepository.findOne({
						where: { id: savedDetails.id }
					});
				const verifyObjective =
					(verifyDetails?.details as any)?.objective || {};
				const verifyLabReports = verifyObjective.labReports || [];
				this.logger.log(
					`Verification: Fetched appointment details after save`,
					{
						appointmentId,
						detailsId: savedDetails.id,
						verifyLabReportsCount: verifyLabReports.length,
						hasTargetLineItem: verifyLabReports.some(
							(r: any) =>
								r.lineItemId === updatedLabReport.lineItemId
						)
					}
				);
			} else {
				// Create new details if none exist
				const newAppointmentDetails =
					this.appointmentDetailsRepository.create({
						appointmentId,
						details: updatedDetails
					});
				const savedDetails =
					await this.appointmentDetailsRepository.save(
						newAppointmentDetails
					);
				this.logger.log(
					`Created new appointment details for appointment ${appointmentId}. Details ID: ${savedDetails.id}`,
					{
						appointmentId,
						detailsId: savedDetails.id,
						updatedLabReportsCount: updatedLabReports.length
					}
				);
			}

			// Prepare payload and broadcast the change via socket
			const broadcastPayload = {
				appointmentId: appointmentId,
				key: 'objective.labReports',
				value: updatedLabReports
			};
			this.logger.log(
				`Broadcasting appointment update via socket for appointment ${appointmentId}.`,
				{ appointmentId }
			);
			await this.appointmentGateway.publishAppointmentUpdate(
				appointmentId,
				broadcastPayload
			);

			// Log successful completion
			this.logger.log(
				`Successfully updated appointment details and broadcasted for appointment ${appointmentId}`
			);
		} catch (error) {
			// Log errors without throwing to avoid disrupting the main flow
			this.logger.error(
				'Error updating appointment details and broadcasting',
				{
					appointmentId,
					error:
						error instanceof Error ? error.message : String(error),
					stack: error instanceof Error ? error.stack : undefined
				}
			);
		}
	}

	/**
	 * Helper method to remove a lab report from appointment.details.objective.labReports and broadcast changes via socket
	 */
	private async removeLabReportFromAppointmentDetailsAndBroadcast(
		appointmentId: string,
		lineItemId: string
	): Promise<void> {
		try {
			// Fetch the appointment with appointment details relation
			const appointment = await this.appointmentRepository.findOne({
				where: { id: appointmentId },
				relations: ['appointmentDetails']
			});

			if (!appointment || !appointment.appointmentDetails) {
				this.logger.warn(
					`Appointment ${appointmentId} or appointment details not found for lab report removal`
				);
				return;
			}

			// Get current appointment details
			const currentDetails = appointment.appointmentDetails.details || {};
			const currentObjective = (currentDetails as any).objective || {};
			const currentLabReports = currentObjective.labReports || [];

			// Remove the lab report from the array
			const updatedLabReports = currentLabReports.filter(
				(report: any) => report.lineItemId !== lineItemId
			);

			// Update appointment details
			const updatedDetails = {
				...currentDetails,
				objective: {
					...currentObjective,
					labReports: updatedLabReports
				}
			};

			appointment.appointmentDetails.details = updatedDetails;
			await this.appointmentDetailsRepository.save(
				appointment.appointmentDetails
			);

			// Broadcast the change via socket
			await this.appointmentGateway.publishAppointmentUpdate(
				appointmentId,
				{
					key: 'objective.labReports',
					value: updatedLabReports
				}
			);

			this.logger.log(
				`Removed lab report with lineItemId ${lineItemId} from appointment ${appointmentId} and broadcasted`
			);
		} catch (error) {
			this.logger.error(
				'Error removing lab report from appointment details and broadcasting',
				{
					appointmentId,
					lineItemId,
					error:
						error instanceof Error ? error.message : String(error)
				}
			);
			// Don't throw error to avoid breaking the main operation
		}
	}

	/**
	 * Mark lab reports as removed from invoice by appointment ID and lab report ID
	 * Used when specific lab report items are removed from an invoice
	 */
	async markAsRemovedFromInvoiceByAppointmentAndLabReportId(
		appointmentId: string,
		labReportId: string,
		entityManager?: EntityManager
	): Promise<void> {
		try {
			const manager = entityManager || this.dataSource.manager;

			// Find lab reports to mark as removed
			const labReports = await manager.find(LabReport, {
				where: {
					appointmentId,
					id: labReportId,
					removedFromInvoice: false
				}
			});

			this.logger.log(
				'Found specific lab reports to mark as removed from invoice',
				{
					appointmentId,
					labReportId,
					count: labReports.length
				}
			);

			if (labReports.length === 0) {
				this.logger.log(
					'No specific lab reports found to mark as removed',
					{
						appointmentId,
						labReportId
					}
				);
				return;
			}

			// Mark as removed from invoice
			await manager.update(
				LabReport,
				{
					appointmentId,
					id: labReportId
				},
				{
					removedFromInvoice: true
				}
			);

			this.logger.log(
				'Marked specific lab reports as removed from invoice',
				{
					appointmentId,
					labReportId,
					markedCount: labReports.length
				}
			);
		} catch (error) {
			this.logger.error(
				'Error marking specific lab reports as removed from invoice',
				{
					appointmentId,
					labReportId,
					error:
						error instanceof Error ? error.message : String(error)
				}
			);
			throw error;
		}
	}
}
