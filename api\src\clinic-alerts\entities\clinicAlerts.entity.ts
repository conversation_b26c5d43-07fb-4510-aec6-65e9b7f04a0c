import {
	Column,
	CreateDateColumn,
	<PERSON>tity,
	<PERSON>in<PERSON><PERSON>umn,
	ManyToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';

@Entity('clinic_alerts')
export class ClinicAlerts {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ name: 'clinic_id', type: 'uuid' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ name: 'alert_name', type: 'varchar' })
	alertName!: string;

	@Column({ type: 'varchar' })
	severity!: string;

	@Column({ name: 'created_by', type: 'uuid' })
	createdBy?: string;

	@Column({ name: 'updated_by', type: 'uuid' })
	updatedBy?: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt?: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt?: Date;

	@ManyToOne(() => ClinicEntity, clinic => clinic.id)
	@JoinColumn({ name: 'clinic_id' })
	clinic!: ClinicEntity;
}
