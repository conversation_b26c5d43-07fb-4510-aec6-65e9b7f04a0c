import { Test, TestingModule } from '@nestjs/testing';
import { AppointmentAssessmentService } from './appointment-assessment.service';
import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { CreateAppointmentAssessmentDto } from './dto/create-appointment-assessment.dto';

describe('AppointmentAssessmentService', () => {
	let service: AppointmentAssessmentService;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let repository: Repository<AppointmentAssessmentEntity>;
	const clinicId = 'clinicId';
	const brandId = 'brandId';
	const mockAppointmentAssessmentRepository = {
		find: jest.fn(),
		save: jest.fn()
	};

	const appointmentAssessments: AppointmentAssessmentEntity[] = [
		{
			id: 1,
			name: 'Respiratory Report',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			isAddedByUser: false,
			clinicId,
			brandId
		},
		{
			id: 2,
			name: 'X-ray Report',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			isAddedByUser: false,
			clinicId,
			brandId
		}
	];

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				AppointmentAssessmentService,
				{
					provide: getRepositoryToken(AppointmentAssessmentEntity),
					useValue: mockAppointmentAssessmentRepository
				}
			]
		}).compile();

		service = module.get<AppointmentAssessmentService>(
			AppointmentAssessmentService
		);
		repository = module.get<Repository<AppointmentAssessmentEntity>>(
			getRepositoryToken(AppointmentAssessmentEntity)
		);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('findAll', () => {
		it('should return an array of appointment assessments without a search keyword', async () => {
			mockAppointmentAssessmentRepository.find.mockResolvedValue(
				appointmentAssessments
			);

			const result = await service.findAll(undefined, clinicId);
			expect(result).toEqual(appointmentAssessments);
			expect(
				mockAppointmentAssessmentRepository.find
			).toHaveBeenCalledWith({
				where: { clinicId }
			});
		});

		it('should return an array of appointment assessments with a search keyword', async () => {
			const searchKeyword = 'Blood Test';
			const filteredAssessments = appointmentAssessments.filter(
				assessment => assessment.name.includes(searchKeyword)
			);

			mockAppointmentAssessmentRepository.find.mockResolvedValue(
				filteredAssessments
			);

			const result = await service.findAll(searchKeyword, clinicId);
			expect(result).toEqual(filteredAssessments);
			expect(
				mockAppointmentAssessmentRepository.find
			).toHaveBeenCalledWith({
				where: {
					name: ILike(`%${searchKeyword}%`),
					//isAddedByUser: false,
					clinicId
				}
			});
		});
	});

	describe('Create New assessment (user added)', () => {
		const mockNewAssessmentDto: CreateAppointmentAssessmentDto = {
			name: 'New Assessment',
			isAddedByUser: true
		};

		const mockNewAssessmentEntity: AppointmentAssessmentEntity = {
			id: 1001,
			name: 'New Assessment',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			isAddedByUser: true,
			clinicId: clinicId,
			brandId: brandId
		};

		it('should create a new assessment (user added)', async () => {
			mockAppointmentAssessmentRepository.save.mockReturnValue(
				mockNewAssessmentEntity
			);
			const result = await service.createNewAssessment(
				mockNewAssessmentDto,
				clinicId,
				brandId
			);
			expect(result).toEqual(mockNewAssessmentEntity);
			expect(
				mockAppointmentAssessmentRepository.save
			).toHaveBeenCalledWith({
				...mockNewAssessmentDto,
				clinicId,
				brandId
			});
		});

		it('should return error if saving of assessment fails', async () => {
			const errorMessage = 'Failed in creating assessment.';
			const error = new Error(errorMessage);

			// Mock the save method to return a rejected promise with the error
			mockAppointmentAssessmentRepository.save.mockRejectedValue(error);

			await expect(
				service.createNewAssessment(
					mockNewAssessmentDto,
					clinicId,
					brandId
				)
			).rejects.toThrow(errorMessage);

			expect(
				mockAppointmentAssessmentRepository.save
			).toHaveBeenCalledWith({
				...mockNewAssessmentDto,
				clinicId,
				brandId
			});
		});
	});
});
