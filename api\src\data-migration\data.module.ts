import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataController } from './data.controller';
import { DataService } from './data.service';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { Patient } from '../patients/entities/patient.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { S3Service } from '../utils/aws/s3/s3.service';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			GlobalOwner,
			OwnerBrand,
			Patient,
			PatientOwner,
			AppointmentEntity,
			AppointmentDoctorsEntity,
			AppointmentDetailsEntity,
			InvoiceEntity,
			CartEntity,
			PaymentDetailsEntity,
			ClinicLabReport,
			LabReport
		])
	],
	controllers: [DataController],
	providers: [DataService, WinstonLogger, S3Service]
})
export class DataModule {}
