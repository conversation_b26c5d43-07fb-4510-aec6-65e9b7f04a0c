import { validate } from 'class-validator';
import { CreateClinicDto } from './create-clinic.dto';

describe('CreateClinicDto', () => {
	let dto: CreateClinicDto;

	beforeEach(() => {
		dto = new CreateClinicDto();
		dto.name = 'ABC Clinic';
		dto.brandId = 'brand123';
		dto.adminFirstName = 'John';
		dto.adminLastName = 'Doe';
		dto.adminEmail = '<EMAIL>';
		dto.adminMobile = '1234567890';
	});

	it('should validate the CreateClinicDto successfully', async () => {
		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should fail if the name is empty', async () => {
		dto.name = '';
		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints?.isNotEmpty).toEqual(
			'The clinic should have a name.'
		);
	});

	it('should fail if the brandId is missing', async () => {
		dto.brandId = '';
		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints?.isNotEmpty).toEqual(
			'brandId should not be empty'
		);
	});

	it('should fail if adminFiestName is empty', async () => {
		dto.adminFirstName = '';
		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints?.isNotEmpty).toEqual(
			'The Admin should have a first name.'
		);
	});
	it('should fail if adminLastName is empty', async () => {
		dto.adminLastName = '';
		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints?.isNotEmpty).toEqual(
			'The Admin should have a last name.'
		);
	});

	it('should fail if adminEmail is invalid', async () => {
		dto.adminEmail = 'invalid-email';
		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints?.isEmail).toEqual(
			'adminEmail must be an email'
		);
	});

	it('should fail if adminMobile is not a valid phone number', async () => {
		dto.adminMobile = 'invalid-phone';
		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints?.isMobilePhone).toEqual(
			'adminMobile must be a phone number'
		);
	});
});
