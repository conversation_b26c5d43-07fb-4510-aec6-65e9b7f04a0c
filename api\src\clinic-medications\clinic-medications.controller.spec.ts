import { Test, TestingModule } from '@nestjs/testing';
import { ClinicMedicationsController } from './clinic-medications.controller';
import { ClinicMedicationsService } from './clinic-medications.service';
import { ClinicMedicationEntity } from './entities/clinic-medication.entity';
import { CreateClinicMedicationDto } from './dto/create-clinic-medication.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { RoleService } from '../roles/role.service';
import { UpdateClinicMedicationDto } from './dto/update-clinic-medication.dto';

describe('ClinicMedicationsController', () => {
	let controller: ClinicMedicationsController;
	let service: ClinicMedicationsService;
	let logger: WinstonLogger;

	const mockClinicMedicationsService = {
		getMedications: jest.fn(),
		createNewMedication: jest.fn(),
		findOne: jest.fn(),
		update: jest.fn(),
		remove: jest.fn()
	};

	const mockLogger = {
		log: jest.fn(),
		error: jest.fn()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicMedicationsController],
			providers: [
				{
					provide: ClinicMedicationsService,
					useValue: mockClinicMedicationsService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				}
			]
		}).compile();

		controller = module.get<ClinicMedicationsController>(
			ClinicMedicationsController
		);
		service = module.get<ClinicMedicationsService>(
			ClinicMedicationsService
		);
		logger = module.get<WinstonLogger>(WinstonLogger);

		// Reset all mocks before each test
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('getClinicMedications', () => {
		const mockMedications: ClinicMedicationEntity[] = [
			{
				id: '1',
				name: 'Medication 1',
				clinicId: 'clinic1',
				isAddedByUser: false
			} as ClinicMedicationEntity,
			{
				id: '2',
				name: 'Medication 2',
				clinicId: 'clinic1',
				isAddedByUser: false
			} as ClinicMedicationEntity
		];

		it('should return an array of clinic medications without a search keyword', async () => {
			mockClinicMedicationsService.getMedications.mockResolvedValue(
				mockMedications
			);

			const result = await controller.getClinicMedications('clinic1');
			expect(result).toEqual(mockMedications);
			expect(
				mockClinicMedicationsService.getMedications
			).toHaveBeenCalledWith('clinic1', undefined);
		});

		it('should return an array of clinic medications with a search keyword', async () => {
			mockClinicMedicationsService.getMedications.mockResolvedValue([
				mockMedications[0]
			]);

			const result = await controller.getClinicMedications(
				'clinic1',
				'Med'
			);
			expect(result).toEqual([mockMedications[0]]);
			expect(
				mockClinicMedicationsService.getMedications
			).toHaveBeenCalledWith('clinic1', 'Med');
		});

		it('should handle errors from the service', async () => {
			const error = new Error('Service error');
			mockClinicMedicationsService.getMedications.mockRejectedValue(
				error
			);

			await expect(
				controller.getClinicMedications('clinic1')
			).rejects.toThrow(error);
		});
	});

	describe('createNewMedication', () => {
		const createDto: CreateClinicMedicationDto = {
			clinicId: 'clinic1',
			name: 'New Medication',
			isAddedByUser: true
		};

		it('should create a new medication', async () => {
			const mockNewMedication: ClinicMedicationEntity = {
				id: '3',
				...createDto
			} as ClinicMedicationEntity;

			mockClinicMedicationsService.createNewMedication.mockResolvedValue(
				mockNewMedication
			);

			const result = await controller.createNewMedication(createDto);
			expect(result).toEqual(mockNewMedication);
			expect(
				mockClinicMedicationsService.createNewMedication
			).toHaveBeenCalledWith(createDto);
			expect(mockLogger.log).toHaveBeenCalledWith(
				'Creating new medication',
				{
					dto: createDto
				}
			);
		});

		it('should throw an error if something goes wrong', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			mockClinicMedicationsService.createNewMedication.mockRejectedValue(
				error
			);

			await expect(
				controller.createNewMedication(createDto)
			).rejects.toThrow(error);

			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error creating new medication',
				{
					error,
					createClinicMedicationDto: createDto
				}
			);
			expect(
				mockClinicMedicationsService.createNewMedication
			).toHaveBeenCalledWith(createDto);
		});
	});

	describe('findOne', () => {
		const mockMedication: ClinicMedicationEntity = {
			id: '1',
			name: 'Test Medication',
			clinicId: 'clinic1'
		} as ClinicMedicationEntity;

		it('should return a medication by ID', async () => {
			mockClinicMedicationsService.findOne.mockResolvedValue(
				mockMedication
			);

			const result = await controller.findOne('1');
			expect(result).toEqual(mockMedication);
			expect(mockClinicMedicationsService.findOne).toHaveBeenCalledWith(
				'1'
			);
		});

		it('should throw HttpException when medication is not found', async () => {
			const error = new HttpException(
				'Medication not found',
				HttpStatus.NOT_FOUND
			);
			mockClinicMedicationsService.findOne.mockRejectedValue(error);

			await expect(controller.findOne('1')).rejects.toThrow(error);

			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error fetching medication',
				{
					error,
					id: '1'
				}
			);
		});
	});

	describe('update', () => {
		const updateDto: UpdateClinicMedicationDto = {
			name: 'Updated Medication'
		};

		const mockUpdatedMedication: ClinicMedicationEntity = {
			id: '1',
			name: 'Updated Medication',
			clinicId: 'clinic1'
		} as ClinicMedicationEntity;

		it('should update a medication successfully', async () => {
			mockClinicMedicationsService.update.mockResolvedValue(
				mockUpdatedMedication
			);

			const result = await controller.update('1', updateDto);
			expect(result).toEqual(mockUpdatedMedication);
			expect(mockClinicMedicationsService.update).toHaveBeenCalledWith(
				'1',
				updateDto
			);
		});

		it('should throw HttpException when update fails', async () => {
			const error = new HttpException(
				'Update failed',
				HttpStatus.BAD_REQUEST
			);
			mockClinicMedicationsService.update.mockRejectedValue(error);

			await expect(controller.update('1', updateDto)).rejects.toThrow(
				error
			);

			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error updating medication',
				{
					error,
					id: '1',
					updateClinicMedicationDto: updateDto
				}
			);
		});
	});

	describe('remove', () => {
		it('should delete a medication successfully', async () => {
			mockClinicMedicationsService.remove.mockResolvedValue(undefined);

			const result = await controller.remove('1');
			expect(result).toEqual({
				message: 'Medication deleted successfully'
			});
			expect(mockClinicMedicationsService.remove).toHaveBeenCalledWith(
				'1'
			);
		});

		it('should throw HttpException when deletion fails', async () => {
			const error = new HttpException(
				'Deletion failed',
				HttpStatus.BAD_REQUEST
			);
			mockClinicMedicationsService.remove.mockRejectedValue(error);

			await expect(controller.remove('1')).rejects.toThrow(error);

			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error deleting medication',
				{
					error,
					id: '1'
				}
			);
		});
	});
});
