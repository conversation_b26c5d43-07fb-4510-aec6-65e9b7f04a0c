import { ApiProperty } from '@nestjs/swagger';

// Define a DTO for Pet information
export class PetDto {
	@ApiProperty({
		description: 'The ID of the pet',
		example: '123e4567-e89b-12d3-a456-426614174000'
	})
	id!: string;

	@ApiProperty({ description: 'The name of the pet', example: '<PERSON>' })
	name!: string;

	@ApiProperty({ description: 'The breed of the pet', example: 'Labrador' })
	breed!: string;

	@ApiProperty({ description: 'The species of the pet', example: 'Dog' })
	species!: string;
}

// Define a DTO for Owner information to be nested within the main response
// This resolves the issue where @ApiProperty decorators were incorrectly placed inside an inline object type.
export class OwnerInfoDto {
	@ApiProperty({
		description: 'The ID of the client',
		example: '123e4567-e89b-12d3-a456-426614174001'
	})
	id!: string;

	@ApiProperty({
		description: 'The first name of the client',
		example: '<PERSON><PERSON>'
	})
	firstName!: string;

	@ApiProperty({
		description: 'The last name of the client',
		example: '<PERSON><PERSON><PERSON>'
	})
	lastName!: string;

	@ApiProperty({
		description: 'The full name of the client',
		example: 'Shivam Agarwal'
	})
	fullName!: string;

	@ApiProperty({
		description: 'The phone number of the client',
		example: '9876543210'
	})
	phoneNumber!: string;

	@ApiProperty({
		description: 'The email of the client',
		example: '<EMAIL>'
	})
	email!: string;

	@ApiProperty({
		description: 'The address of the client',
		example: '123 Main St, City'
	})
	address!: string;

	@ApiProperty({
		description: 'The balance amount of the client',
		example: 1000.0, // Example should match the type (number)
		type: Number // Explicitly set type for Swagger
	})
	ownerBalance!: number;

	// Add ownerCredits property
	@ApiProperty({
		description: 'The credit amount available to the client',
		example: 50.0,
		type: Number
	})
	ownerCredits!: number;
}

// Define the main response DTO for the Client Dashboard
export class ClientDashboardResponseDto {
	// Use the OwnerInfoDto class for the owner property
	@ApiProperty({
		description: 'Client information',
		type: OwnerInfoDto // Reference the OwnerInfoDto class for Swagger documentation
	})
	owner!: OwnerInfoDto; // Type the owner property with the OwnerInfoDto class

	// Use the PetDto class for the pets array property
	@ApiProperty({
		description: 'List of pets owned by the client',
		type: [PetDto] // Reference the PetDto class for Swagger documentation (array)
	})
	pets!: PetDto[]; // Type the pets property as an array of PetDto
}
