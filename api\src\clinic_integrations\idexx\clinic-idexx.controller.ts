import {
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Post,
	Query,
	Req,
	UseGuards
} from '@nestjs/common';
import { ClinicIdexxService } from './clinic-idexx.service';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CreateClinicIdexxDto } from './dto/create-clinic-idexx.dto';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { CreateClinicIdexxTestItemDto } from './dto/create_clinic-idexx-test-item.dto';
import { CreateIdexxOrderDto } from './dto/create-idexx-order.dto';
import { CheckIdexxOrdersDeletionDto } from './dto/create-idexx-order.dto';
import { TrackMethod } from '../../utils/new-relic/decorators/track-method.decorator';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Role } from '../../roles/role.enum';
import { Roles } from '../../roles/roles.decorator';

@ApiTags('Clinic Integrations')
@Controller('clinic-integrations')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicIdexxController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly clinicIdexxService: ClinicIdexxService
	) {}

	@ApiOkResponse({
		description: 'Creates a IDEXX entry for a clinic',
		type: CreateClinicIdexxEntity
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@Post()
	@TrackMethod('createIdexxEntry-clinic-integration')
	createIdexxEntry(
		@Body() createClinicIdexxDto: CreateClinicIdexxDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Creating new IDEXX entry', {
				dto: createClinicIdexxDto
			});

			return this.clinicIdexxService.createIdexxEntry(
				createClinicIdexxDto,
				req.user.brandId
			);
		} catch (error) {
			this.logger.error('Error creating new IDEXX entry', {
				error,
				createClinicIdexxDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Gets the list of entries for a clinic',
		type: CreateClinicIdexxEntity,
		isArray: true
	})
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getIdexxEntries-clinic-integration')
	getIdexxEntries(@Query('clinicId') clinicId: string) {
		try {
			this.logger.log('Fetching IDEXX entry list by clinic id', {
				clinicId
			});

			return this.clinicIdexxService.getIdexxEntries(clinicId);
		} catch (error) {
			this.logger.error('Error fetching IDEXX entry list by clinic id', {
				error
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@ApiOkResponse({
		description:
			'Deletes the IDEXX entry for a clinic id and returns true else returns false'
	})
	@Delete(':clinicIdexxId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deletIdexxEntry-clinic-integration')
	deletIdexxEntry(@Param('clinicIdexxId') clinicIdexxId: string) {
		try {
			this.logger.log('Deleting IDEXX entry item by clinic id', {
				clinicIdexxId
			});
			return this.clinicIdexxService.deletIdexxEntry(clinicIdexxId);
		} catch (error) {
			this.logger.error('Error deleting IDEXX entry item by clinic id', {
				error
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@ApiOkResponse({
		description: 'Gets the list of tests supported by IDEXX'
	})
	@Get(':clinicId/testsList')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getAllIDexxTestsList-clinic-integration')
	getAllIDexxTestsList(@Param('clinicId') clinicId: string) {
		try {
			this.logger.log('Fetching fetching IDEXX tests list', { clinicId });

			return this.clinicIdexxService.getAllIDexxTestsList(clinicId);
		} catch (error) {
			this.logger.error('Error fetching IDEXX tests list', { error });

			throw new HttpException(
				'Error fetching IDEXX tests list',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Creates a IDEXX Test list item for a clinic',
		type: CreateClinicIdexxEntity
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@Post(':clinicId/create')
	@TrackMethod('createIdexxTestItem-clinic-integration')
	createIdexxTestItem(
		@Body() createClinicIdexxTestItemDto: CreateClinicIdexxTestItemDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Creating IDEXX Test list entry', {
				dto: createClinicIdexxTestItemDto
			});

			return this.clinicIdexxService.createIdexxTestItem(
				createClinicIdexxTestItemDto,
				req.user.brandId
			);
		} catch (error) {
			this.logger.error('Error creating IDEXX Test list entry', {
				error
			});

			throw new HttpException(
				'Error creating IDEXX Test list entry',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description:
			'Deletes the IDEXX Test item entry from the clinic lab reports table id and returns true else returns false'
	})
	@Delete('/labReport/:clinicLabReportEntryId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteIdexxTestItem-clinic-integration')
	deleteIdexxTestItem(
		@Param('clinicLabReportEntryId') clinicLabReportEntryId: string
	) {
		try {
			this.logger.log('Deleting IDEXX test list item by id', {
				clinicLabReportEntryId
			});
			return this.clinicIdexxService.deleteIdexxTestItem(
				clinicLabReportEntryId
			);
		} catch (error) {
			this.logger.error('Error deleting IDEXX test list item by id', {
				error
			});
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@ApiOkResponse({
		description: 'Creates a IDEXX order'
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@Post('/create')
	@TrackMethod('createIdexxOrder-clinic-integration')
	async createIdexxOrder(
		@Body() createIdexxOrderDto: CreateIdexxOrderDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		this.logger.log('Creating an IDEXX order', {
			dto: createIdexxOrderDto
		});

		try {
			const result = await this.clinicIdexxService.createIdexxOrder(
				createIdexxOrderDto,
				req.user.brandId
			);

			// Add detailed logging for the result
			if (result.automationFailed) {
				this.logger.log(
					'IDEXX order creation succeeded but automation failed',
					{
						orderId: result.orderId,
						labReportId: result.labReportId,
						requiresManualCompletion: true
					}
				);
			} else if (result.orderId) {
				this.logger.log(
					'IDEXX order creation and automation succeeded',
					{
						orderId: result.orderId,
						labReportId: result.labReportId
					}
				);
			}

			return result;
		} catch (error) {
			this.logger.error('Error creating or automating IDEXX order', {
				error,
				dto: createIdexxOrderDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description:
			'Checks if IDEXX orders can be deleted by verifying their status',
		schema: {
			type: 'object',
			properties: {
				canBeDeleted: {
					type: 'boolean',
					description: 'Whether all IDEXX orders can be deleted'
				},
				details: {
					type: 'array',
					items: {
						type: 'object',
						properties: {
							labReportId: { type: 'string' },
							idexxOrderId: { type: 'string', nullable: true },
							status: { type: 'string', nullable: true },
							canDelete: { type: 'boolean' },
							error: { type: 'string', nullable: true }
						}
					}
				}
			}
		}
	})
	@Post('clinic/:clinicId/check-deletion-eligibility')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('checkIdexxOrdersDeletion-clinic-integration')
	async checkIdexxOrdersDeletion(
		@Param('clinicId') clinicId: string,
		@Body() checkIdexxOrdersDeletionDto: CheckIdexxOrdersDeletionDto
	) {
		try {
			this.logger.log('Checking IDEXX orders deletion eligibility', {
				clinicId,
				labReportIds: checkIdexxOrdersDeletionDto.labReportIds
			});

			return await this.clinicIdexxService.checkIdexxOrdersCanBeDeleted(
				clinicId,
				checkIdexxOrdersDeletionDto.labReportIds
			);
		} catch (error) {
			this.logger.error(
				'Error checking IDEXX orders deletion eligibility',
				{
					error,
					clinicId,
					dto: checkIdexxOrdersDeletionDto
				}
			);

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description: 'Cancels an IDEXX order'
	})
	@Delete('clinic/:clinicId/cancel/:idexxOrderId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('cancelIdexxOrder-clinic-integration')
	cancelIdexxOrder(
		@Param('clinicId') clinicId: string,
		@Param('idexxOrderId') idexxOrderId: string
	) {
		try {
			this.logger.log('Cancelling an IDEXX order', {
				clinicId,
				idexxOrderId
			});

			return this.clinicIdexxService.cancelIdexxOrder(
				clinicId,
				idexxOrderId
			);
		} catch (error) {
			this.logger.error('Error cancelling new IDEXX order', {
				error
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
