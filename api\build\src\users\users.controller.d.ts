import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto, UpdateProfileDto, UpdateWorkingHoursDto } from './dto/user.dto';
import { CalendarWorkingHoursResponseDto } from './dto/calendar-working-hours.dto';
import { Role } from '../roles/role.enum';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { Request } from 'express';
import { CreateExceptionDto, UpdateExceptionDto } from './dto/exception.dto';
import { FindClinicUsersAvailabilityResponse } from './dto/availability-response.dto';
import { RoleService } from '../roles/role.service';
interface RequestWithUser extends Request {
    user: {
        userId: string;
        role: Role;
        email: string;
    };
}
export declare class UsersController {
    private readonly usersService;
    private readonly logger;
    private readonly roleService;
    constructor(usersService: UsersService, logger: <PERSON>Logger, roleService: RoleService);
    createUser(createUserDto: CreateUserDto, req: RequestWithUser, clinicId: string, brandId: string): Promise<import("./entities/user.entity").User>;
    searchUsersAcrossClinics(brandId: string, searchTerm: string, excludeClinicId: string): Promise<{
        users: import("./entities/user.entity").User[];
        total: number;
    }>;
    getClinicUsersAvailability(clinicId: string, role?: string, search?: string, orderBy?: string, date?: string, startTime?: string, endTime?: string): Promise<FindClinicUsersAvailabilityResponse>;
    getClinicDoctors(clinicId: string): Promise<{
        users: any;
        total: number;
    }>;
    getClinicUsers(clinicId: string, role?: string, search?: string, page?: number, limit?: number, orderBy?: string): Promise<{
        users: any;
        total: number;
    }>;
    updateUserStatus(id: string, isActive: boolean): Promise<import("./entities/user.entity").User>;
    updateClinicUser(id: string, updateUserDto: UpdateUserDto, req: RequestWithUser): Promise<import("./entities/user.entity").User>;
    addUserToClinic(userId: string, clinicId: string, brandId: string, isPrimary?: boolean): Promise<import("../clinics/entities/clinic-user.entity").ClinicUser>;
    getClinicUserData(id: string): Promise<any>;
    remove(id: string): Promise<{
        message: string;
    }>;
    getCalendarWorkingHours(date: string, clinicId: string): Promise<CalendarWorkingHoursResponseDto>;
    findOne(id: string): Promise<Partial<import("./entities/user.entity").User>>;
    completeProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<{
        message: string;
        user: import("./entities/user.entity").User;
    }>;
    updateWorkingHours(userId: string, updateWorkingHoursDto: UpdateWorkingHoursDto): Promise<import("../clinics/entities/clinic-user.entity").ClinicUser>;
    getUserClinics(userId: string): Promise<import("../clinics/entities/clinic.entity").ClinicEntity[] | undefined>;
    createException(createExceptionDto: CreateExceptionDto, req: RequestWithUser): Promise<import("./entities/availability-exception.entity").AvailabilityExceptionEntity>;
    getExceptions(clinicUserId: string, includeHistory?: string | boolean): Promise<import("./entities/availability-exception.entity").AvailabilityExceptionEntity[]>;
    getExceptionById(id: string): Promise<import("./entities/availability-exception.entity").AvailabilityExceptionEntity>;
    updateException(id: string, updateExceptionDto: UpdateExceptionDto, req: RequestWithUser): Promise<import("./entities/availability-exception.entity").AvailabilityExceptionEntity>;
    deleteException(id: string): Promise<{
        message: string;
    }>;
}
export {};
