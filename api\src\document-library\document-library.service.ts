import {
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { CreateDocumentLibraryDto } from './dto/create-document-library.dto';
import { UpdateDocumentLibraryDto } from './dto/update-document-library.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { InjectRepository } from '@nestjs/typeorm';
import { DocumentLibrary } from './entities/document-library.entity';
import { Brackets, Repository } from 'typeorm';
import { generatePDF } from '../utils/generatePdf';
import { uuidv4 } from 'uuidv7';
import { generateDocumentLibrary } from '../utils/pdfs/documentLibrary';
import moment = require('moment');
import { S3Service } from '../utils/aws/s3/s3.service';

@Injectable()
export class DocumentLibraryService {
	constructor(
		private readonly logger: <PERSON><PERSON><PERSON><PERSON>,
		@InjectRepository(DocumentLibrary)
		private readonly documentLibraryRepository: Repository<DocumentLibrary>,
		private s3Service: S3Service
	) {}

	async create(
		createDocumentLibraryDto: CreateDocumentLibraryDto,
		brandId: string
	): Promise<DocumentLibrary> {
		this.logger.log('Creating a new document library entry', {
			dto: createDocumentLibraryDto
		});
		const documentLibrary = this.documentLibraryRepository.create({
			...createDocumentLibraryDto,
			brandId: brandId
		});
		if (documentLibrary.documentType === 'create') {
			const documentResponse =
				await this.documentLibraryRepository.findOne({
					where: { id: documentLibrary?.id },
					relations: ['clinic', 'clinic.brand']
				});
			const fileKey = `document-library/${uuidv4()}`;
			const documentHtml = generateDocumentLibrary({
				bodyText:
					createDocumentLibraryDto?.documentBody?.bodyText || '',
				clinicAddress: `${documentResponse?.clinic?.addressLine1} ${documentResponse?.clinic?.addressLine2}, ${documentResponse?.clinic?.city} ${documentResponse?.clinic?.addressPincode} ${documentResponse?.clinic?.state} ${documentResponse?.clinic?.country}`,
				clinicEmail: documentResponse?.clinic?.email || '',
				clinicName: documentResponse?.clinic?.name || '',
				clinicPhone:
					documentResponse?.clinic?.phoneNumbers[0]?.number || '',
				clinicWebsite: documentResponse?.clinic?.website || '',
				digitalSignature: '',
				docDate: '',
				title: createDocumentLibraryDto?.documentBody?.title || '',
				vetName: ' '
			});
			const pdfbuffer: Buffer = await generatePDF(documentHtml);
			await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
			documentLibrary.fileKey = fileKey;
		}
		return await this.documentLibraryRepository.save(documentLibrary);
	}

	async findAll(
		clinicId: string,
		page: number = 1,
		limit: number = 10,
		search?: string,
		orderBy: string = 'DESC'
	): Promise<{ documents: any; total: number }> {
		try {
			this.logger.log('Fetching documents', {
				page,
				limit,
				search,
				orderBy
			});

			const queryBuilder = this.documentLibraryRepository
				.createQueryBuilder('document')
				.where('document.deletedAt IS NULL') // Always true to allow appending conditions
				.andWhere('document.clinicId = :clinicId', { clinicId });

			const updatedSearch = search?.trim();
			if (updatedSearch) {
				this.logger.log('Adding search filter', { updatedSearch });
				queryBuilder.andWhere(
					new Brackets(qb => {
						qb.where('document.documentName ILIKE :search', {
							search: `%${updatedSearch}%`
						}).orWhere('document.category ILIKE :search', {
							search: `%${updatedSearch}%`
						});
					})
				);
			}

			queryBuilder
				.skip((page - 1) * limit)
				.take(limit)
				.orderBy('document.createdAt', 'DESC');

			const [documents, total] = await queryBuilder.getManyAndCount();

			this.logger.log('Documents fetched successfully', {
				count: documents.length,
				page,
				limit
			});

			return { documents, total };
		} catch (error) {
			this.logger.error('Error fetching documents', { error });
			throw new InternalServerErrorException('Failed to fetch documents');
		}
	}

	async findOne(id: string): Promise<DocumentLibrary> {
		this.logger.log(`Retrieving document library entry with ID: ${id}`);
		const documentLibrary = await this.documentLibraryRepository.findOne({
			where: { id }
		});
		if (!documentLibrary) {
			this.logger.error(
				`Document library entry with ID: ${id} not found`
			);
			throw new NotFoundException(
				`Document library entry with ID: ${id} not found`
			);
		}
		return documentLibrary;
	}

	async update(
		id: string,
		updateDocumentLibraryDto: UpdateDocumentLibraryDto
	): Promise<DocumentLibrary> {
		this.logger.log(`Updating document library entry with ID: ${id}`, {
			dto: updateDocumentLibraryDto
		});
		const documentLibrary = await this.documentLibraryRepository.findOne({
			where: { id },
			relations: ['clinic', 'clinic.brand']
		});
		if (documentLibrary && updateDocumentLibraryDto.fileKey) {
			await this.s3Service.deleteFile(documentLibrary.fileKey);
		}
		if (
			documentLibrary &&
			(updateDocumentLibraryDto?.documentBody?.title ||
				updateDocumentLibraryDto?.documentBody?.bodyText)
		) {
			const newFileKey = `document-library/${uuidv4()}`;
			const documentHtml = generateDocumentLibrary({
				bodyText:
					updateDocumentLibraryDto?.documentBody?.bodyText ||
					documentLibrary?.documentBody?.bodyText ||
					'',
				clinicAddress: `${documentLibrary?.clinic?.addressLine1} ${documentLibrary?.clinic?.addressLine2}, ${documentLibrary?.clinic?.city} ${documentLibrary?.clinic?.addressPincode} ${documentLibrary?.clinic?.state} ${documentLibrary?.clinic?.country}`,
				clinicEmail: documentLibrary?.clinic?.email || '',
				clinicName: documentLibrary?.clinic?.name || '',
				clinicPhone:
					documentLibrary?.clinic?.phoneNumbers[0]?.number || '',
				clinicWebsite: documentLibrary?.clinic?.website || '',
				digitalSignature: '',
				docDate: moment(documentLibrary?.createdAt).format(
					'DD MM YYYY'
				),
				title:
					updateDocumentLibraryDto?.documentBody?.title ||
					documentLibrary?.documentBody?.title ||
					'',
				vetName: ' '
			});
			const pdfbuffer: Buffer = await generatePDF(documentHtml);
			await this.s3Service.uploadPdfToS3(pdfbuffer, newFileKey);
			updateDocumentLibraryDto.fileKey = newFileKey;
			if (documentLibrary.fileKey) {
				await this.s3Service.deleteFile(documentLibrary.fileKey);
			}
		}

		await this.documentLibraryRepository.update(
			id,
			updateDocumentLibraryDto
		);
		const updatedDocument = await this.documentLibraryRepository.findOne({
			where: { id }
		});

		if (!updatedDocument) {
			throw new InternalServerErrorException(
				`Failed to retrieve the updated document library entry with ID: ${id}`
			);
		}

		return updatedDocument;
	}

	async remove(id: string): Promise<void> {
		this.logger.log(`Soft deleting document library entry with ID: ${id}`);

		const documentLibrary = await this.documentLibraryRepository.findOne({
			where: { id }
		});

		if (!documentLibrary) {
			this.logger.error(
				`Document library entry with ID: ${id} not found`
			);
			throw new NotFoundException(
				`Document library entry with ID: ${id} not found`
			);
		}

		documentLibrary.deletedAt = new Date();

		await this.documentLibraryRepository.save(documentLibrary);

		this.logger.log(
			`Document library entry with ID: ${id} successfully soft deleted`
		);
	}
}
