import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ClinicPlan } from './entities/clinic-plan.entity';
import { ILike, Repository } from 'typeorm';
import { ClinicConsumblesService } from '../clinic-consumables/clinic-consumbles.service';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { EnumPlanType } from './enums/enum-plan-type';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';

@Injectable()
export class ClinicPlansService {
	constructor(
		@InjectRepository(ClinicPlan)
		private readonly clinicPlansRepository: Repository<ClinicPlan>,
		private readonly clinicProductsservice: ClinicProductsService,
		private readonly clinicVaccinationsservice: ClinicVaccinationsService,
		private readonly clinicServicesservice: ClinicServicesService,
		private readonly clinicMedicationService: ClinicMedicationsService,
		private readonly clinicLabReportsService: ClinicLabReportService
	) {}

	async findAll(
		clinicId: string,
		searchKeyword?: string,
		excludeTypes?: EnumPlanType[]
	) {
		const [services, vaccinations, medications, products, labreports] =
			await Promise.all([
				this.clinicServicesservice.getServices(clinicId),
				this.clinicVaccinationsservice.getVaccinations(clinicId),
				this.clinicMedicationService.getMedications(clinicId),
				this.clinicProductsservice.getProducts(clinicId),
				this.clinicLabReportsService.getLabReports(clinicId)
			]);

		let combinedData = [
			...products?.map((item: any) => ({
				type: EnumPlanType.Product,
				...item
			})),
			...services?.map((item: any) => ({
				type: EnumPlanType.Service,
				...item
			})),
			...vaccinations?.map((item: any) => ({
				type: EnumPlanType.Vaccination,
				...item
			})),
			...medications?.map((item: any) => ({
				type: EnumPlanType.Medication,
				...item
			})),
			...labreports?.map((item: any) => ({
				type: EnumPlanType.Labreport,
				...item
			}))
		];

		// Filter out the excluded types
		if (excludeTypes && excludeTypes.length > 0) {
			combinedData = combinedData.filter(
				item => !excludeTypes.includes(item.type)
			);
		}

		return combinedData;
	}
}
