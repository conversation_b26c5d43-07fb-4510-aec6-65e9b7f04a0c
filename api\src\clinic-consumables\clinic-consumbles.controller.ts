import {
	Controller,
	Get,
	Post,
	Body,
	Patch,
	Param,
	Delete,
	HttpException,
	HttpStatus,
	Query,
	UseGuards
} from '@nestjs/common';
import { ClinicConsumblesService } from './clinic-consumbles.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiOkResponse,
	ApiCreatedResponse,
	ApiQuery,
	ApiTags
} from '@nestjs/swagger';
import { ClinicConsumableEntity } from './entities/clinic-consumable.entity';
import { CreateConsumableDto } from './dto/create-consumable.dto';
import { UpdateConsumableDto } from './dto/update-consumable.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Inventories')
@Controller('clinic-consumables')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicConsumblesController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly consumblesService: ClinicConsumblesService
	) {}

	@ApiOkResponse({
		description: 'Gets consumables items',
		isArray: true,
		type: ClinicConsumableEntity
	})
	@ApiQuery({ name: 'search', required: false })
	@ApiQuery({ name: 'clinicId', required: false })
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getAllConsumables-clinic-consumables')
	getAllConsumables(
		@Query('clinicId') clinicId: string,
		@Query('search') searchKeyword?: string
	) {
		try {
			this.logger.log('Fetching all consumables');

			return this.consumblesService.getConsumables(
				clinicId,
				searchKeyword
			);
		} catch (error) {
			this.logger.error('Error fetching consumables', { error });

			throw new HttpException(
				'Error fetching all the consumables',
				HttpStatus.BAD_REQUEST
			);
		}
	}
	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiCreatedResponse({ type: ClinicConsumableEntity })
	@TrackMethod('create-clinic-consumables')
	async create(@Body() createConsumableDto: CreateConsumableDto) {
		try {
			return await this.consumblesService.create(createConsumableDto);
		} catch (error) {
			this.logger.error('Error creating consumable', { error });
			throw new HttpException(
				'Error creating consumable',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({ type: ClinicConsumableEntity })
	@TrackMethod('findOne-clinic-consumables')
	async findOne(@Param('id') id: string) {
		try {
			return await this.consumblesService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching consumable', { error });
			throw new HttpException(
				'Error fetching consumable',
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Patch(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({ type: ClinicConsumableEntity })
	@TrackMethod('update-clinic-consumables')
	async update(
		@Param('id') id: string,
		@Body() updateConsumableDto: UpdateConsumableDto
	) {
		try {
			return await this.consumblesService.update(id, updateConsumableDto);
		} catch (error) {
			this.logger.error('Error updating consumable', { error });
			throw new HttpException(
				'Error updating consumable',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOkResponse({ description: 'Consumable deleted successfully' })
	@TrackMethod('remove-clinic-consumables')
	async remove(@Param('id') id: string) {
		try {
			await this.consumblesService.remove(id);
			return { message: 'Consumable deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting consumable', { error });
			throw new HttpException(
				'Error deleting consumable',
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
