import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ApiDocumentationBase } from './api-documentation-base';
import { SwaggerModule, DocumentBuilder, OpenAPIObject } from '@nestjs/swagger';

jest.mock('../../package.json', () => ({
	name: 'Test API',
	description: 'Test API Description',
	version: '1.0.0',
	tag: 'test'
}));

describe('ApiDocumentationBase', () => {
	let app: INestApplication;

	beforeAll(async () => {
		const moduleFixture: TestingModule = await Test.createTestingModule(
			{}
		).compile();

		app = moduleFixture.createNestApplication();
		await app.init();
	});

	afterAll(async () => {
		await app.close();
	});

	it('should initialize Swagger documentation', () => {
		const createDocumentSpy = jest.spyOn(SwaggerModule, 'createDocument');
		const setupSpy = jest.spyOn(SwaggerModule, 'setup');

		ApiDocumentationBase.initApiDocumentation(app);

		const config = new DocumentBuilder()
			.setTitle('Test API')
			.setDescription('Test API Description')
			.setVersion('1.0.0')
			.addTag('test')
			.build();

		// Expect createDocument to have been called with the correct app and config
		expect(createDocumentSpy).toHaveBeenCalledWith(app, config);

		const document: OpenAPIObject = createDocumentSpy.mock.results[0].value;

		// Expect setup to have been called with 'api', the app, and the created document
		expect(setupSpy).toHaveBeenCalledWith('api', app, document);
	});
});
