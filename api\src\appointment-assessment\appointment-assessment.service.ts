import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { ILike, Repository } from 'typeorm';
import { CreateAppointmentAssessmentDto } from './dto/create-appointment-assessment.dto';

@Injectable()
export class AppointmentAssessmentService {
	constructor(
		@InjectRepository(AppointmentAssessmentEntity)
		private readonly appointmentAssessmentRepository: Repository<AppointmentAssessmentEntity>
	) {}

	async findAll(
		searchKeyword?: string,
		clinicId?: string
	): Promise<AppointmentAssessmentEntity[]> {
		if (searchKeyword) {
			return this.appointmentAssessmentRepository.find({
				where: {
					name: ILike(`%${searchKeyword}%`),
					// isAddedByUser: false,
					clinicId
				}
			});
		}
		return this.appointmentAssessmentRepository.find({
			where: { clinicId }
		});
	}

	async createNewAssessment(
		createAppointmentAssessmentDto: CreateAppointmentAssessmentDto,
		clinicId: string,
		brandId: string
	) {
		const { name, isAddedByUser = true } = createAppointmentAssessmentDto;

		const createdAssessent =
			await this.appointmentAssessmentRepository.save({
				name,
				isAddedByUser,
				clinicId,
				brandId
			});
		if (!createdAssessent) {
			throw new InternalServerErrorException(
				'Failed in creating assessment.'
			);
		}

		return createdAssessent;
	}
}
