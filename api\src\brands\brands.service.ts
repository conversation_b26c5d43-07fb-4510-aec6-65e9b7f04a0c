import {
	BadRequestException,
	ConflictException,
	Injectable,
	InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';

@Injectable()
export class BrandService {
	constructor(
		@InjectRepository(Brand)
		private brandRepository: Repository<Brand>,
		private readonly logger: WinstonLogger
	) {}

	async createBrand(createBrandDto: CreateBrandDto): Promise<Brand> {
		try {
			this.logger.log('Creating Brand', { dto: createBrandDto });
			const existingBrand = await this.brandRepository.findOne({
				where: { name: createBrandDto.name }
			});
			if (existingBrand) {
				this.logger.error('Brand already exists', {
					email: createBrandDto.name
				});
				throw new ConflictException(
					'Brand with this name already exists'
				);
			}
			const brand = this.brandRepository.create(createBrandDto);
			return await this.brandRepository.save(brand);
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while creating the user'
			);
		}
	}

	async getAllBrands(): Promise<Brand[]> {
		try {
			this.logger.log('Fetching all Brands');
			const brands = await this.brandRepository.find();
			this.logger.log('Fetched all Brands:', { brands });
			return brands;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brands'
			);
		}
	}

	async getBrandById(id: string): Promise<Brand | null> {
		try {
			this.logger.log('Fetching a Brand');
			const brand = await this.brandRepository.findOne({ where: { id } });
			this.logger.log('Fetched a Brand:', { brand });
			return brand;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brand'
			);
		}
	}

	async getBrandBySlug(slug: string): Promise<BrandWithSettingsDto | null> {
		try {
			this.logger.log('Fetching a Brand');
			const brand = await this.brandRepository.findOne({
				where: { slug },
				relations: ['clinics']
			});

			if (!brand) {
				return null;
			}

			const hasClientBookingEnabled =
				brand.clinics?.some(
					clinic =>
						clinic.customRule?.clientBookingSettings?.isEnabled ===
						true
				) || false;

			const brandDto = new BrandWithSettingsDto();

			// Copy only the basic brand properties (excluding clinics)
			brandDto.id = brand.id;
			brandDto.name = brand.name;
			brandDto.slug = brand.slug;
			brandDto.createdAt = brand.createdAt;
			brandDto.updatedAt = brand.updatedAt;
			brandDto.createdBy = brand.createdBy;
			brandDto.updatedBy = brand.updatedBy;

			// Add the calculated client booking flag
			brandDto.hasClientBookingEnabled = hasClientBookingEnabled;

			// Include clinics with their contact numbers
			if (brand.clinics && brand.clinics.length > 0) {
				// Include basic clinic info including phone numbers
				brandDto.clinics = brand.clinics.map(clinic => ({
					id: clinic.id,
					name: clinic.name,
					phoneNumbers: clinic.phoneNumbers || []
				}));
			}

			this.logger.log('Fetched a Brand with settings:', {
				brandId: brand.id,
				hasClientBookingEnabled,
				clinicsCount: brand.clinics?.length || 0
			});

			return brandDto;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brand'
			);
		}
	}
}
