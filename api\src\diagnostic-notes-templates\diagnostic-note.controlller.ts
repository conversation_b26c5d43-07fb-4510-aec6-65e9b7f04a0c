import {
	Controller,
	Get,
	Post,
	Body,
	Param,
	Delete,
	UseGuards,
	Query,
	Req,
	Put
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { CreateDiagnosticTemplateDto } from './dto/create-template.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { DiagnosticTemplatesService } from './diagnostic-note.service';
import {
	UpdateDiagnosticNoteDto,
	CreateDiagnosticNoteDto
} from './dto/diagnostic-note.dto';

@ApiTags('Diagnostic Templates')
@Controller('diagnostic-templates')
@UseGuards(JwtAuthGuard, RolesGuard)
export class DiagnosticTemplatesController {
	constructor(
		private readonly diagnosticNoteService: DiagnosticTemplatesService
	) {}

	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create a new diagnostic template' })
	@ApiResponse({ status: 201, description: 'Template created successfully' })
	@TrackMethod('create-diagnostic-template')
	async create(
		@Body() createDto: CreateDiagnosticTemplateDto,
		@Req() req: any
	) {
		return this.diagnosticNoteService.create(createDto, req.user.id);
	}

	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get all templates for a clinic' })
	@TrackMethod('find-diagnostic-templates')
	async findAll(@Query('clinicId') clinicId: string) {
		return this.diagnosticNoteService.findAll(clinicId);
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get a template by id' })
	@TrackMethod('get-diagnostic-template')
	async findOne(
		@Param('id') id: string,
		@Query('clinicId') clinicId: string
	) {
		return this.diagnosticNoteService.findOne(id, clinicId);
	}

	@Put(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update a template' })
	@TrackMethod('update-diagnostic-template')
	async update(
		@Param('id') id: string,
		@Body() updateDto: Partial<CreateDiagnosticTemplateDto>,
		@Query('clinicId') clinicId: string,
		@Req() req: any
	) {
		return this.diagnosticNoteService.update(
			id,
			updateDto,
			req.user.id,
			clinicId
		);
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete a template' })
	@TrackMethod('delete-diagnostic-template')
	async remove(@Param('id') id: string, @Query('clinicId') clinicId: string) {
		return this.diagnosticNoteService.remove(id, clinicId);
	}

	@Post('/diagnostic-note')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create a new diagnostic note' })
	async createNote(
		@Body() createNoteDto: CreateDiagnosticNoteDto,
		@Req() req: any
	) {
		const note = await this.diagnosticNoteService.createNote(
			createNoteDto,
			req.user.userId
		);
		return note;
	}

	@Get('lab-report/:labReportId/templates')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get templates for a lab report' })
	async getTemplatesForLabReport(
		@Param('labReportId') labReportId: string,
		@Query('clinicId') clinicId: string
	) {
		return await this.diagnosticNoteService.getTemplatesForLabReport(
			labReportId,
			clinicId
		);
	}

	@Get('clinic-lab-report/:clinicLabReportId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get notes for a lab report' })
	async getNotesByLabReport(
		@Param('clinicLabReportId') clinicLabReportId: string,
		@Query('clinicId') clinicId: string
	) {
		return await this.diagnosticNoteService.findTemplatesByDiagnostic(
			clinicLabReportId,
			clinicId
		);
	}

	@Get('/diagnostic-note/:patientId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get a diagnostic notes for patient' })
	async getPatientNotes(@Param('patientId') patientId: string) {
		return await this.diagnosticNoteService.getPatientNotes(patientId);
	}

	@Get('/diagnostic-note/note/:noteId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get a diagnostic notes for patient' })
	async getNote(@Param('noteId') noteId: string) {
		return await this.diagnosticNoteService.getNote(noteId);
	}

	@Put('/diagnostic-note/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update a diagnostic note' })
	async updateNote(
		@Param('id') id: string,
		@Body() updateNoteDto: UpdateDiagnosticNoteDto,
		@Req() req: any
	) {
		return await this.diagnosticNoteService.updateNote(
			id,
			updateNoteDto,
			req.user.id
		);
	}

	@Delete('/diagnostic-note/:noteId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete a diagnostic note' })
	async deleteNote(@Param('noteId') noteId: string) {
		return await this.diagnosticNoteService.deleteNote(noteId);
	}
}
