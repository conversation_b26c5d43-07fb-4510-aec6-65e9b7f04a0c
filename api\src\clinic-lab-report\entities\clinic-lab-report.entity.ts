import { uuid } from 'aws-sdk/clients/customerprofiles';
import {
	Column,
	CreateDateColumn,
	Entity,
	JoinColumn,
	ManyToOne,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { User } from '../../users/entities/user.entity';
import { CartItemEntity } from '../../cart-items/entities/cart-item.entity';

@Entity({ name: 'clinic_lab_reports' })
export class ClinicLabReport {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@ManyToOne(() => ClinicEntity)
	@JoinColumn({ name: 'clinic_id' })
	@Column('uuid', { nullable: false, name: 'clinic_id' })
	clinicId!: uuid;

	@Column({ nullable: false })
	name!: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@Column({ name: 'unique_id' })
	uniqueId!: string;
	
	@Column({ name: 'chargeable_price' })
	chargeablePrice!: number;

	@Column({ name: 'tax' })
	tax!: number;

	@Column({ name: 'associated_lab' })
	associatedLab!: number;

	@Column({ name: 'description' })
	description!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@OneToOne(() => CartItemEntity, cart => cart.product)
    cart?: CartItemEntity;

	@Column({ name: 'integration_type' })
	integrationType?: string; // IDEXX or not

	@Column({ name: 'integration_code' })
	integrationCode?: string; // IDEXX code

	@Column({ name: 'deleted_at', nullable: true })
	deletedAt?: Date;
}
