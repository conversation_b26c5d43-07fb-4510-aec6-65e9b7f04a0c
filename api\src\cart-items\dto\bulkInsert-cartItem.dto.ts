import { ApiProperty } from '@nestjs/swagger';
import {
	ArrayMinSize,
	ArrayNotEmpty,
	IsArray,
	IsNotEmpty,
	IsUUID
} from 'class-validator';

export class BulkInsertIntoCartItemDto {
	@ApiProperty({
		description: 'The appointment id',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	appointmentId!: string;

	@ApiProperty({
		description: 'presciption id for inserting into cartItem',
		example: ['uuid1, uuid2'],
		isArray: true
	})

	@IsArray()
	@ArrayNotEmpty()
	@ArrayMinSize(1)
	@IsUUID('4', { each: true })
	prescriptionIds!: string[];

	@ApiProperty({
		description:
			'The source from which the item is added, default is "prescription"',
		example: 'prescription'
	})
	addedFrom: string = 'prescriptions';
}
