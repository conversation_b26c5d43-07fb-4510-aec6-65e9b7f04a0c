import { Test, TestingModule } from '@nestjs/testing';
import { ClientBookingController } from './client-booking.controller';
import { ClientBookingService } from './client-booking.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import {
	CreateClientBookingDto,
	UpdateClientBookingDto,
	ClientBookingResponseDto
} from './dto/client-booking.dto';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';
import { Role } from '../roles/role.enum';
import { ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

// Mock ClientBookingService
const mockClientBookingService = {
	createClientBooking: jest.fn(),
	getClientBooking: jest.fn(),
	updateClientBooking: jest.fn(),
	deleteClientBooking: jest.fn()
};

// Mock Guards
const mockJwtAuthGuard = { canActivate: jest.fn(() => true) };
const mockRolesGuard = { canActivate: jest.fn(() => true) };

// Mock Logger
const mockLogger = {
	log: jest.fn(),
	error: jest.fn(),
	warn: jest.fn(),
	debug: jest.fn(),
	verbose: jest.fn()
};

describe('ClientBookingController', () => {
	let controller: ClientBookingController;
	let service: ClientBookingService;

	const mockOwnerId = 'owner-uuid-123';
	const mockBrandId = 'brand-uuid-456';
	const mockAppointmentId = 'appt-uuid-789';

	// Minimal mock request satisfying the controller's usage
	const mockUser = {
		userId: 'owner-uuid-123',
		brandId: 'brand-uuid-456',
		role: Role.OWNER,
		globalOwnerId: 'global-owner-1'
	};
	const mockRequest = {
		user: mockUser
	} as unknown as RequestWithUser; // Cast to bypass extensive property mocking

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClientBookingController],
			providers: [
				{
					provide: ClientBookingService,
					useValue: mockClientBookingService
				},
				// Provide mock Logger if the controller uses it directly (it does)
				{
					provide: Logger,
					useValue: mockLogger
				},
				Reflector // Needed for RolesGuard potentially
			]
		})
			.overrideGuard(JwtAuthGuard)
			.useValue(mockJwtAuthGuard)
			.overrideGuard(RolesGuard)
			.useValue(mockRolesGuard)
			.compile();

		controller = module.get<ClientBookingController>(
			ClientBookingController
		);
		service = module.get<ClientBookingService>(ClientBookingService);

		// Reset mocks
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('createBooking', () => {
		it('should create a booking successfully', async () => {
			const createDto: CreateClientBookingDto = {
				/* ...dto data... */ petId: 'pet-1',
				doctorId: 'doc-1',
				clinicId: 'clinic-1',
				date: '2024-01-01',
				startTime: '10:00',
				endTime: '10:30'
			};
			const expectedResponse: ClientBookingResponseDto = {
				/* ...response data... */ id: 'booking-1',
				petId: 'pet-1',
				doctorId: 'doc-1',
				clinicId: 'clinic-1',
				date: '2024-01-01',
				startTime: '10:00',
				endTime: '10:30',
				petName: '',
				doctorName: '',
				clinicName: '',
				status: '',
				createdAt: new Date(),
				updatedAt: new Date()
			};
			mockClientBookingService.createClientBooking.mockResolvedValue(
				expectedResponse
			);

			// Pass the correctly cast mockRequest
			const result = await controller.createBooking(
				createDto,
				mockRequest
			);

			expect(result).toEqual(expectedResponse);
			// Expect service call with ownerId from mockRequest.user
			expect(
				mockClientBookingService.createClientBooking
			).toHaveBeenCalledWith(createDto, mockUser.userId);
		});

		// Test for ForbiddenException should ideally be in e2e or check guard interaction
	});

	describe('getBooking', () => {
		it('should retrieve booking details successfully', async () => {
			const appointmentId = 'app-uuid-123';
			const expectedResponse: ClientBookingResponseDto = {
				/* ...response data... */ id: appointmentId,
				petId: 'pet-1',
				doctorId: 'doc-1',
				clinicId: 'clinic-1',
				date: '2024-01-01',
				startTime: '10:00',
				endTime: '10:30',
				petName: '',
				doctorName: '',
				clinicName: '',
				status: '',
				createdAt: new Date(),
				updatedAt: new Date()
			};
			mockClientBookingService.getClientBooking.mockResolvedValue(
				expectedResponse
			);

			const result = await controller.getBooking(
				appointmentId,
				mockRequest
			);

			expect(result).toEqual(expectedResponse);
			expect(
				mockClientBookingService.getClientBooking
			).toHaveBeenCalledWith(appointmentId, mockUser.userId);
		});
	});

	describe('updateBooking', () => {
		it('should update a booking successfully', async () => {
			const appointmentId = 'app-uuid-456';
			const updateDto: UpdateClientBookingDto = {
				date: '2024-01-02',
				startTime: '11:00',
				endTime: '11:30'
			};
			const expectedResponse: ClientBookingResponseDto = {
				/* ...updated response data... */ id: appointmentId,
				petId: 'pet-1',
				doctorId: 'doc-1',
				clinicId: 'clinic-1',
				date: '2024-01-02',
				startTime: '11:00',
				endTime: '11:30',
				petName: '',
				doctorName: '',
				clinicName: '',
				status: '',
				createdAt: new Date(),
				updatedAt: new Date()
			};
			mockClientBookingService.updateClientBooking.mockResolvedValue(
				expectedResponse
			);

			const result = await controller.updateBooking(
				appointmentId,
				updateDto,
				mockRequest
			);

			expect(result).toEqual(expectedResponse);
			expect(
				mockClientBookingService.updateClientBooking
			).toHaveBeenCalledWith(appointmentId, updateDto, mockUser.userId);
		});
	});

	describe('deleteBooking', () => {
		it('should cancel a booking successfully', async () => {
			const appointmentId = 'app-uuid-789';
			const expectedResponse: ClientBookingResponseDto = {
				/* ...cancelled response data... */ id: appointmentId,
				status: 'Cancelled',
				petId: 'pet-1',
				doctorId: 'doc-1',
				clinicId: 'clinic-1',
				date: '2024-01-01',
				startTime: '10:00',
				endTime: '10:30',
				petName: '',
				doctorName: '',
				clinicName: '',
				createdAt: new Date(),
				updatedAt: new Date()
			}; // Status might be updated by service
			mockClientBookingService.deleteClientBooking.mockResolvedValue(
				expectedResponse
			);

			const result = await controller.deleteBooking(
				appointmentId,
				mockRequest
			);

			expect(result).toEqual(expectedResponse);
			expect(
				mockClientBookingService.deleteClientBooking
			).toHaveBeenCalledWith(appointmentId, mockUser.userId);
		});
	});
});
