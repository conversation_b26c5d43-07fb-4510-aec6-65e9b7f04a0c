import {
	<PERSON><PERSON><PERSON>,
	PrimaryGeneratedC<PERSON>umn,
	Column,
	ManyTo<PERSON>ne,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	CreateDateColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicUser } from '../../clinics/entities/clinic-user.entity';

/**
 * Entity to store precomputed availability slots for clinic users
 * This optimizes availability queries by storing preprocessed time slots
 */
@Entity('clinic_availability_slots')
export class ClinicAvailabilitySlot {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'clinic_user_id' })
	clinicUserId!: string;

	@ManyToOne(() => ClinicUser)
	@JoinColumn({ name: 'clinic_user_id' })
	clinicUser!: ClinicUser;

	@Column({ type: 'date' })
	date!: string; // YYYY-MM-DD format

	@Column({ type: 'timestamptz', name: 'start_time' })
	startTime!: Date; // UTC timestamp

	@Column({ type: 'timestamptz', name: 'end_time' })
	endTime!: Date; // UTC timestamp

	@Column({ type: 'boolean', default: true, name: 'is_available' })
	isAvailable!: boolean; // Flag for quick filtering

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;
}
