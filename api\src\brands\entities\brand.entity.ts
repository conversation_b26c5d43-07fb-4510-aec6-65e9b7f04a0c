import {
	BeforeInsert,
	BeforeUpdate,
	Column,
	CreateDateColumn,
	Entity,
	JoinColumn,
	ManyToOne,
	OneToMany,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import slugify from 'slugify';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
@Entity({ name: 'brands' })
export class Brand {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'varchar', length: 50, unique: true })
	name!: string;

	@Column({ type: 'varchar', length: 50, unique: true })
	slug!: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@OneToMany(() => ClinicEntity, clinic => clinic.brand)
	clinics!: ClinicEntity[];

	@BeforeInsert()
	@BeforeUpdate()
	generateSlug() {
		this.slug = slugify(this.name, { lower: true, replacement: '' });
	}

	@OneToOne(() => PaymentDetailsEntity, paymentDetail => paymentDetail.brand)
	paymentDetail?: PaymentDetailsEntity;
}
