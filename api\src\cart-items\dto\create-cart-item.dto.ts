import { Optional } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class CreateCartItemDto {
	@ApiProperty({
		description: 'The appointment id',
		example: 'uuid'
	})
	@IsOptional()
	@IsUUID()
	appointmentId?: string;

	@IsOptional()
	cartId?: string;

	@ApiProperty({
		description: 'The line item id',
		example: 'uuid'
	})
	@IsOptional()
	lineItemId?: string;

	@ApiProperty({
		description: 'The patient id',
		example: 'uuid'
	})
	@IsOptional()
	@IsUUID()
	patientId?: string;

	@ApiProperty({
		description:
			'Flag to indicate if this cart is for an impromptu appointment',
		example: false
	})
	@IsOptional()
	@IsBoolean()
	impromptu?: boolean;

	@ApiProperty({
		description: 'The lab report id',
		example: 'uuid'
	})
	labReportId?: string;

	@ApiProperty({
		description: 'The product id',
		example: 'uuid'
	})
	productId?: string;

	@ApiProperty({
		description: 'The service id',
		example: 'uuid'
	})
	serviceId?: string;

	@ApiProperty({
		description: 'The vaccination id',
		example: 'uuid'
	})
	vaccinationId?: string;

	@ApiProperty({
		description: 'The prescription id',
		example: 'uuid'
	})
	prescriptionId?: string;

	@ApiProperty({
		description: 'The quantity added to the cart',
		example: 1
	})
	quantity?: number;

	@Optional()
	@ApiProperty({
		description: 'The quantity added to the cart',
		example: 1
	})
	price?: number;

	@ApiProperty({
		description: 'The source from which the item is added',
		example: 'prescription'
	})
	@IsOptional()
	addedFrom?: string = 'manual';
}
