import { Repository } from 'typeorm';
import { UserOtp } from './entities/user-otp.entity';
import { UsersService } from '../users/users.service';
import { GenerateOtpDto } from './dto/generate-otp-dto';
import { RoleService } from '../roles/role.service';
import { ValidateOtpDto } from './dto/validate-user-otp.dto';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { JwtService } from '@nestjs/jwt';
export declare class UserOtpsService {
    private userOtpsRepository;
    private usersServices;
    private roleService;
    private jwtService;
    private readonly mailService;
    constructor(userOtpsRepository: Repository<UserOtp>, usersServices: UsersService, roleService: RoleService, jwtService: JwtService, mailService: SESMailService);
    private generateOtp;
    createOtp(generateOtpDto: GenerateOtpDto): Promise<{
        statusCode: number;
        message: string;
    }>;
    validateOtp(validateOtpDto: ValidateOtpDto): Promise<{
        token: string;
        roleName: string;
    }>;
}
