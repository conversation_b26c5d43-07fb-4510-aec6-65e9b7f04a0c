import { Column, Entity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';

@Entity('clinic_rooms')
export class ClinicRoomEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ name: 'name' })
	name!: string;

	@Column({ name: 'description',nullable:true })
	description!: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@OneToOne(() => AppointmentEntity, room => room)
	appointment!: AppointmentEntity;
}
