import { ApiProperty } from '@nestjs/swagger';
import {
	ArrayMinSize,
	IsArray,
	IsEnum,
	IsNotEmpty,
	IsOptional,
	IsUUID
} from 'class-validator';
import { EnumAppointmentType } from '../../enums/enum-appointment-type';
import { EnumAppointmentTriage } from '../../enums/enum-appointment-triage';
import { EnumAppointmentStatus } from '../../enums/enum-appointment-status';

// DTO for updating appointment fields, can add more fields as required
export class UpdateAppointmentFeildsDto {
	@ApiProperty({
		description:
			'The doctor id of a specific clinic for which appointment is to be made.',
		example: ['uuid1, uuid2'],
		isArray: true
	})
	@IsOptional()
	@IsArray()
	@ArrayMinSize(1)
	@IsUUID('4', { each: true })
	doctorIds!: string[];

	@ApiProperty({
		description: 'The patient id of a specific clinic.',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	patientId!: string;

	@ApiProperty({
		enum: EnumAppointmentStatus,
		description: 'New status of the appointment'
	})
	@IsEnum(EnumAppointmentStatus)
	status?: EnumAppointmentStatus;

	@ApiProperty({
		description: 'The room id of a specific clinic.',
		example: 'uuid'
	})
	// @IsNotEmpty()
	@IsOptional()
	@IsUUID()
	roomId?: string;

	@ApiProperty({
		description: 'The date of an appointment'
	})
	@IsOptional()
	date?: Date;

	@ApiProperty({
		description: 'The start time of an appointment'
	})
	@IsOptional()
	startTime?: Date;

	@ApiProperty({
		description: 'The end time of an appointment'
	})
	@IsOptional()
	endTime?: Date;

	@ApiProperty({
		description: 'The reason for an appointment',
		example: 'Visit'
	})
	// @IsNotEmpty()
	@IsOptional()
	reason?: string;

	@ApiProperty({
		description: 'The type of an appointment',
		example: 'Itching'
	})
	// @IsNotEmpty()
	@IsOptional()
	@IsEnum(EnumAppointmentType)
	type?: EnumAppointmentType;

	@ApiProperty({
		description: 'The weight of the patient',
		example: '10'
	})
	@IsOptional()
	weight?: number;

	@ApiProperty({
		description: 'The priority of an appointment',
		example: 'No priority'
	})
	@IsOptional()
	triage?: EnumAppointmentTriage;

	@ApiProperty({
		description: 'The notes of an appointment'
	})
	@IsOptional()
	notes?: object;

	@IsArray()
	providerIds!: string[];

	@ApiProperty({
		description: 'The pre visit questions of an appointment'
	})
	@IsOptional()
	preVisitQuestions?: object;

	@IsOptional()
	isBlocked?: boolean;

	@IsOptional()
	createdAt?: Date;

	@IsOptional()
	updatedAt?: Date;
}
