import {
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Patch,
	Post,
	Query,
	UseGuards
} from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiOkResponse,
	ApiOperation,
	ApiQuery,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { ClinicServicesService } from './clinic-services.service';
import { ClinicServiceEntity } from './entities/clinic-service.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { CreateServiceDto } from './dto/create-services.dto';
import { UpdateServiceDto } from './dto/update-services.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Inventories')
@Controller('clinic-services')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicServicesController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly clinicService: ClinicServicesService
	) {}

	@ApiOkResponse({
		description: 'Get all services items',
		isArray: true,
		type: ClinicServiceEntity
	})
	@ApiQuery({ name: 'search', required: false })
	@ApiQuery({ name: 'clinicId', required: false })
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getAllServices-clinic-services')
	async getAllServices(
		@Query('clinicId') clinicId: string,
		@Query('search') searchKeyword?: string
	) {
		try {
			this.logger.log('Fetching all services');
			return await this.clinicService.getServices(
				clinicId,
				searchKeyword
			);
		} catch (error) {
			this.logger.error('Error fetching services', { error });
			throw new HttpException(
				'Error fetching all the services',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get service by id' })
	@TrackMethod('findOne-clinic-services')
	async findOne(@Param('id') id: string) {
		try {
			return await this.clinicService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching service', { error });
			throw new HttpException(
				'Error fetching service',
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create new service' })
	@ApiResponse({ status: 201, description: 'Service created successfully' })
	@TrackMethod('create-clinic-services')
	async create(@Body() createServiceDto: CreateServiceDto) {
		try {
			return await this.clinicService.create(createServiceDto);
		} catch (error) {
			this.logger.error('Error creating service', { error });
			throw new HttpException(
				'Error creating service',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Patch(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update service' })
	@TrackMethod('update-clinic-services')
	async update(
		@Param('id') id: string,
		@Body() updateServiceDto: UpdateServiceDto
	) {
		try {
			return await this.clinicService.update(id, updateServiceDto);
		} catch (error) {
			this.logger.error('Error updating service', { error });
			throw new HttpException(
				'Error updating service',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete service' })
	@TrackMethod('remove-clinic-services')
	async remove(@Param('id') id: string) {
		try {
			await this.clinicService.remove(id);
			return { message: 'Service deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting service', { error });
			throw new HttpException(
				'Error deleting service',
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
