import { Modu<PERSON> } from '@nestjs/common';
import { ClinicConsumblesController } from './clinic-consumbles.controller';
import { ClinicConsumblesService } from './clinic-consumbles.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicConsumableEntity } from './entities/clinic-consumable.entity';
import { RoleModule } from '../roles/role.module';

@Module({
  imports: [TypeOrmModule.forFeature([ClinicConsumableEntity]),RoleModule],
  controllers: [ClinicConsumblesController],
  providers: [ClinicConsumblesService],
  exports:[ClinicConsumblesService]
})
export class  ClinicConsumablesModule {}
