import { validate } from 'class-validator';
import { AppUpdateDto } from './app-update.dto';

describe('AppUpdateDto', () => {
	let dto: AppUpdateDto;

	beforeEach(() => {
		dto = new AppUpdateDto();
	});

	it('should validate all required fields', async () => {
		dto.buildType = 'Android';
		dto.majorVersion = '1';
		dto.minorVersion = '0';
		dto.patchVersion = '2';

		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should fail if buildType is empty', async () => {
		dto.buildType = '';
		dto.majorVersion = '1';
		dto.minorVersion = '0';
		dto.patchVersion = '2';

		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
	});

	it('should fail if majorVersion is empty', async () => {
		dto.buildType = 'Android';
		dto.majorVersion = '';
		dto.minorVersion = '0';
		dto.patchVersion = '2';

		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
	});

	it('should fail if minorVersion is empty', async () => {
		dto.buildType = 'Android';
		dto.majorVersion = '1';
		dto.minorVersion = '';
		dto.patchVersion = '2';

		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
	});

	it('should fail if patchVersion is empty', async () => {
		dto.buildType = 'Android';
		dto.majorVersion = '1';
		dto.minorVersion = '0';
		dto.patchVersion = '';

		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
	});

	it('should fail if majorVersion is not a string', async () => {
		dto.buildType = 'Android';
		// eslint-disable-next-line @typescript-eslint/ban-ts-comment
		// @ts-expect-error
		dto.majorVersion = 1; // Simulate incorrect type
		dto.minorVersion = '0';
		dto.patchVersion = '2';

		const errors = await validate(dto);
		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].constraints).toHaveProperty('isString');
	});
});
