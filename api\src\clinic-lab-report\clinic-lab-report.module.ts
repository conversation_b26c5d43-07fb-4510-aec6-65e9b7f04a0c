import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicLabReportService } from './clinic-lab-report.service';
import { ClinicLabReportController } from './clinic-lab-report.controller';
import { ClinicLabReport } from './entities/clinic-lab-report.entity';
import { LabReport } from './entities/lab-report.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { S3Service } from '../utils/aws/s3/s3.service';
import { RoleModule } from '../roles/role.module';
import { SocketModule } from '../socket/socket.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			ClinicLabReport,
			LabReport,
			AppointmentEntity,
			AppointmentDetailsEntity
		]),
		RoleModule,
		forwardRef(() => SocketModule)
	],
	providers: [ClinicLabReportService, S3Service],
	controllers: [ClinicLabReportController],
	exports: [ClinicLabReportService]
})
export class ClinicLabReportModule {}
