import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicServicesController } from './clinic-services.controller';
import { ClinicServicesService } from './clinic-services.service';
import { ClinicServiceEntity } from './entities/clinic-service.entity';
import { RoleService } from '../roles/role.service';

describe('ClinicServicesController', () => {
	let controller: ClinicServicesController;
	let serviceService: jest.Mocked<ClinicServicesService>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockClinicServicesService = {
		getServices: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		findOne: jest.fn(),
		remove: jest.fn(),
		bulkInsert: jest.fn()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicServicesController],
			providers: [
				{
					provide: ClinicServicesService,
					useValue: mockClinicServicesService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicServicesController>(
			ClinicServicesController
		);
		serviceService = module.get(ClinicServicesService);
		logger = module.get(WinstonLogger);

		// Reset all mocks before each test
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('Get services for a clinic - GET - /clinic-services', () => {
		const servicesDataList: ClinicServiceEntity[] = [
			{
				id: 'service_uuid_1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				serviceName: 'service_name_A',
				chargeablePrice: 0,
				tax: 10
			},
			{
				id: 'service_uuid_2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				serviceName: 'service_name_A',
				chargeablePrice: 0,
				tax: 10
			}
		];

		it('should have a getAllServices function', () => {
			expect(controller.getAllServices).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the getAllServices call', async () => {
			const error = new HttpException(
				'Error fetching all the services',
				HttpStatus.BAD_REQUEST
			);

			serviceService.getServices.mockRejectedValue(error);
			await expect(controller.getAllServices('clinicId')).rejects.toThrow(
				'Error fetching all the services'
			);
			expect(serviceService.getServices).toHaveBeenCalled();
			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching services',
				{ error }
			);
		});

		it('should return a list of services without a search keyword', async () => {
			serviceService.getServices.mockResolvedValue(servicesDataList);

			const result = await controller.getAllServices('clinicId');

			expect(result).toEqual(servicesDataList);
			expect(serviceService.getServices).toHaveBeenCalledWith(
				'clinicId',
				undefined
			);
			expect(logger.log).toHaveBeenCalledWith('Fetching all services');
		});

		it('should return a list of services for a clinic id with search keyword', async () => {
			const searchKeyword = 'service_name_A';
			const clinicId = 'clinic_id';

			const filteredData = servicesDataList.filter(service =>
				service.serviceName.includes(searchKeyword)
			);

			serviceService.getServices.mockResolvedValue(filteredData);
			const result = await controller.getAllServices(
				clinicId,
				searchKeyword
			);
			expect(result).toEqual(filteredData);
			expect(serviceService.getServices).toHaveBeenCalledWith(
				clinicId,
				searchKeyword
			);
		});
	});

	describe('create', () => {
		const createServiceDto = {
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			uniqueId: 'S_000001',
			serviceName: 'Test Service',
			chargeablePrice: 100,
			tax: 10
		};

		it('should create a new service', async () => {
			const createdService = { ...createServiceDto, id: 'new_uuid' };
			serviceService.create.mockResolvedValue(createdService);

			const result = await controller.create(createServiceDto);

			expect(result).toEqual(createdService);
			expect(serviceService.create).toHaveBeenCalledWith(
				createServiceDto
			);
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle errors during creation', async () => {
			serviceService.create.mockRejectedValue(
				new Error('Creation failed')
			);

			await expect(controller.create(createServiceDto)).rejects.toThrow(
				new HttpException(
					'Error creating service',
					HttpStatus.BAD_REQUEST
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating service',
				{ error: expect.any(Error) }
			);
		});
	});

	describe('update', () => {
		const updateServiceDto = {
			serviceName: 'Updated Service',
			chargeablePrice: 150
		};

		it('should update a service', async () => {
			const updatedService = {
				id: 'service_uuid',
				...updateServiceDto,
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'S_000001',
				tax: 10
			};
			serviceService.update.mockResolvedValue(updatedService);

			const result = await controller.update(
				'service_uuid',
				updateServiceDto
			);

			expect(result).toEqual(updatedService);
			expect(serviceService.update).toHaveBeenCalledWith(
				'service_uuid',
				updateServiceDto
			);
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle errors during update', async () => {
			serviceService.update.mockRejectedValue(new Error('Update failed'));

			await expect(
				controller.update('service_uuid', updateServiceDto)
			).rejects.toThrow(
				new HttpException(
					'Error updating service',
					HttpStatus.BAD_REQUEST
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating service',
				{ error: expect.any(Error) }
			);
		});
	});

	describe('findOne', () => {
		it('should return a service by id', async () => {
			const service: ClinicServiceEntity = {
				id: 'service_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'S_000001',
				serviceName: 'Test Service',
				chargeablePrice: 100,
				tax: 10
			};
			serviceService.findOne.mockResolvedValue(service);

			const result = await controller.findOne('service_uuid');

			expect(result).toEqual(service);
			expect(serviceService.findOne).toHaveBeenCalledWith('service_uuid');
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle errors when fetching service', async () => {
			serviceService.findOne.mockRejectedValue(
				new Error('Service not found')
			);

			await expect(
				controller.findOne('nonexistent_uuid')
			).rejects.toThrow(
				new HttpException(
					'Error fetching service',
					HttpStatus.NOT_FOUND
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching service',
				{ error: expect.any(Error) }
			);
		});
	});

	describe('remove', () => {
		it('should delete a service and return success message', async () => {
			serviceService.remove.mockResolvedValue(undefined);

			const result = await controller.remove('service_uuid');

			expect(result).toEqual({ message: 'Service deleted successfully' });
			expect(serviceService.remove).toHaveBeenCalledWith('service_uuid');
			expect(logger.error).not.toHaveBeenCalled();
		});

		it('should handle errors during deletion', async () => {
			serviceService.remove.mockRejectedValue(
				new Error('Deletion failed')
			);

			await expect(controller.remove('service_uuid')).rejects.toThrow(
				new HttpException(
					'Error deleting service',
					HttpStatus.BAD_REQUEST
				)
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error deleting service',
				{ error: expect.any(Error) }
			);
		});
	});
});
