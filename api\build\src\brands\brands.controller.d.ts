import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';
export declare class BrandController {
    private readonly brandService;
    private readonly logger;
    constructor(brandService: BrandService, logger: WinstonLogger);
    create(createBrandDto: CreateBrandDto): Promise<import("./entities/brand.entity").Brand>;
    findbyId(id: string): Promise<import("./entities/brand.entity").Brand | null>;
    findAll(): Promise<import("./entities/brand.entity").Brand[]>;
    findBySlug(slug: string): Promise<BrandWithSettingsDto | null>;
}
