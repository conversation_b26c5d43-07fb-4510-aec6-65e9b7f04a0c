import { ApiProperty } from '@nestjs/swagger';
import {
	IsEnum,
	IsNotEmpty,
	IsOptional,
	IsString,
	IsUUID
} from 'class-validator';
import { EnumAppointmentStatus } from '../../appointments/enums/enum-appointment-status';

/**
 * DTO for creating a new client booking
 */
export class CreateClientBookingDto {
	@IsUUID()
	@ApiProperty({
		description: 'ID of the pet',
		example: '123e4567-e89b-12d3-a456-************'
	})
	petId: string = '';

	@IsUUID()
	@ApiProperty({
		description: 'ID of the doctor',
		example: '123e4567-e89b-12d3-a456-************'
	})
	doctorId: string = '';

	@IsUUID()
	@ApiProperty({
		description: 'ID of the clinic',
		example: '123e4567-e89b-12d3-a456-************'
	})
	clinicId: string = '';

	@ApiProperty({
		description: 'The date of an appointment'
	})
	@IsNotEmpty()
	// @IsDate()
	date!: string;

	@IsString()
	@ApiProperty({
		description: 'Start time of the appointment (HH:MM)',
		example: '09:00'
	})
	startTime: string = '';

	@IsString()
	@ApiProperty({
		description: 'End time of the appointment (HH:MM)',
		example: '09:30'
	})
	endTime: string = '';

	@IsString()
	@IsOptional()
	@ApiProperty({
		description: 'Optional notes for the appointment',
		required: false,
		example: 'First-time checkup'
	})
	reason?: string;
}

/**
 * DTO for updating a client booking (reschedule or cancel)
 */
export class UpdateClientBookingDto {
	@ApiProperty({
		description: 'The date of an appointment'
	})
	@IsNotEmpty()
	// @IsDate()
	date!: string;

	@IsString()
	@IsOptional()
	@ApiProperty({
		description: 'New start time of the appointment (HH:MM)',
		required: false,
		example: '10:00'
	})
	startTime?: string;

	@IsString()
	@IsOptional()
	@ApiProperty({
		description: 'New end time of the appointment (HH:MM)',
		required: false,
		example: '10:30'
	})
	endTime?: string;

	@IsString()
	@IsEnum(EnumAppointmentStatus)
	@IsOptional()
	@ApiProperty({
		description:
			'New status of the appointment (Note: "Cancelled" will trigger soft-deletion by setting deleted_at)',
		required: false,
		enum: EnumAppointmentStatus,
		example: EnumAppointmentStatus.Cancelled
	})
	status?: EnumAppointmentStatus;

	@IsOptional()
	@IsString()
	@ApiProperty({
		description: 'ID of the doctor',
		example: '123e4567-e89b-12d3-a456-************'
	})
	doctorId?: string;
}

/**
 * DTO for client booking response
 */
export class ClientBookingResponseDto {
	@ApiProperty({
		description: 'ID of the booking',
		example: '123e4567-e89b-12d3-a456-426614174003'
	})
	id: string = '';

	@ApiProperty({
		description: 'ID of the pet',
		example: '123e4567-e89b-12d3-a456-************'
	})
	petId: string = '';

	@ApiProperty({
		description: 'Name of the pet',
		example: 'Max'
	})
	petName: string = '';

	@ApiProperty({
		description: 'ID of the doctor',
		example: '123e4567-e89b-12d3-a456-************'
	})
	doctorId: string = '';

	@ApiProperty({
		description: 'Name of the doctor',
		example: 'Dr. Smith'
	})
	doctorName: string = '';

	@ApiProperty({
		description: 'ID of the clinic',
		example: '123e4567-e89b-12d3-a456-************'
	})
	clinicId: string = '';

	@ApiProperty({
		description: 'Name of the clinic',
		example: 'Pet Care Clinic'
	})
	clinicName: string = '';

	@ApiProperty({
		description: 'Date of the appointment',
		example: '2023-06-01'
	})
	date: string = '';

	@ApiProperty({
		description: 'Start time of the appointment',
		example: '09:00'
	})
	startTime: string = '';

	@ApiProperty({
		description: 'End time of the appointment',
		example: '09:30'
	})
	endTime: string = '';

	@ApiProperty({
		description: 'Status of the appointment',
		enum: EnumAppointmentStatus,
		example: EnumAppointmentStatus.Scheduled
	})
	status: string = '';

	@ApiProperty({
		description: 'Optional notes for the appointment',
		required: false,
		example: 'First-time checkup'
	})
	notes?: string;

	@ApiProperty({
		description: 'When the booking was created',
		example: '2023-05-15T12:00:00Z'
	})
	createdAt: Date = new Date();

	@ApiProperty({
		description: 'When the booking was last updated',
		example: '2023-05-15T12:00:00Z'
	})
	updatedAt: Date = new Date();
}
