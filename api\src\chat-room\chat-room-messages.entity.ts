import {
	<PERSON><PERSON><PERSON>,
	Column,
	PrimaryGenerated<PERSON><PERSON>umn,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn
} from 'typeorm';
// import { ChatRoom } from './chat-room.entity';
import { User } from '../users/entities/user.entity';
import { ChatRoom } from './chat-room.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';

@Entity('chat_room_messages')
export class ChatRoomMessage {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column('uuid', { name: 'sender_id' })
	senderId!: string;

	@Column('uuid', { name: 'chat_room_id' })
	chatRoomId!: string;

	@Column()
	message!: string;

	@Column({
		type: 'jsonb',
		nullable: true,
		name: 'file'
	})
	file!: object;

	@Column({
		type: 'jsonb',
		nullable: true,
		name: 'meta'
	})
	meta!: object;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@ManyToOne(() => ClinicUser, user => user.chatRoomUser)
	@JoinColumn({ name: 'sender_id' })
	user!: User;

	@ManyToOne(() => ChatRoom, chatRoom => chatRoom.users)
	@JoinColumn({ name: 'chat_room_id' })
	chatRoom!: ChatRoom;
}
