import { uuid } from 'aws-sdk/clients/customerprofiles';
import {
	Column,
	CreateDateColumn,
	Entity,
	JoinColumn,
	ManyToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { User } from '../../users/entities/user.entity';

@Entity({ name: 'clinic_plans' })
export class ClinicPlan {
	@PrimaryGeneratedColumn('uuid')
	id!: number;

	@ManyToOne(() => ClinicEntity)
	@JoinColumn({ name: 'clinic_id' })
	@Column('uuid', { nullable: false, name: 'clinic_id' })
	clinicId!: uuid;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ nullable: false })
	name!: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;
}
