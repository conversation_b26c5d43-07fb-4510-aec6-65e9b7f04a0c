import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createBrand, getBrand, getBrands } from './brands.services';

export function useCreateBrand() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createBrand,
        onSuccess: (data) => {
            if (data.status === true) {
                // Invalidate queries to reload the brands data
                queryClient.invalidateQueries({ queryKey: ['brands'] });
            }
        },
        onError: (error) => {
            console.error('Error creating brand:', error);
        },
    });
}


export function useGetAllBrands() {
    return useQuery({
        queryKey: ['brands'],
        queryFn:()=> getBrands(),
    });
}

export function useGetBrand(id:string) {
    return useQuery({
        queryFn:()=> getBrand(id),
        queryKey: ['brands', id],
    });
}
