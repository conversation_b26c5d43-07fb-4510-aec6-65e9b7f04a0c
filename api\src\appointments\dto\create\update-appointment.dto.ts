import { IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { EnumAppointmentStatus } from '../../enums/enum-appointment-status';

// DTO for updating appointment, can add more fields as required
export class UpdateAppointmentsDto {
	@ApiProperty({
		enum: EnumAppointmentStatus,
		description: 'New status of the appointment'
	})
	@IsEnum(EnumAppointmentStatus)
	status?: EnumAppointmentStatus;

	@ApiProperty({
		description:
			'Status is any soap update is pending. If yes, will show up under tasks'
	})
	soapPending?: boolean;

	@ApiProperty({
		description: 'User id of the clinic user'
	})
	userId?: string;
}
