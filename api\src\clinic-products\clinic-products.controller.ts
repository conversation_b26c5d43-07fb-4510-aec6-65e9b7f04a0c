import {
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Patch,
	Post,
	Query,
	UseGuards
} from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiOkResponse,
	ApiOperation,
	ApiQuery,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { ClinicProductsService } from './clinic-products.service';
import { ClinicProductEntity } from './entities/clinic-product.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { CreateProductDto } from './dto/create-products.dto';
import { UpdateProductDto } from './dto/update-products.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Inventories')
@Controller('clinic-products')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicProductsController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly productsService: ClinicProductsService
	) {}

	@ApiOkResponse({
		description: 'Get all product items',
		isArray: true,
		type: ClinicProductEntity
	})
	@ApiQuery({ name: 'search', required: false })
	@ApiQuery({ name: 'clinicId', required: false })
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getAllProducts-clinic-products')
	async getAllProducts(
		@Query('clinicId') clinicId: string,
		@Query('search') searchKeyword?: string
	) {
		try {
			this.logger.log('Fetching all products');
			return await this.productsService.getProducts(
				clinicId,
				searchKeyword
			);
		} catch (error) {
			this.logger.error('Error fetching products', { error });
			throw new HttpException(
				'Error fetching all the products',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create new product' })
	@ApiResponse({ status: 201, description: 'Product created successfully' })
	@TrackMethod('create-clinic-products')
	async create(@Body() createProductDto: CreateProductDto) {
		try {
			return await this.productsService.create(createProductDto);
		} catch (error) {
			this.logger.error('Error creating product', { error });
			throw new HttpException(
				'Error creating product',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Patch(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update product' })
	@TrackMethod('update-clinic-products')
	async update(
		@Param('id') id: string,
		@Body() updateProductDto: UpdateProductDto
	) {
		try {
			return await this.productsService.update(id, updateProductDto);
		} catch (error) {
			this.logger.error('Error updating product', { error });
			throw new HttpException(
				'Error updating product',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get product by id' })
	@TrackMethod('findOne-clinic-products')
	async findOne(@Param('id') id: string) {
		try {
			return await this.productsService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching product', { error });
			throw new HttpException(
				'Error fetching product',
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete product' })
	@TrackMethod('remove-clinic-products')
	async remove(@Param('id') id: string) {
		try {
			await this.productsService.remove(id);
			return { message: 'Product deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting product', { error });
			throw new HttpException(
				'Error deleting product',
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
