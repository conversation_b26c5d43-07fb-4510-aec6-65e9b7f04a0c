import {
	Injectable,
	NotFoundException,
	BadRequestException,
	Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as moment from 'moment';
import 'moment-timezone';

import {
	AvailableDatesResponseDto,
	AvailableTimeSlotsResponseDto,
	TimeSlotDto,
	NextAvailableSlotInfoDto
} from './dto/client-availability.dto';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { AvailabilityService } from '../availability/availability.service';
import { ClinicService } from '../clinics/clinic.service';
import {
	ClientBookingSettings,
	ClientBookingWorkingHours,
	ClientBookingDaySchedule,
	TimeDuration
} from '../clinics/entities/clinic.entity';

// Helper function to convert TimeDuration to total minutes
function timeDurationToMinutes(
	duration: TimeDuration | null | undefined
): number | null {
	if (!duration) return null;
	const days = duration.days || 0;
	const hours = duration.hours || 0;
	const minutes = duration.minutes || 0;
	const totalMinutes = days * 24 * 60 + hours * 60 + minutes;
	// Return null if total is 0, indicating no specific duration set
	return totalMinutes > 0 ? totalMinutes : null;
}

// Define an extended settings interface for internal use
interface EffectiveClientBookingSettings extends ClientBookingSettings {
	minBookingLeadMinutes?: number | null;
	modificationDeadlineMinutes?: number | null;
	maxAdvanceBookingMinutes?: number | null;
}

@Injectable()
export class ClientAvailabilityService {
	private readonly logger = new Logger(ClientAvailabilityService.name);

	constructor(
		@InjectRepository(ClinicUser)
		private clinicUserRepository: Repository<ClinicUser>,
		private readonly availabilityService: AvailabilityService,
		private readonly clinicService: ClinicService
	) {}

	private async getEffectiveBookingSettings(
		clinicId: string | undefined
	): Promise<EffectiveClientBookingSettings> {
		// Define default settings structure including new fields and calculated minutes
		const defaultSettings: EffectiveClientBookingSettings = {
			isEnabled: true,
			workingHours: null,
			allowedDoctorIds: null,
			minBookingLeadTime: null,
			modificationDeadlineTime: null,
			maxAdvanceBookingTime: null,
			minBookingLeadHours: 0,
			modificationDeadlineHours: 0,
			minBookingLeadMinutes: null,
			modificationDeadlineMinutes: null,
			maxAdvanceBookingMinutes: null // Default max advance: unbounded
		};

		if (!clinicId) {
			this.logger.warn(
				'Clinic ID is missing, using default booking settings.'
			);
			return defaultSettings;
		}

		try {
			// Fetch raw settings from ClinicService
			const settings =
				await this.clinicService.getClientBookingSettings(clinicId);

			// Calculate total minutes from TimeDuration objects
			const minLeadMinutes = timeDurationToMinutes(
				settings?.minBookingLeadTime
			);
			const modDeadlineMinutes = timeDurationToMinutes(
				settings?.modificationDeadlineTime
			);
			const maxAdvanceMinutes = timeDurationToMinutes(
				settings?.maxAdvanceBookingTime
			);

			// Construct the effective settings object, prioritizing new TimeDuration fields
			const effectiveSettings: EffectiveClientBookingSettings = {
				isEnabled: settings?.isEnabled ?? defaultSettings.isEnabled,
				allowAllDoctors:
					settings?.allowAllDoctors ??
					defaultSettings.allowAllDoctors,
				workingHours:
					settings?.workingHours ?? defaultSettings.workingHours,
				allowedDoctorIds:
					settings?.allowedDoctorIds ??
					defaultSettings.allowedDoctorIds,

				// New time duration fields (raw)
				minBookingLeadTime:
					settings?.minBookingLeadTime ??
					defaultSettings.minBookingLeadTime,
				modificationDeadlineTime:
					settings?.modificationDeadlineTime ??
					defaultSettings.modificationDeadlineTime,
				maxAdvanceBookingTime:
					settings?.maxAdvanceBookingTime ??
					defaultSettings.maxAdvanceBookingTime,

				// Calculated minutes from new fields
				minBookingLeadMinutes: minLeadMinutes,
				modificationDeadlineMinutes: modDeadlineMinutes,
				maxAdvanceBookingMinutes: maxAdvanceMinutes,

				// Legacy fields (kept for potential backward compatibility checks elsewhere)
				minBookingLeadHours:
					settings?.minBookingLeadHours ??
					defaultSettings.minBookingLeadHours,
				modificationDeadlineHours:
					settings?.modificationDeadlineHours ??
					defaultSettings.modificationDeadlineHours
			};

			return effectiveSettings;
		} catch (error) {
			this.logger.error(
				'Error fetching clinic settings, using defaults',
				{ clinicId, error }
			);
			return defaultSettings;
		}
	}

	async getAvailableDatesForDoctor(
		doctorIdsString: string,
		startDate?: string,
		endDate?: string,
		clinicIdFromQuery?: string
	): Promise<AvailableDatesResponseDto> {
		this.logger.log('Getting available dates with settings', {
			doctorIds: doctorIdsString,
			startDate,
			endDate,
			clinicId: clinicIdFromQuery
		});

		let relevantClinicId: string | undefined = clinicIdFromQuery;
		let targetDoctorIds: string[] = [];

		if (doctorIdsString === 'all') {
			if (!relevantClinicId) {
				throw new BadRequestException(
					"Clinic ID must be provided when requesting dates for 'all' doctors."
				);
			}
			const doctorsInClinic = await this.clinicUserRepository.find({
				where: { clinicId: relevantClinicId },
				select: ['id']
			});
			targetDoctorIds = doctorsInClinic.map(doc => doc.id);
		} else {
			const parsedDoctorIds = doctorIdsString.split(',').map(id => id.trim()).filter(id => id.length > 0);

			if (parsedDoctorIds.length === 0) {
				 throw new BadRequestException('At least one Doctor ID must be provided.');
			}

			if (parsedDoctorIds.length === 1) {
				const singleDoctorId = parsedDoctorIds[0];
				const clinicUser = await this.clinicUserRepository.findOne({
					where: { id: singleDoctorId }
				});
				if (!clinicUser) {
					throw new NotFoundException(
						`Doctor with ID ${singleDoctorId} not found`
					);
				}
				if (relevantClinicId && clinicUser.clinicId !== relevantClinicId) {
					 throw new BadRequestException(
						`Doctor ${singleDoctorId} does not belong to clinic ${relevantClinicId}.`
					);
				}
				relevantClinicId = clinicUser.clinicId;
				targetDoctorIds = [singleDoctorId];
			} else {
				if (!relevantClinicId) {
					throw new BadRequestException(
						"Clinic ID must be provided when requesting dates for multiple specific doctors."
					);
				}
				const clinicUsers = await this.clinicUserRepository.find({
					where: parsedDoctorIds.map(id => ({ id, clinicId: relevantClinicId })),
					select: ['id', 'clinicId']
				});
				
				targetDoctorIds = clinicUsers.map(u => u.id);

				if (targetDoctorIds.length !== parsedDoctorIds.length) {
					const foundIds = new Set(targetDoctorIds);
					const notFoundOrMisMatched = parsedDoctorIds.filter(id => !foundIds.has(id));
					this.logger.warn(`Some doctors were not found in clinic ${relevantClinicId} or do not exist: ${notFoundOrMisMatched.join(', ')}`);
				}
				 if (targetDoctorIds.length === 0 && parsedDoctorIds.length > 0) {
					 throw new NotFoundException(
						`None of the specified doctors (${parsedDoctorIds.join(', ')}) were found in clinic ${relevantClinicId}.`
					);
				}
			}
		}

		// Fetch effective settings including calculated minutes
		const settings =
			await this.getEffectiveBookingSettings(relevantClinicId);

		if (!settings.isEnabled) {
			this.logger.log('Client booking disabled for clinic', {
				clinicId: relevantClinicId
			});
			return { availableDates: [] };
		}

		// Filter target doctors based on settings
		if (
			settings.allowAllDoctors === false &&
			Array.isArray(settings.allowedDoctorIds) &&
			settings.allowedDoctorIds.length > 0
		) {
			targetDoctorIds = targetDoctorIds.filter(id =>
				settings.allowedDoctorIds!.includes(id)
			);
			this.logger.log('Filtered doctors based on allowed list', {
				allowed: targetDoctorIds
			});
		} else {
			this.logger.log(
				'Skipping doctor filter: allowAllDoctors is true or allowedDoctorIds is not restrictive.'
			);
		}

		if (targetDoctorIds.length === 0) {
			this.logger.log('No allowed doctors found for the criteria', {
				clinicId: relevantClinicId
			});
			return { availableDates: [] };
		}

		// --- Date and Time Calculations ---
		let queryStartMoment, queryEndMoment;
		const todayMoment = moment().startOf('day');
		const nowMoment = moment();

		// Calculate max advance booking threshold using minutes (default to 30 days if null)
		const maxAdvanceMinutes = settings.maxAdvanceBookingMinutes;
		const maxAdvanceThreshold = maxAdvanceMinutes
			? nowMoment.clone().add(maxAdvanceMinutes, 'minutes').endOf('day')
			: moment().add(30, 'days').endOf('day'); // Default max advance (30 days)

		try {
			// Determine effective start date (requested or today, whichever is later)
			queryStartMoment =
				startDate && moment(startDate, 'YYYY-MM-DD').isValid()
					? moment(startDate, 'YYYY-MM-DD').startOf('day')
					: todayMoment.clone(); // Use today if no valid start date
			if (queryStartMoment.isBefore(todayMoment)) {
				queryStartMoment = todayMoment.clone(); // Cannot query past dates
			}

			// Determine effective end date (requested or maxAdvanceThreshold, whichever is earlier)
			queryEndMoment =
				endDate && moment(endDate, 'YYYY-MM-DD').isValid()
					? moment(endDate, 'YYYY-MM-DD').endOf('day')
					: maxAdvanceThreshold.clone(); // Use max threshold if no valid end date
			if (queryEndMoment.isAfter(maxAdvanceThreshold)) {
				queryEndMoment = maxAdvanceThreshold.clone(); // Cap end date by max advance
			}

			// Validate start <= end after adjustments
			if (queryStartMoment.isAfter(queryEndMoment)) {
				this.logger.warn('Query range invalid after adjustments', {
					start: queryStartMoment.format(),
					end: queryEndMoment.format()
				});
				return { availableDates: [] }; // No valid range
			}
		} catch (parseError) {
			this.logger.warn('Invalid date format provided', {
				startDate,
				endDate,
				error:
					parseError instanceof Error
						? parseError.message
						: String(parseError)
			});
			throw new BadRequestException(
				'Invalid date format provided. Use YYYY-MM-DD.'
			);
		}
		// --- End Date and Time Calculations ---

		// Get clinic timezone for accurate filtering
		let clinicTimezone = 'UTC';
		if (relevantClinicId) {
			try {
				const clinic = await this.clinicService.getClinicById(relevantClinicId);
				if (clinic?.timezone) {
					clinicTimezone = clinic.timezone;
				} else {
					this.logger.warn(`Clinic ${relevantClinicId} has no timezone or clinic not found, using UTC.`);
				}
			} catch (error) {
				this.logger.warn(`Error fetching clinic ${relevantClinicId} for timezone, using UTC`, {
					error: error instanceof Error ? error.message : String(error)
				});
			}
		} else {
			 this.logger.warn('Relevant clinic ID not determined for timezone, using UTC. This may lead to incorrect date filtering.');
		}

		// Instead of separately checking today and then fetching other dates,
		// use the new method that applies all filters at once
		const filteredDates =
			await this.availabilityService.getFilteredAvailableDatesForDoctors(
				targetDoctorIds,
				queryStartMoment.format('YYYY-MM-DD'),
				queryEndMoment.format('YYYY-MM-DD'),
				{
					clinicTimezone,
					workingHours: settings.workingHours,
					minBookingLeadMinutes: settings.minBookingLeadMinutes,
					slotDurationMinutes: 30 // Default slot duration for filtering
				}
			);

		// Final formatting and sorting
		const finalDates = filteredDates
			.sort((a, b) => a.localeCompare(b))
			.map(date => moment.utc(date).format('YYYY-MM-DD')); // Ensure consistent UTC format

		this.logger.log('Returning filtered available dates', {
			doctorIds: doctorIdsString,
			clinicId: relevantClinicId,
			count: finalDates.length
		});

		return { availableDates: finalDates };
	}

	/**
	 * Get available time slots for a doctor or all doctors on a specific date, considering clinic settings.
	 * Also finds the next available slot for doctors unavailable on the requested date.
	 */
	async getAvailableTimeSlotsForDoctor(
		doctorIdsString: string,
		date: string,
		clinicIdFromQuery?: string
	): Promise<AvailableTimeSlotsResponseDto> {
		this.logger.log('Getting available time slots with settings', {
			doctorIds: doctorIdsString,
			date,
			clinicId: clinicIdFromQuery
		});

		const dateMoment = moment(date, 'YYYY-MM-DD');
		if (!dateMoment.isValid()) {
			throw new BadRequestException(
				`Invalid date format: ${date}. Expected format: YYYY-MM-DD`
			);
		}

		let relevantClinicId: string | undefined = clinicIdFromQuery;
		let requestedDoctorIds: string[] = [];
		const doctorMap = new Map<string, ClinicUser>();
		let isSingleSpecificDoctorRequest = false;
		let singleSpecificDoctorForFallback: ClinicUser | null = null;

		if (doctorIdsString === 'all') {
			if (!relevantClinicId) {
				throw new BadRequestException(
					"Clinic ID must be provided when requesting time slots for 'all' doctors."
				);
			}
			const doctorsInClinic = await this.clinicUserRepository.find({
				where: { clinicId: relevantClinicId },
				relations: ['user', 'clinic']
			});
			if (doctorsInClinic.length === 0) {
				return { date, timeSlots: [], nextAvailableSlots: {} };
			}

			requestedDoctorIds = doctorsInClinic.map(doc => {
				doctorMap.set(doc.id, doc);
				return doc.id;
			});
		} else {
			const parsedDoctorIds = doctorIdsString.split(',').map(id => id.trim()).filter(id => id.length > 0);

			if (parsedDoctorIds.length === 0) {
				 throw new BadRequestException('At least one Doctor ID must be provided.');
			}
			
			if (parsedDoctorIds.length === 1) {
				isSingleSpecificDoctorRequest = true;
				const singleDoctorId = parsedDoctorIds[0];
				const clinicUser = await this.clinicUserRepository.findOne({
					where: { id: singleDoctorId },
					relations: ['user', 'clinic']
				});
				if (!clinicUser) {
					throw new NotFoundException(
						`Doctor with ID ${singleDoctorId} not found`
					);
				}
				if (relevantClinicId && clinicUser.clinicId !== relevantClinicId) {
					throw new BadRequestException(
						`Doctor ${singleDoctorId} is not associated with clinic ${relevantClinicId}`
					);
				}
				relevantClinicId = clinicUser.clinicId;
				requestedDoctorIds = [singleDoctorId];
				doctorMap.set(singleDoctorId, clinicUser);
				singleSpecificDoctorForFallback = clinicUser;
			} else {
				if (!relevantClinicId) {
					throw new BadRequestException(
						"Clinic ID must be provided when requesting time slots for multiple specific doctors."
					);
				}
				const clinicUsers = await this.clinicUserRepository.find({
					where: parsedDoctorIds.map(id => ({ id, clinicId: relevantClinicId })),
					relations: ['user', 'clinic']
				});

				if (clinicUsers.length !== parsedDoctorIds.length) {
					const foundIds = new Set(clinicUsers.map(u => u.id));
					const notFoundInClinic = parsedDoctorIds.filter(id => !foundIds.has(id));
					this.logger.warn(`Some specified doctors were not found in clinic ${relevantClinicId}: ${notFoundInClinic.join(', ')}`);
				}

				if (clinicUsers.length === 0 && parsedDoctorIds.length > 0) {
					this.logger.log(`None of the specified doctors were found in clinic ${relevantClinicId}.`);
					return { date, timeSlots: [], nextAvailableSlots: {} };
				}

				requestedDoctorIds = clinicUsers.map(doc => {
					doctorMap.set(doc.id, doc);
					return doc.id;
				});
				if (requestedDoctorIds.length === 0) {
					 return { date, timeSlots: [], nextAvailableSlots: {} };
				}
			}
		}

		const settings =
			await this.getEffectiveBookingSettings(relevantClinicId);

		if (!settings.isEnabled) {
			this.logger.log('Client booking disabled for clinic', {
				clinicId: relevantClinicId
			});
			return { date, timeSlots: [], nextAvailableSlots: {} };
		}

		let clinicTimezone = 'UTC';
		if (relevantClinicId) {
			try {
				const clinicDetails = await this.clinicService.getClinicById(relevantClinicId);
				if (clinicDetails?.timezone) {
					clinicTimezone = clinicDetails.timezone;
					this.logger.log('Using clinic timezone for time comparisons', {
						clinicId: relevantClinicId, clinicTimezone
					});
				} else {
					this.logger.warn(`Clinic ${relevantClinicId} has no timezone set or clinic not found, using UTC.`);
				}
			} catch (error) {
				this.logger.warn('Error determining clinic timezone from clinicService, using UTC', {
					clinicId: relevantClinicId, error: error instanceof Error ? error.message : String(error)
				});
			}
		} else {
			this.logger.warn('Relevant clinic ID could not be determined for timezone, using UTC.');
		}

		let allowedDoctorIds = [...requestedDoctorIds];
		if (
			settings.allowAllDoctors === false &&
			Array.isArray(settings.allowedDoctorIds) &&
			settings.allowedDoctorIds.length > 0
		) {
			allowedDoctorIds = allowedDoctorIds.filter(id =>
				settings.allowedDoctorIds!.includes(id)
			);
			this.logger.log(
				'Filtered doctors based on allowed list for slots',
				{ allowed: allowedDoctorIds }
			);
		} else {
			this.logger.log(
				'Skipping doctor filter for slots: allowAllDoctors is true or allowedDoctorIds is not restrictive.'
			);
		}

		if (allowedDoctorIds.length === 0) {
			this.logger.log(
				'No allowed doctors found for slots after filtering',
				{
					clinicId: relevantClinicId
				}
			);
			return { date, timeSlots: [], nextAvailableSlots: {} };
		}

		try {
			const nowMoment = moment().tz(clinicTimezone);
			const maxAdvanceMinutes = settings.maxAdvanceBookingMinutes;
			if (maxAdvanceMinutes !== null) {
				const maxAdvanceThreshold = nowMoment
					.clone()
					.add(maxAdvanceMinutes, 'minutes')
					.endOf('day');
				if (dateMoment.isAfter(maxAdvanceThreshold)) {
					this.logger.log(
						'Requested date is beyond maximum advance booking time',
						{
							date,
							clinicTimezone,
							maxAdvanceThreshold: maxAdvanceThreshold.format()
						}
					);
					return { date, timeSlots: [], nextAvailableSlots: {} };
				}
			}
		} catch (error) {
			this.logger.warn(
				'Error in timezone-aware max advance check, using fallback',
				{
					error:
						error instanceof Error ? error.message : String(error)
				}
			);
			const nowMoment = moment();
			const maxAdvanceMinutes = settings.maxAdvanceBookingMinutes;
			if (maxAdvanceMinutes !== null) {
				const maxAdvanceThreshold = nowMoment
					.clone()
					.add(maxAdvanceMinutes, 'minutes')
					.endOf('day');
				if (dateMoment.isAfter(maxAdvanceThreshold)) {
					return { date, timeSlots: [], nextAvailableSlots: {} };
				}
			}
		}

		const slotDuration = 30;
		let fetchedTimeSlotsMap: Map<
			string,
			Array<{ startTime: string; endTime: string; isAvailable: boolean }>
		>;

		if (allowedDoctorIds.length === 0) {
			fetchedTimeSlotsMap = new Map();
		} else if (allowedDoctorIds.length > 1 || (doctorIdsString === 'all' && allowedDoctorIds.length > 0)) {
			fetchedTimeSlotsMap =
				await this.availabilityService.getAvailableTimeSlotsForMultipleDoctors(
					allowedDoctorIds,
					date,
					slotDuration
				);
		} else {
			const singleDoctorToQuery = allowedDoctorIds[0];
			const singleDoctorSlots =
				await this.availabilityService.getAvailableTimeSlotsForDate(
					singleDoctorToQuery,
					date,
					slotDuration
				);
			fetchedTimeSlotsMap = new Map();
			if (singleDoctorSlots.length > 0) {
				fetchedTimeSlotsMap.set(singleDoctorToQuery, singleDoctorSlots);
			}
		}

		this.logger.log('Fetched raw slots from AvailabilityService', {
			usersFound: Array.from(fetchedTimeSlotsMap.keys()),
			totalSlotsFetched: Array.from(fetchedTimeSlotsMap.values()).reduce(
				(sum, slots) => sum + slots.length,
				0
			)
		});

		const finalFilteredSlots: TimeSlotDto[] = [];
		const processedDoctorIds = new Set<string>();

		for (const [docId, rawSlots] of fetchedTimeSlotsMap.entries()) {
			processedDoctorIds.add(docId);
			const user = doctorMap.get(docId);
			const doctorName = user?.user
				? `Dr. ${user.user.firstName || ''} ${user.user.lastName || ''}`.trim()
				: 'Unknown Doctor';

			let userFilteredSlots = rawSlots
				.filter(slot => slot.isAvailable)
				.map(slot => ({ ...slot, doctorId: docId, doctorName }));

			if (settings.workingHours) {
				const dayOfWeek = dateMoment
					.format('dddd')
					.toLowerCase() as keyof ClientBookingWorkingHours;
				const daySchedule = settings.workingHours?.[dayOfWeek];
				const allowedIntervals = Array.isArray(daySchedule)
					? daySchedule.filter(
							interval =>
								interval.isWorkingDay &&
								interval.startTime &&
								interval.endTime
						)
					: [];

				if (allowedIntervals.length > 0) {
					userFilteredSlots = userFilteredSlots.filter(slot => {
						const slotStart = moment.tz(
							`${date} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);
						const slotEnd = moment.tz(
							`${date} ${slot.endTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);

						return allowedIntervals.some(interval => {
							if (!interval.startTime || !interval.endTime)
								return false;
							const intervalStart = moment.tz(
								`${date} ${interval.startTime}`,
								'YYYY-MM-DD HH:mm',
								clinicTimezone
							);
							const intervalEnd = moment.tz(
								`${date} ${interval.endTime}`,
								'YYYY-MM-DD HH:mm',
								clinicTimezone
							);
							return (
								slotStart.isSameOrAfter(intervalStart) &&
								slotEnd.isSameOrBefore(intervalEnd)
							);
						});
					});
				} else {
					userFilteredSlots = [];
				}
			}

			try {
				if (settings.minBookingLeadMinutes) {
					const leadTimeThreshold = moment()
						.tz(clinicTimezone)
						.add(settings.minBookingLeadMinutes, 'minutes');
					userFilteredSlots = userFilteredSlots.filter(slot => {
						const slotStartTime = moment.tz(
							`${date} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);
						return slotStartTime.isAfter(leadTimeThreshold);
					});
				}
			} catch (error) {
				this.logger.warn(
					'Error in timezone-aware lead time check, using fallback',
					{
						error:
							error instanceof Error
								? error.message
								: String(error)
					}
				);
				if (settings.minBookingLeadMinutes) {
					const leadTimeThreshold = moment().add(
						settings.minBookingLeadMinutes,
						'minutes'
					);
					userFilteredSlots = userFilteredSlots.filter(slot => {
						const slotStartTime = moment(
							`${date} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm'
						);
						return slotStartTime.isAfter(leadTimeThreshold);
					});
				}
			}

			try {
				if (dateMoment.isSame(moment(), 'day')) {
					const nowInClinicTz = moment().tz(clinicTimezone);
					userFilteredSlots = userFilteredSlots.filter(slot => {
						const slotStartTime = moment.tz(
							`${date} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);
						return slotStartTime.isAfter(nowInClinicTz);
					});
				}
			} catch (error) {
				this.logger.warn(
					'Error in timezone-aware past slot filtering, using fallback',
					{
						error:
							error instanceof Error
								? error.message
								: String(error)
					}
				);
				if (dateMoment.isSame(moment(), 'day')) {
					const now = moment();
					userFilteredSlots = userFilteredSlots.filter(slot => {
						const slotStartTime = moment(
							`${date} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm'
						);
						return slotStartTime.isAfter(now);
					});
				}
			}

			if (
				isSingleSpecificDoctorRequest &&
				singleSpecificDoctorForFallback &&
				docId === singleSpecificDoctorForFallback.id &&
				userFilteredSlots.length === 0
			) {
				this.logger.log(
					'No slots after filtering for specific doctor, using fallback with SETTINGS working hours',
					{ doctorId: docId, date }
				);
				const fallbackSlots = this.generateSlotsFromWorkingHours(
					singleSpecificDoctorForFallback,
					settings,
					dateMoment,
					slotDuration
				);
				let filteredFallback = fallbackSlots.filter(
					slot => slot.isAvailable
				);
				try {
					if (settings.minBookingLeadMinutes) {
						const leadTimeThreshold = moment()
							.tz(clinicTimezone)
							.add(settings.minBookingLeadMinutes, 'minutes');
						filteredFallback = filteredFallback.filter(slot => {
							const slotStartTime = moment.tz(
								`${date} ${slot.startTime}`,
								'YYYY-MM-DD HH:mm',
								clinicTimezone
							);
							return slotStartTime.isAfter(leadTimeThreshold);
						});
					}
				} catch (e) {
					/* ignore fallback error */
				}
				try {
					if (dateMoment.isSame(moment(), 'day')) {
						const nowInClinicTz = moment().tz(clinicTimezone);
						filteredFallback = filteredFallback.filter(slot => {
							const slotStartTime = moment.tz(
								`${date} ${slot.startTime}`,
								'YYYY-MM-DD HH:mm',
								clinicTimezone
							);
							return slotStartTime.isAfter(nowInClinicTz);
						});
					}
				} catch (e) {
					/* ignore fallback error */
				}

				// Re-apply past time
				try {
					if (dateMoment.isSame(moment(), 'day')) {
						const nowInClinicTz = moment().tz(clinicTimezone);
						filteredFallback = filteredFallback.filter(slot => {
							const slotStartTime = moment.tz(
								`${date} ${slot.startTime}`,
								'YYYY-MM-DD HH:mm',
								clinicTimezone
							);
							return slotStartTime.isAfter(nowInClinicTz);
						});
					}
				} catch (e) {
					/* ignore fallback error */
				}

				// Assign the filtered fallback slots.
				userFilteredSlots = filteredFallback.map(slot => ({
					...slot,
					doctorId: slot.doctorId!, 
					doctorName: slot.doctorName! 
				}));
			} else if (userFilteredSlots.length === 0) {
				this.logger.log('No slots found for doctor after filtering (or not a single specific request eligible for fallback)', {
					docId,
					date
				});
			}

			finalFilteredSlots.push(...userFilteredSlots);
		}

		const unavailableDoctorIds = allowedDoctorIds.filter(id => {
			const hasAvailableSlots = finalFilteredSlots.some(
				slot => slot.doctorId === id && slot.isAvailable
			);
			return !hasAvailableSlots;
		});

		const nextAvailableSlotsMap: Record<
			string,
			NextAvailableSlotInfoDto | null
		> = {};

		if (unavailableDoctorIds.length > 0) {
			this.logger.log(
				'Finding next available slots for unavailable doctors',
				{ unavailableDoctorIds }
			);
			const nextSlotPromises = unavailableDoctorIds.map(id =>
				this.availabilityService
					.getNextAvailableSlot(id, date, '00:00')
					.then(slot => ({ doctorId: id, slot }))
			);

			const nextSlotResults = await Promise.allSettled(nextSlotPromises);

			nextSlotResults.forEach(result => {
				if (result.status === 'fulfilled') {
					nextAvailableSlotsMap[result.value.doctorId] =
						result.value.slot;
				} else {
					this.logger.error(
						'Failed to get next available slot for doctor',
						{
							error: result.reason
						}
					);
				}
			});
		}

		const sortedTimeSlots = this.sortTimeSlots(
			finalFilteredSlots.filter(slot => slot.isAvailable)
		);

		const response: AvailableTimeSlotsResponseDto = {
			date,
			timeSlots: sortedTimeSlots
		};

		if (Object.keys(nextAvailableSlotsMap).length > 0) {
			response.nextAvailableSlots = nextAvailableSlotsMap;
		}

		return response;
	}

	private generateSlotsFromWorkingHours(
		clinicUser: ClinicUser,
		settings: ClientBookingSettings,
		dateMoment: moment.Moment,
		slotDuration: number
	): TimeSlotDto[] {
		const generatedSlots: TimeSlotDto[] = [];
		const date = dateMoment.format('YYYY-MM-DD');
		const dayOfWeek = dateMoment
			.format('dddd')
			.toLowerCase() as keyof ClientBookingWorkingHours;
		const workingHoursSettings = settings.workingHours;
		const daySchedule: ClientBookingDaySchedule[] | undefined =
			workingHoursSettings ? workingHoursSettings[dayOfWeek] : undefined;

		this.logger.log('Generating fallback slots using CLIENT settings', {
			clinicUserId: clinicUser.id,
			date,
			dayOfWeek
		});

		if (Array.isArray(daySchedule)) {
			for (const workingInterval of daySchedule) {
				if (
					!workingInterval.isWorkingDay ||
					!workingInterval.startTime ||
					!workingInterval.endTime ||
					workingInterval.startTime === 'null' ||
					workingInterval.endTime === 'null'
				) {
					continue;
				}

				try {
					const startWork = moment(
						`${date} ${workingInterval.startTime}`,
						'YYYY-MM-DD HH:mm'
					);
					const endWork = moment(
						`${date} ${workingInterval.endTime}`,
						'YYYY-MM-DD HH:mm'
					);

					if (
						!startWork.isValid() ||
						!endWork.isValid() ||
						startWork.isSameOrAfter(endWork)
					) {
						this.logger.warn(
							'Invalid time interval in settings for fallback generation',
							{ date, interval: workingInterval }
						);
						continue;
					}

					let currentSlotStart = startWork.clone();

					while (currentSlotStart.isBefore(endWork)) {
						const currentSlotEnd = currentSlotStart
							.clone()
							.add(slotDuration, 'minutes');

						if (currentSlotEnd.isAfter(endWork)) break;

						generatedSlots.push({
							startTime: currentSlotStart.format('HH:mm'),
							endTime: currentSlotEnd.format('HH:mm'),
							isAvailable: true,
							doctorId: clinicUser.id,
							doctorName: clinicUser.user
								? `Dr. ${clinicUser.user.firstName || ''} ${clinicUser.user.lastName || ''}`.trim()
								: 'Unknown Doctor'
						});
						currentSlotStart = currentSlotEnd;
					}
				} catch (e) {
					this.logger.error(
						'Error processing working hour interval during fallback generation',
						{ interval: workingInterval, error: e }
					);
				}
			}
		} else {
			this.logger.log(
				'No working hours defined in client settings for this day, generating no fallback slots',
				{ clinicUserId: clinicUser.id, date, dayOfWeek }
			);
		}

		this.logger.log('Generated fallback slots count:', {
			count: generatedSlots.length
		});
		return generatedSlots;
	}

	private sortTimeSlots(timeSlots: TimeSlotDto[]): TimeSlotDto[] {
		if (timeSlots.length === 0) {
			return [];
		}

		return [...timeSlots].sort((a, b) => {
			const startTimeCompare = a.startTime.localeCompare(b.startTime);
			if (startTimeCompare !== 0) {
				return startTimeCompare;
			}
			if (a.doctorName && b.doctorName) {
				return a.doctorName.localeCompare(b.doctorName);
			}
			return 0;
		});
	}
}
