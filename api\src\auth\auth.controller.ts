import {
	Controller,
	Post,
	Body,
	UsePipes,
	ValidationPipe,
	UseGuards,
	Req,
	Query,
	HttpException,
	HttpStatus,
	ConflictException,
	BadRequestException,
	NotFoundException,
	InternalServerErrorException,
	UnauthorizedException
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { VerifyOtpDto, PinLoginDto } from './dto/auth.dto';
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth
} from '@nestjs/swagger';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { Request } from 'express';
import { User } from '../users/entities/user.entity';
import { ValidateOtpDto } from '../user-otps/dto/validate-user-otp.dto';
import { ResetPinDto } from '../users/dto/user.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

export interface RequestWithUser extends Request {
	user: {
		id: string;
		role: Role;
	};
}
@ApiTags('Auth')
@Controller('auth')
export class AuthController {
	constructor(private authService: AuthService) {}

	@Post('login/users/email')
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Login using email and OTP' })
	@ApiResponse({
		status: 200,
		description: 'User logged in successfully.',
		type: User
	})
	@ApiResponse({ status: 401, description: 'Invalid OTP or User not found.' })
	@TrackMethod('loginByEmail-auth')
	async loginByEmail(@Body() validateOtpDto: ValidateOtpDto) {
		try {
			console.log(validateOtpDto);
			const user = await this.authService.loginByEmail(validateOtpDto);
			return user;
		} catch (error) {
			if (error instanceof ConflictException || BadRequestException) {
				throw error;
			}
			throw new HttpException(
				'Failed to login by email',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Post('verify-otp')
	@UsePipes(ValidationPipe)
	@ApiOperation({
		summary: 'Verify OTP (Step 2: Verify OTP and get access token)'
	})
	@ApiResponse({
		status: 200,
		description: 'OTP verified successfully. Access token provided.'
	})
	@ApiResponse({ status: 400, description: 'Bad request or invalid OTP.' })
	@TrackMethod('verifyOtp-auth')
	async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
		return this.authService.verifyOtp(verifyOtpDto);
	}

	@Post('login/pin')
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Login with PIN' })
	@ApiResponse({ status: 200, description: 'Login successful.' })
	@ApiResponse({ status: 400, description: 'Invalid PIN.' })
	@TrackMethod('loginPin-auth')
	async loginPin(@Body() pinLoginDto: PinLoginDto) {
		return await this.authService.loginPin(pinLoginDto);
	}

	@Post('reset-pin')
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Reset user PIN' })
	@ApiResponse({ status: 200, description: 'PIN reset successfully.' })
	@ApiResponse({ status: 404, description: 'User not found.' })
	@ApiResponse({ status: 500, description: 'Failed to reset PIN.' })
	@TrackMethod('resetPin-auth')
	async resetPin(@Body() resetPinDto: ResetPinDto) {
		try {
			await this.authService.resetPin(resetPinDto.email);
			return { message: 'PIN reset successfully' };
		} catch (error) {
			if (error instanceof NotFoundException) {
				throw new NotFoundException('User not found');
			} else if (error instanceof InternalServerErrorException) {
				throw new InternalServerErrorException('Failed to reset PIN');
			}
			throw error;
		}
	}

	@Post('verify-pin')
	@UseGuards(JwtAuthGuard)
	@UsePipes(ValidationPipe)
	@ApiOperation({ summary: 'Verify user PIN for authorization' })
	@ApiResponse({ status: 200, description: 'PIN verified successfully.' })
	@ApiResponse({ status: 401, description: 'Invalid PIN.' })
	@ApiResponse({ status: 404, description: 'User not found.' })
	@TrackMethod('verifyPin-auth')
	async verifyPin(
		@Body() body: { pin: string },
		@Req() req: RequestWithUser
	) {
		try {
			const result = await this.authService.verifyPin(
				body.pin,
				req.user.id
			);
			return result;
		} catch (error) {
			if (error instanceof NotFoundException) {
				throw new NotFoundException('User not found');
			} else if (error instanceof UnauthorizedException) {
				throw new UnauthorizedException('Invalid PIN');
			}
			throw new HttpException(
				'Failed to verify PIN',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
