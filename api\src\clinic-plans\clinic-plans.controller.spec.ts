import { Test, TestingModule } from '@nestjs/testing';
import { ClinicPlansController } from './clinic-plans.controller';
import { ClinicPlansService } from './clinic-plans.service';
import { ClinicPlan } from './entities/clinic-plan.entity';
import { User } from '../users/entities/user.entity';
import { EnumPlanType } from './enums/enum-plan-type';

describe('ClinicPlansController', () => {
	let controller: ClinicPlansController;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let service: ClinicPlansService;

	const mockClinicPlansService = {
		findAll: jest.fn()
	};

	const clinicPlans: ClinicPlan[] = [
		{
			id: 1,
			name: 'Basic Plan',
			clinicId: '',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			brandId: ''
		},
		{
			id: 2,
			name: 'Premium Plan',
			clinicId: '',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			brandId: ''
		}
	];

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicPlansController],
			providers: [
				{
					provide: ClinicPlansService,
					useValue: mockClinicPlansService
				}
			]
		}).compile();

		controller = module.get<ClinicPlansController>(ClinicPlansController);
		service = module.get<ClinicPlansService>(ClinicPlansService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('getClinicPlans', () => {
		it('should return an array of clinic plans without a search keyword', async () => {
			mockClinicPlansService.findAll.mockResolvedValue(clinicPlans);

			const result = await controller.getClinicPlans('clinic-id');
			expect(result).toEqual(clinicPlans);
			expect(mockClinicPlansService.findAll).toHaveBeenCalledWith(
				'clinic-id',undefined, []
			);
		});

		it('should return an array of clinic plans with a search keyword and an empty exclude', async () => {
			const searchKeyword = 'Basic';
			const filteredPlans = clinicPlans.filter(plan =>
				plan.name.includes(searchKeyword)
			);

			mockClinicPlansService.findAll.mockResolvedValue(filteredPlans);

			const result = await controller.getClinicPlans('clinic-id',searchKeyword);
			expect(result).toEqual(filteredPlans);
			expect(mockClinicPlansService.findAll).toHaveBeenCalledWith(
				'clinic-id',searchKeyword, []
			);
		});

		it('should return an array of clinic plans with exclude Labreport value', async () => {
			const filteredPlans = clinicPlans;
		
			mockClinicPlansService.findAll.mockResolvedValue(filteredPlans);

			const result = await controller.getClinicPlans('clinic-id',undefined, EnumPlanType.Labreport);
			expect(result).toEqual(filteredPlans);
			expect(mockClinicPlansService.findAll).toHaveBeenCalledWith(
				'clinic-id',undefined, ['Labreport']
			);
		});
	});
});
