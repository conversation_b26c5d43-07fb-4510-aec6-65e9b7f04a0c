import { Column, Entity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { CartItemEntity } from '../../cart-items/entities/cart-item.entity';

@Entity('clinic_services')
export class ClinicServiceEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ name: 'unique_id' })
	uniqueId!: string;

	@Column({ name: 'service_name' })
	serviceName!: string;

	@Column({ name: 'chargeable_price' })
	chargeablePrice!: number;

	@Column({ name: 'tax' })
	tax!: number;

	@Column({ type: 'boolean', name: 'euthanasia', default: false })
	euthanasia!: boolean;

	@Column({ type: 'boolean', name: 'neutered_spayed', default: false })
	neuteredSpayed!: boolean;

	@OneToOne(() => CartItemEntity, cart => cart.product)
	cart?: CartItemEntity;
}
