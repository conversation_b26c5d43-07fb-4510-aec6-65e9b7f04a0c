import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsUUID } from "class-validator";

export class CreateClinicIdexxTestItemDto {
    @ApiProperty({
		description: 'The clinic id',
		example: 'uuid'
	})
    @IsNotEmpty()
    @IsUUID()
    clinicId!: string;

    @ApiProperty({
		description: 'The IDEXX test name',
		example: 'string'
	})
    name!: string;

    @ApiProperty({
		description: 'The IDEXX test description',
		example: 'string'
	})
    description?: string;

    @ApiProperty({
		description: 'The IDEXX test item chargeable price',
		example: 'string'
	})
    chargeablePrice!: number;

    @ApiProperty({
		description: 'The IDEXX test item tax',
		example: 'string'
	})
    tax!: number;

	@ApiProperty({
		description: 'The type',
		example: 'IDEXX'
	})
    integrationType!: string;
}
