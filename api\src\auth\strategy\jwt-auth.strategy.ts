import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { SessionService } from '../../session/session.service';
import { Role } from '../../roles/role.enum';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor(
		private configService: ConfigService,
		private readonly sessionService: SessionService
	) {
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			ignoreExpiration: false,
			secretOrKey: configService.get<string>('JWT_SECRET')
		});
	}

	async validate(payload: any) {
		// Enforce single session for all roles except SUPER_ADMIN
		if (payload?.sid && payload?.role !== Role.SUPER_ADMIN) {
			const currentSid = await this.sessionService.getUserSession(
				payload.sub
			);
			if (currentSid && currentSid !== payload.sid) {
				throw new UnauthorizedException('SESSION_ENDED');
			}
		}

		return {
			id: payload.sub,
			email: payload.email,
			roleId: payload.role,
			owner: payload.owner,
			userId: payload.sub,
			role: payload.role,
			clinicId: payload.clinicId,
			brandId: payload.brandId
		};
	}
}
