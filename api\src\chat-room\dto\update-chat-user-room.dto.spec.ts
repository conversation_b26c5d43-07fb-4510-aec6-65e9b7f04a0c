
import { validate } from 'class-validator';
import { UpdateChatUserRoomDto } from './update-chat-user-room.dto';

describe('update chat user room dto', () => {
	it('should validate successfully with valid data', async () => {
		const dto = new UpdateChatUserRoomDto();
		dto.lastMessage = 'hey there !';
        dto.unreadMessage = 2
		
		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should not fail if last message is empty string', async () => {
		const dto = new UpdateChatUserRoomDto();
		dto.lastMessage = '';
		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});
});
