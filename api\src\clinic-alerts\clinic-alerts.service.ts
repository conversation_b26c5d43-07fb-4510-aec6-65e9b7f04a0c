import { Injectable, NotFoundException } from '@nestjs/common';
import { ClinicAlerts } from './entities/clinicAlerts.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateClinicAlertDto } from './dto/create-clinicAlerts.dto';
import { UpdateClinicAlertsDto } from './dto/update-clinicAlerts.dto';
@Injectable()
export class ClinicAlertsService {
	constructor(
		@InjectRepository(ClinicAlerts)
		private clinicAlertsRepository: Repository<ClinicAlerts>
	) {}

	async createClinicAlerts(
		createClinicAlert: CreateClinicAlertDto,
		brandId: string
	): Promise<ClinicAlerts> {
		return this.clinicAlertsRepository.save({
			...createClinicAlert,
			brandId: brandId
		});
	}

	async getClinicAlerts(
		clinicId: string,
		search?: string
	): Promise<ClinicAlerts[]> {
		return this.clinicAlertsRepository.find({
			where: { clinicId, alertName: search }
		});
	}

	async deleteClinicAlert(id: string) {
		const result = await this.clinicAlertsRepository.delete(id);

		if (result.affected === 0) {
			throw new NotFoundException(
				`This clinic-alerts with id: ${id} doesn't exist`
			);
		}
	}

	async updateClinicAlerts(
		id: string,
		updateClinicAlertDto: UpdateClinicAlertsDto
	): Promise<ClinicAlerts> {
		const clinicAlert = await this.clinicAlertsRepository.findOne({
			where: { id }
		});

		if (!clinicAlert) {
			throw new NotFoundException(
				`clinic-alert with ID "${id}" not found`
			);
		}
		Object.assign(clinicAlert, updateClinicAlertDto);

		const updatedClinicAlert =
			await this.clinicAlertsRepository.save(clinicAlert);
		return updatedClinicAlert;
	}
}
