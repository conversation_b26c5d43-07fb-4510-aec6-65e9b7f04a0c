import { validate } from 'class-validator';
import { CreateChatMessageDto } from './create-chat-message.dto';

describe('create chat message dto', () => {
	it('should validate successfully with valid data', async () => {
		const dto = new CreateChatMessageDto();
		dto.chatRoomId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
		dto.message = 'fever';
		dto.otherUserId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.senderId = '9c1aaa0e-847c-4bca-9da0-72960eaa269d'
		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should fail if senderId and message is not passed', async () => {
		const dto = new CreateChatMessageDto();
		dto.senderId = '';
		dto.message = '';
        dto.chatRoomId= '9c1aaa0e-847c-4bca-9da0-72960eaa269d'
		const errors = await validate(dto);
		expect(errors.length).toBe(2);
	});
});
