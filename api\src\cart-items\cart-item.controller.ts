import {
	BadRequestException,
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Post,
	Put,
	Query,
	Req,
	UseGuards
} from '@nestjs/common';
import { CartItemService } from './cart-item.service';
import { CreateCartItemDto } from './dto/create-cart-item.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CartItemEntity } from './entities/cart-item.entity';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { BulkInsertIntoCartItemDto } from './dto/bulkInsert-cartItem.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
@ApiTags('Cart')
@Controller('cart-items')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CartItemController extends ApiDocumentationBase {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly cartService: CartItemService
	) {
		super();
	}

	@ApiOkResponse({
		description: 'Creates a new cart item',
		type: CartItemEntity
	})
	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createCartItem-cart-item')
	async createCartItem(
		@Body() createCartDto: CreateCartItemDto,
		@Req() req: { user: { id: string; clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Creating new cart item', { dto: createCartDto });

			return await this.cartService.createCartItem(createCartDto, {
				clinicId: req.user.clinicId,
				brandId: req.user.brandId,
				userId: req.user.id
			});
		} catch (error) {
			this.logger.error('Error creating new cart item', {
				error,
				createCartDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@ApiOkResponse({
		description:
			'Deletes the cart item for a valid id and returns true else returns false'
	})
	@Delete(':cartId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteCartItem-cart-item')
	async deleteCartItem(
		@Param('cartId') cartId: string,
		@Query('source') source?: string
	) {
		try {
			this.logger.log('Fetching cart item by ID', { cartId, source });
			return await this.cartService.deleteCartItem(cartId, source);
		} catch (error) {
			this.logger.error('Error deleting cart item by ID', { error });
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@ApiOkResponse({
		description: 'Gets a cart list for a given appointment id or cart id',
		isArray: true,
		type: CartItemEntity
	})
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getCartItemDetailsByAppointmentId-cart-item')
	async getCartItemDetails(
		@Query('appointmentId') appointmentId?: string,
		@Query('cartId') cartId?: string
	) {
		try {
			this.logger.log('Fetching cart list', {
				appointmentId,
				cartId
			});

			if (cartId) {
				return await this.cartService.getCartItemDetailsByCartId(
					cartId
				);
			}
			if (appointmentId) {
				return await this.cartService.getCartItemDetailsByAppointmentId(
					appointmentId
				);
			}
			throw new BadRequestException(
				'Either appointmentId or cartId is required'
			);
		} catch (error) {
			this.logger.error('Error fetching cart list', { error });
			// If it's already a BadRequestException, re-throw it as is
			if (error instanceof BadRequestException) {
				throw error;
			}
			throw new HttpException(
				(error as Error).message,
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Put(':cartId/details')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('updateCartItemDetails-cart-item')
	async updateCartItemDetails(
		@Param('cartId') cartId: string,
		@Body() updateCartDto: UpdateCartItemDto,
		@Query('source') source?: string
	) {
		try {
			this.logger.log('Updating cart item details', {
				updateCartDto
			});
			return await this.cartService.updateCartItemDetails(
				cartId,
				updateCartDto,
				source
			);
		} catch (error) {
			this.logger.error('Error updating cart itemdetails', { error });

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Post('/bulkInsert')
	@TrackMethod('createCartAndBulkInsert-cart-item')
	async createCartAndBulkInsert(@Body() dto: BulkInsertIntoCartItemDto) {
		try {
			this.logger.log('Bulk inserting cart items', { dto });
			return await this.cartService.createCartAndBulkInsert(dto);
		} catch (error) {
			this.logger.error('Error in bulk insert cart items', error);
			throw new BadRequestException(error);
		}
	}
}
