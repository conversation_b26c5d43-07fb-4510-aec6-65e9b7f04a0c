import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches, IsOptional } from 'class-validator';

export class DirectLoginDto {
	@ApiProperty({
		description: 'The phone number of the pet owner without country code',
		example: '9876543210'
	})
	@IsNotEmpty()
	@IsString()
	@Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits' })
	phoneNumber!: string;

	@ApiProperty({
		description: 'The country code for the phone number',
		example: '91',
		default: '91'
	})
	@IsOptional()
	@IsString()
	@Matches(/^[0-9]+$/, { message: 'Country code must contain only digits' })
	countryCode?: string = '91';

	@ApiProperty({
		description: 'The brand ID',
		example: '123e4567-e89b-12d3-a456-426614174000'
	})
	@IsNotEmpty()
	@IsString()
	brandId!: string;
}
