import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, IsUUID } from 'class-validator';

export class CreateConsumableDto {
	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	clinicId!: string;

	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	brandId!: string;

	// @ApiProperty()
	// @IsString()
	// @IsNotEmpty()
	// uniqueId!: string;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	productName!: string;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	currentStock!: number;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	minimumQuantity!: number;
}
