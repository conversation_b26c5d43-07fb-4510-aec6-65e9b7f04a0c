import {
	Injectable,
	HttpException,
	HttpStatus,
	NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection, MoreThanOrEqual, Brackets, In } from 'typeorm';
import { CreditTransactionEntity } from './entities/credit-transaction.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { CreditTransactionType } from './enums/enum-credit-transaction-type';
import { DerivedCreditTransactionType } from './enums/enum-derived-credit-transaction-type';
import { OwnerCreditTransactionsFiltersDto } from './dto/owner-credit-transactions-filters.dto';
import {
	PaginatedCreditTransactionsResponseDto,
	CreditTransactionResponseDto
} from './dto/credit-transaction-response.dto';
import { User } from '../users/entities/user.entity';

@Injectable()
export class CreditsService {
	constructor(
		@InjectRepository(CreditTransactionEntity)
		private creditTransactionRepository: Repository<CreditTransactionEntity>,
		@InjectRepository(OwnerBrand)
		private ownerBrandRepository: Repository<OwnerBrand>,
		@InjectRepository(InvoiceEntity)
		private invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(User)
		private userRepository: Repository<User>,
		private connection: Connection,
		private readonly logger: WinstonLogger
	) {}

	/**
	 * Add credits to an owner's account
	 */
	async addOwnerCredit(
		ownerId: string,
		amount: number,
		description: string,
		userId: string,
		clinicId: string,
		brandId: string,
		metadata?: Record<string, any>
	): Promise<{ transaction: CreditTransactionEntity; newBalance: number }> {
		this.logger.log('Adding credits to owner', {
			ownerId,
			amount,
			description,
			userId,
			clinicId,
			brandId
		});

		const queryRunner = this.connection.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			// Get owner with lock
			const owner = await queryRunner.manager.findOne(OwnerBrand, {
				where: { id: ownerId },
				lock: { mode: 'pessimistic_write' }
			});

			if (!owner) {
				throw new HttpException(
					'Owner not found',
					HttpStatus.NOT_FOUND
				);
			}

			// Add credits to owner
			const newBalance = Number(owner.ownerCredits || 0) + Number(amount);
			owner.ownerCredits = newBalance;
			await queryRunner.manager.save(owner);

			// Create transaction entity with explicit user IDs
			const transaction = new CreditTransactionEntity();
			transaction.ownerId = ownerId;
			transaction.amount = amount;
			transaction.transactionType = CreditTransactionType.ADD;
			transaction.description = description;
			transaction.clinicId = clinicId;
			transaction.brandId = brandId;
			transaction.createdBy = userId;
			transaction.updatedBy = userId;
			transaction.metadata = metadata;

			const savedTransaction =
				await queryRunner.manager.save(transaction);

			await queryRunner.commitTransaction();

			return {
				transaction: savedTransaction,
				newBalance
			};
		} catch (error) {
			this.logger.error('Error adding credits to owner', error);
			await queryRunner.rollbackTransaction();
			throw error;
		} finally {
			await queryRunner.release();
		}
	}

	/**
	 * Get owner credit balance
	 */
	async getOwnerCreditBalance(ownerId: string) {
		const owner = await this.ownerBrandRepository.findOne({
			where: { id: ownerId }
		});

		if (!owner) {
			throw new HttpException('Owner not found', HttpStatus.NOT_FOUND);
		}

		return {
			ownerId: owner.id,
			creditBalance: owner.ownerCredits,
			ownerBalance: owner.ownerBalance
		};
	}

	/**
	 * Get credit transaction history for an owner
	 */
	async getOwnerCreditTransactions(ownerId: string) {
		const transactions = await this.creditTransactionRepository.find({
			where: { ownerId },
			order: { createdAt: 'DESC' },
			relations: ['invoice', 'paymentDetail']
		});

		return transactions;
	}

	/**
	 * Get paginated credit transactions for an owner with filtering
	 */
	async getOwnerCreditTransactionsPaginated(
		ownerId: string,
		filters: OwnerCreditTransactionsFiltersDto
	): Promise<PaginatedCreditTransactionsResponseDto> {
		this.logger.log('Fetching paginated credit transactions for owner', {
			ownerId,
			filters
		});

		try {
			// Validate owner exists
			const owner = await this.ownerBrandRepository.findOne({
				where: { id: ownerId }
			});

			if (!owner) {
				throw new NotFoundException(
					`Owner with ID ${ownerId} not found`
				);
			}

			// Set default pagination values if not provided
			const page = filters.page || 1;
			const limit = filters.limit || 20;
			const skip = (page - 1) * limit;

			// Build the base query
			const queryBuilder = this.creditTransactionRepository
				.createQueryBuilder('transaction')
				.leftJoinAndSelect('transaction.invoice', 'invoice')
				.leftJoinAndSelect('transaction.paymentDetail', 'paymentDetail')
				.where('transaction.ownerId = :ownerId', { ownerId });

			// Apply filters
			if (filters.transactionType) {
				queryBuilder.andWhere(
					'transaction.transactionType = :transactionType',
					{
						transactionType: filters.transactionType
					}
				);
			}

			// Apply derived transaction types filter (supports multiple types)
			if (filters.derivedTransactionTypes) {
				const derivedTypes = filters.derivedTransactionTypes.split(',');

				if (derivedTypes.length > 0) {
					queryBuilder.andWhere(
						'transaction.derived_transaction_type IN (:...derivedTypes)',
						{ derivedTypes }
					);
				}
			}

			if (filters.startDate && filters.endDate) {
				// Convert to Date objects and set time to start/end of day
				const startDate = new Date(filters.startDate);
				startDate.setHours(0, 0, 0, 0);

				const endDate = new Date(filters.endDate);
				endDate.setHours(23, 59, 59, 999);

				queryBuilder.andWhere(
					'transaction.createdAt BETWEEN :startDate AND :endDate',
					{
						startDate,
						endDate
					}
				);
			} else if (filters.startDate) {
				const startDate = new Date(filters.startDate);
				startDate.setHours(0, 0, 0, 0);

				queryBuilder.andWhere('transaction.createdAt >= :startDate', {
					startDate
				});
			} else if (filters.endDate) {
				const endDate = new Date(filters.endDate);
				endDate.setHours(23, 59, 59, 999);

				queryBuilder.andWhere('transaction.createdAt <= :endDate', {
					endDate
				});
			}

			if (filters.userId) {
				// Handle comma-separated user IDs
				const userIds = filters.userId.split(',');
				queryBuilder.andWhere(
					'transaction.createdBy IN (:...userIds)',
					{ userIds }
				);
			}

			if (filters.searchTerm) {
				const searchTerm = filters.searchTerm.trim();
				if (searchTerm) {
					queryBuilder.andWhere(
						new Brackets(qb => {
							qb.where(
								'transaction.description ILIKE :searchTerm',
								{ searchTerm: `%${searchTerm}%` }
							)
								.orWhere(
									'invoice.referenceAlphaId ILIKE :searchTerm',
									{ searchTerm: `%${searchTerm}%` }
								)
								.orWhere(
									'paymentDetail.referenceAlphaId ILIKE :searchTerm',
									{ searchTerm: `%${searchTerm}%` }
								);
						})
					);
				}
			}

			// Count total before applying pagination
			const total = await queryBuilder.getCount();

			// Apply sorting and pagination
			queryBuilder
				.orderBy('transaction.createdAt', 'DESC')
				.skip(skip)
				.take(limit);

			// Execute the query
			const transactions = await queryBuilder.getMany();

			// Fetch user names for transactions
			const userIds = [
				...new Set(transactions.map(t => t.createdBy).filter(Boolean))
			];
			let userMap: Record<
				string,
				{ firstName: string; lastName: string }
			> = {};

			if (userIds.length > 0) {
				const users = await this.userRepository.find({
					where: { id: In(userIds) },
					select: ['id', 'firstName', 'lastName']
				});

				userMap = users.reduce(
					(map, user) => {
						map[user.id] = {
							firstName: user.firstName,
							lastName: user.lastName
						};
						return map;
					},
					{} as Record<
						string,
						{ firstName: string; lastName: string }
					>
				);
			}

			// Map transactions to response DTOs
			const transactionDtos: CreditTransactionResponseDto[] =
				transactions.map(transaction => {
					const user = userMap[transaction.createdBy];
					const userName = user
						? `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
							'Staff Member'
						: 'Staff Member';

					return {
						id: transaction.id,
						ownerId: transaction.ownerId,
						amount: transaction.amount,
						transactionType: transaction.transactionType,
						derivedTransactionType:
							transaction.derivedTransactionType ||
							DerivedCreditTransactionType.UNKNOWN,
						description: transaction.description || '',
						invoiceId: transaction.invoiceId,
						invoiceReferenceAlphaId:
							transaction.invoice?.referenceAlphaId,
						paymentDetailId: transaction.paymentDetailId,
						paymentReferenceAlphaId:
							transaction.paymentDetail?.referenceAlphaId,
						paymentNotes:
							transaction.paymentDetail?.paymentNotes || '',
						paymentDetails: transaction.paymentDetail
							? {
									id: transaction.paymentDetail.id,
									amount: transaction.paymentDetail.amount,
									type: transaction.paymentDetail.type,
									paymentType:
										transaction.paymentDetail.paymentType,
									isCreditUsed:
										transaction.paymentDetail.isCreditUsed,
									creditAmountUsed:
										transaction.paymentDetail
											.creditAmountUsed,
									isCreditsAdded:
										transaction.paymentDetail
											.isCreditsAdded,
									creditAmountAdded:
										transaction.paymentDetail
											.creditAmountAdded,
									amountPayable:
										transaction.paymentDetail.amountPayable,
									mainBalance:
										transaction.paymentDetail.mainBalance,
									transactionAmount:
										transaction.paymentDetail
											.transactionAmount,
									transactionpaymentnotes:
										transaction.paymentDetail.paymentNotes,
									previousBalance:
										transaction.paymentDetail
											.previousBalance,
									createdAt:
										transaction.paymentDetail.createdAt,
									createdBy:
										transaction.paymentDetail.createdBy,
									referenceAlphaId:
										transaction.paymentDetail
											.referenceAlphaId || ''
								}
							: undefined,
						clinicId: transaction.clinicId,
						brandId: transaction.brandId,
						createdBy: transaction.createdBy,
						createdByName: userName,
						createdAt: transaction.createdAt,
						updatedAt: transaction.updatedAt,
						metadata: transaction.metadata
					};
				});

			// Fetch unique users who created transactions for this owner
			let uniqueUsers: { id: string; name: string }[] = [];
			try {
				const usersQuery = this.connection.manager
					.createQueryBuilder()
					.select([
						'DISTINCT user.id AS id',
						'user.first_name AS firstName',
						'user.last_name AS lastName'
					])
					.from('users', 'user')
					.innerJoin(
						'credit_transactions',
						'transaction',
						'transaction.created_by = user.id'
					)
					.where('transaction.owner_id = :ownerId', { ownerId });

				const users = await usersQuery.getRawMany();

				// Format user data for frontend
				uniqueUsers = users
					.map(user => ({
						id: user.id,
						name:
							`${user.firstname || ''} ${user.lastname || ''}`.trim() ||
							'Staff Member'
					}))
					.filter(user => user.name !== 'Staff Member');
			} catch (error) {
				this.logger.error(
					'Error fetching unique users for credit transactions',
					{
						error,
						ownerId
					}
				);
				uniqueUsers = []; // Use empty array on error
			}

			// Construct the paginated response
			const response: PaginatedCreditTransactionsResponseDto = {
				ownerDetails: {
					id: owner.id,
					name: `${owner.firstName} ${owner.lastName}`,
					creditBalance: owner.ownerCredits
				},
				transactions: transactionDtos,
				uniqueUsers,
				pagination: {
					total,
					page,
					limit,
					hasMore: skip + transactions.length < total
				}
			};

			this.logger.log(
				'Successfully fetched paginated credit transactions',
				{
					ownerId,
					transactionsCount: transactionDtos.length,
					total
				}
			);

			return response;
		} catch (error) {
			this.logger.error('Error fetching paginated credit transactions', {
				error,
				ownerId,
				filters
			});
			throw error;
		}
	}

	/**
	 * Check for duplicate payments in the last 3 hours
	 */
	async checkForDuplicatePayment({
		invoiceId,
		amount,
		clinicId,
		brandId,
		userId
	}: {
		invoiceId: string;
		amount: number;
		clinicId: string;
		brandId: string;
		userId?: string;
	}): Promise<boolean> {
		// Get timestamp for 3 hours ago
		const threeHoursAgo = new Date();
		threeHoursAgo.setHours(threeHoursAgo.getHours() - 3);

		try {
			// Check for any payment with the same amount for this invoice in the last 3 hours
			const recentDuplicates =
				await this.creditTransactionRepository.find({
					where: {
						invoiceId,
						amount,
						clinicId,
						brandId,
						createdAt: MoreThanOrEqual(threeHoursAgo)
					}
				});

			this.logger.log('Checked for duplicate payments', {
				invoiceId,
				amount,
				userId,
				duplicatesFound: recentDuplicates.length > 0,
				checkTimestamp: new Date()
			});

			return recentDuplicates.length > 0;
		} catch (error) {
			this.logger.error('Error checking for duplicate payments', {
				error,
				invoiceId,
				amount,
				userId
			});
			throw error;
		}
	}
}
