import { <PERSON>, Post, Body, Get, Param, Put } from '@nestjs/common';
import { ChatRoomService } from './chat-room.service';
import { CreateChatRoomDto } from './dto/create-chat-room.dto';
import { ChatRoom } from './chat-room.entity';
import { ApiTags } from '@nestjs/swagger';
import { ChatRoomUser } from './chat-room-users.entity';
import { CreateChatMessageDto } from './dto/create-chat-message.dto';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { UpdateChatUserRoomDto } from './dto/update-chat-user-room.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
@ApiTags('Chat Rooms')
@Controller('chat-rooms')
export class ChatRoomController {
	constructor(private readonly chatRoomService: ChatRoomService) {}

	@Post()
	@TrackMethod('create-chat-room')
	async create(
		@Body() createChatRoomDto: CreateChatRoomDto
	): Promise<ChatRoom> {
		return this.chatRoomService.create(createChatRoomDto);
	}

	@Get('user/:userId')
	@TrackMethod('findAllForUser-chat-room')
	async findAllForUser(
		@Param('userId') userId: string
	): Promise<ChatRoomUser[]> {
		return this.chatRoomService.findAllForUser(userId);
	}

	@Post('message')
	@TrackMethod('sendMessage-chat-room')
	async sendMessage(
		@Body() createChatMessageDto: CreateChatMessageDto
	): Promise<ChatRoomMessage> {
		return this.chatRoomService.sendMessage(createChatMessageDto);
	}

	@Get('/:id')
	@TrackMethod('getChatRoomDetails-chat-room')
	async getChatRoomDetails(
		@Param('id') chatRoomId: string
	): Promise<ChatRoom> {
		return this.chatRoomService.getChatRoomDetails(chatRoomId);
	}

	@Put('/:id')
	@TrackMethod('updateChatRoom-chat-room')
	async updateChatRoom(
		@Param('id') id: string,
		@Body() updateChatRoomDto: UpdateChatUserRoomDto
	) {
		return this.chatRoomService.updateChatRoom(id, updateChatRoomDto);
	}
}
