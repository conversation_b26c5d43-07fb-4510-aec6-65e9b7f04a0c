import { DataSource, Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto, UpdateUserDto, UpdateProfileDto, UpdateWorkingHoursDto } from './dto/user.dto';
import { Role } from '../roles/role.enum';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { RoleService } from '../roles/role.service';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { BrandService } from '../brands/brands.service';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { AvailabilityExceptionEntity } from './entities/availability-exception.entity';
import { CreateExceptionDto, UpdateExceptionDto } from './dto/exception.dto';
import { CalendarWorkingHoursResponseDto } from './dto/calendar-working-hours.dto';
import { AvailabilityService } from '../availability/availability.service';
import { FindClinicUsersAvailabilityResponse } from './dto/availability-response.dto';
interface user {
    userId: string;
    role: Role;
    email: string;
}
export declare class UsersService {
    private usersRepository;
    private clinicUsersRepository;
    private appointmentDoctorsRepository;
    private availabilityExceptionRepository;
    private roleService;
    private readonly logger;
    private readonly mailService;
    private brandService;
    private dataSource;
    private readonly availabilityService;
    constructor(usersRepository: Repository<User>, clinicUsersRepository: Repository<ClinicUser>, appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>, availabilityExceptionRepository: Repository<AvailabilityExceptionEntity>, roleService: RoleService, logger: WinstonLogger, mailService: SESMailService, brandService: BrandService, dataSource: DataSource, availabilityService: AvailabilityService);
    generateUniquePin(): Promise<string>;
    createUser(createUserDto: CreateUserDto, clinicId: string, brandId: string, userObj: user): Promise<User>;
    findOneByPin(pin: string): Promise<User | null>;
    updateStaffProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<User>;
    findByRoleId(roleId: string): Promise<User | null>;
    updateWorkingHours(userId: string, updateWorkingHoursDto: UpdateWorkingHoursDto): Promise<ClinicUser>;
    findByUserId(userId: string): Promise<ClinicUser[]>;
    findOneByEmail(email: string): Promise<User | null>;
    getUserClinics(userId: string): Promise<ClinicEntity[] | undefined>;
    findUsersAcrossClinics(brandId: string, excludeClinicId: string, searchTerm: string, page?: number, limit?: number): Promise<{
        users: User[];
        total: number;
    }>;
    addUserToClinic(userId: string, clinicId: string, brandId: string, isPrimary?: boolean): Promise<ClinicUser>;
    findClinicUsersAvailability(clinicId: string, role?: string, search?: string, _orderBy?: string, // Keep parameter for signature, but mark as unused
    date?: string, startTime?: string, endTime?: string): Promise<FindClinicUsersAvailabilityResponse>;
    getClinicDoctors(clinicId: string): Promise<{
        users: any;
        total: number;
    }>;
    findClinicUsers(clinicId: string, page?: number, limit?: number, role?: string, search?: string, orderBy?: string): Promise<{
        users: any;
        total: number;
    }>;
    updateUserStatus(id: string, isActive: boolean, updatedBy?: string): Promise<User>;
    updateClinicUser(id: string, updateUserDto: UpdateUserDto, updatedBy?: string): Promise<User>;
    findOne(id: string): Promise<Partial<User>>;
    getClinicUserData(id: string): Promise<any | null>;
    findAll(brandId: string): Promise<User[]>;
    remove(id: string): Promise<void>;
    private sendUserCreationEmail;
    private sendPinResetEmail;
    /**
     * Create a new availability exception
     *
     * @param createExceptionDto Data for creating the exception
     * @param userId ID of the user creating the exception
     * @returns The created exception
     */
    createException(createExceptionDto: CreateExceptionDto, userId: string): Promise<AvailabilityExceptionEntity>;
    /**
     * Get all exceptions for a clinic user, optionally including past exceptions
     *
     * @param clinicUserId ID of the clinic user
     * @param includeHistory Whether to include past exceptions
     * @returns List of exceptions
     */
    getExceptions(clinicUserId: string, includeHistory?: boolean): Promise<AvailabilityExceptionEntity[]>;
    /**
     * Get a specific exception by ID
     *
     * @param id Exception ID
     * @returns The exception entity
     */
    getExceptionById(id: string): Promise<AvailabilityExceptionEntity>;
    /**
     * Update an existing exception
     *
     * @param id Exception ID
     * @param updateExceptionDto Data for updating the exception
     * @param userId ID of the user updating the exception
     * @returns The updated exception
     */
    updateException(id: string, updateExceptionDto: UpdateExceptionDto, userId: string): Promise<AvailabilityExceptionEntity>;
    /**
     * Delete an exception
     *
     * @param id Exception ID
     */
    deleteException(id: string): Promise<void>;
    /**
     * Validate that there are no conflicting exceptions for the same clinic user
     *
     * @param clinicUserId The clinic user ID
     * @param startDate Start date of the exception
     * @param endDate End date of the exception
     * @param type Type of exception
     * @param isFullDay Whether the exception is for a full day
     * @param times Time slots for the exception if not full day
     * @param excludeExceptionId Optional exception ID to exclude from conflict check
     */
    private validateExceptionConflicts;
    /**
     * Validate that there are no conflicting appointments for out-of-office exceptions
     *
     * @param clinicUserId The clinic user ID
     * @param startDate Start date of the exception
     * @param endDate End date of the exception
     * @param isFullDay Whether the exception is for a full day
     * @param times Time slots for the exception if not full day
     */
    private validateNoConflictingAppointments;
    /**
     * Validate that there are no appointments scheduled during additional hours
     * before allowing those additional hours to be deleted or modified in a way that would
     * no longer cover existing appointments
     *
     * @param clinicUserId The clinic user ID
     * @param startDate Start date of the exception
     * @param endDate End date of the exception
     * @param isFullDay Whether the exception is for a full day
     * @param times Time slots for the exception if not full day
     * @param newTimes Optional new time slots for comparison when updating
     */
    private validateNoAppointmentsInAdditionalHours;
    /**
     * Get all doctors working hours for a specific date for the calendar view
     * This combines regular working hours with exceptions (both out-of-office and additional hours)
     *
     * @param date The date to get working hours for (YYYY-MM-DD)
     * @param clinicId The clinic ID
     * @returns A list of doctors with their available time slots for the specified date
     */
    getCalendarWorkingHours(date: string, clinicId: string): Promise<CalendarWorkingHoursResponseDto>;
    /**
     * Check if an exception has already ended (is in the past)
     *
     * @param exception The exception entity or exception data
     * @returns True if the exception has already ended
     */
    private isExceptionEnded;
}
export {};
