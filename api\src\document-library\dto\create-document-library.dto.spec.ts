import { validate } from 'class-validator';
import { CreateDocumentLibraryDto } from './create-document-library.dto';

describe('CreateDocumentLibraryDto', () => {
  let dto: CreateDocumentLibraryDto;

  beforeEach(() => {
    dto = new CreateDocumentLibraryDto();
  });

  it('should validate a valid DTO', async () => {
    dto.clinicId = '550e8400-e29b-41d4-a716-446655440000';
    dto.documentName = 'Consent Form';
    dto.signatureRequired = true;
    dto.category = 'Forms';
    dto.documentType = 'create';
    dto.fileKey = undefined;
    dto.documentBody = { title: 'Title', bodyText: 'Body text' };
    dto.createdBy = '550e8400-e29b-41d4-a716-446655440000';
    dto.updatedBy = '550e8400-e29b-41d4-a716-446655440000';

    const errors = await validate(dto);
    expect(errors.length).toBe(0); 
  });

  it('should fail if documentName is empty', async () => {
    dto.documentName = '';
    dto.signatureRequired = true;
    dto.documentType = 'upload';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('documentName');
  });

  it('should fail if documentType is invalid', async () => {
    dto.documentName = 'Test Document';
    dto.signatureRequired = true;
    dto.documentType = 'invalid' as any; 

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('documentType');
  });

  it('should fail if signatureRequired is missing', async () => {
    dto.documentName = 'Test Document';
    dto.documentType = 'upload';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('signatureRequired');
  });

  it('should allow optional fields to be missing', async () => {
    dto.documentName = 'Test Document';
    dto.signatureRequired = false;
    dto.documentType = 'create';

    const errors = await validate(dto);
    expect(errors.length).toBe(0); 
  });
});
