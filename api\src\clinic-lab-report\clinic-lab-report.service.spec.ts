import { Test, TestingModule } from '@nestjs/testing';
import { ClinicLabReportService } from './clinic-lab-report.service';
import { ClinicLabReport } from './entities/clinic-lab-report.entity';
import { LabReport } from './entities/lab-report.entity';
import { DeleteResult, ILike, Repository, SelectQueryBuilder, UpdateResult } from 'typeorm';
import { S3Service } from '../utils/aws/s3/s3.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { CreateLabReportDto } from './dto/lab-report.dto';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { User } from '../users/entities/user.entity';
import { EnumAppointmentType } from '../appointments/enums/enum-appointment-type';
import { EnumAppointmentTriage } from '../appointments/enums/enum-appointment-triage';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { ClinicRoomEntity } from '../clinics/entities/clinic-room.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { Patient } from '../patients/entities/patient.entity';
import { UpdateStatusDto } from './dto/update-clinic-lab-report-status.dto';

describe('ClinicLabReportService', () => {
	let service: ClinicLabReportService;
	let clinicLabReportRepository: Repository<ClinicLabReport>;
	let labReportRepository: Repository<LabReport>;
	let appointmentRepository: Repository<AppointmentEntity>;
	let s3Service: S3Service;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let logger: WinstonLogger;
	const mockQueryBuilder = () => {
		return {
		  leftJoinAndSelect: jest.fn().mockReturnThis(),
		  select: jest.fn().mockReturnThis(),
		  where: jest.fn().mockReturnThis(),
		  andWhere: jest.fn().mockReturnThis(),
		  skip: jest.fn().mockReturnThis(),
		  take: jest.fn().mockReturnThis(),
		  orderBy: jest.fn().mockReturnThis(),
		  getManyAndCount: jest.fn(),
		} as unknown as SelectQueryBuilder<LabReport>;
	  };

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicLabReportService,
				{
					provide: getRepositoryToken(ClinicLabReport),
					useClass: Repository,
					useValue: {
						update: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(LabReport),
					useClass: Repository
				},
				{
					provide: getRepositoryToken(AppointmentEntity),
					useClass: Repository
				},
				{
					provide: S3Service,
					useValue: {
						deleteFile: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicLabReportService>(ClinicLabReportService);
		clinicLabReportRepository = module.get<Repository<ClinicLabReport>>(
			getRepositoryToken(ClinicLabReport)
		);
		labReportRepository = module.get<Repository<LabReport>>(
			getRepositoryToken(LabReport)
		);
		appointmentRepository = module.get<Repository<AppointmentEntity>>(
			getRepositoryToken(AppointmentEntity)
		);
		s3Service = module.get<S3Service>(S3Service);
		logger = module.get<WinstonLogger>(WinstonLogger);
		jest.spyOn(labReportRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder());
	});

	const DummyData = {
		patientId: '',
		isDataImported: false,
		isBalanceUpdated: false
	}

	describe('getLabReports', () => {
		it('should return clinic lab reports based on clinicId', async () => {
			const clinicLabReports: ClinicLabReport[] = [
				{
					id: '1',
					name: 'Test Report',
					clinicId: 'clinicId',
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: '',
					createdByUser: new User(),
					updatedBy: '',
					updatedByUser: new User(),
					uniqueId: '',
					chargeablePrice: 0,
					tax: 0,
					associatedLab: 0,
					description: '',
					brandId: ''
				}
			];
			jest.spyOn(clinicLabReportRepository, 'find').mockResolvedValue(
				clinicLabReports
			);

			const result = await service.getLabReports('clinicId');
			expect(result).toEqual(clinicLabReports);
			expect(clinicLabReportRepository.find).toHaveBeenCalled();
		});

		it('should return filtered clinic lab reports when a search term is provided', async () => {
			const clinicLabReports: ClinicLabReport[] = [
				{
					id: '1',
					name: 'Blood Test',
					clinicId: 'clinicId',
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: '',
					createdByUser: new User(),
					updatedBy: '',
					updatedByUser: new User(),
					uniqueId: '',
					chargeablePrice: 0,
					tax: 0,
					associatedLab: 0,
					description: '',
					brandId: ''
				}
			];
			jest.spyOn(clinicLabReportRepository, 'find').mockResolvedValue(
				clinicLabReports
			);

			const result = await service.getLabReports('clinicId', 'Blood');
			expect(result).toEqual(clinicLabReports);
			expect(clinicLabReportRepository.find).toHaveBeenCalled();
		});
	});

	describe('createOrUpdateLabReport', () => {
		it('should create a new lab report', async () => {
			const createLabReportDto: CreateLabReportDto = {
				appointmentId: 'appointment-id',
				clinicLabReportId: 'report-id',
				files: [
					{
						fileName: 'test.pdf',
						fileSize: 1024 * 1024,
						fileKey: 's3Key'
					}
				],
				clinicId: 'clinic-id',
				status: 'PENDING',
				integrationDetails: {}
			};

			const mockAppointment: AppointmentEntity = {
				id: 'appointment-id',
				clinicId: 'clinic-id',
				patientId: 'patient-id',
				roomId: 'room-id',
				reason: 'reason',
				type: EnumAppointmentType.Consultation,
				triage: EnumAppointmentTriage.Low,
				date: new Date(),
				startTime: new Date(),
				endTime: new Date(),
				deletedAt: undefined,
				weight: 70,
				notes: {},
				preVisitQuestions: {},
				isBlocked: false,
				status: EnumAppointmentStatus.Scheduled,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'createdBy',
				updatedBy: 'updatedBy',
				initialNotes: 'initialNotes',
				patient: {
					id: 'patient-id',
					patientName: 'John Doe',
					breed: 'Breed',
					patientOwners: [],
					patientAlerts: [],
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: 'createdBy',
					updatedBy: 'updatedBy',
					clinicId: '',
					updatedByUser: new User(),
					balance: 100,
					dummyData: DummyData,
					brandId: 'b_1'
				},
				appointmentDoctors: [],
				room: new ClinicRoomEntity(),
				appointmentDetails: new AppointmentDetailsEntity(),
				labReports: [],
				clinic: new ClinicEntity(),
				cart: new CartEntity(),
				checkinTime: new Date(),
				checkoutTime: new Date(),
				receivingCareTime: new Date(),
				brandId: 'b_1'
			};
			const mockClinicLabReport: ClinicLabReport = {
				id: 'report-id',
				name: 'Test Report',
				clinicId: 'clinic-id',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'user-id',
				createdByUser: new User(),
				updatedBy: 'user-id',
				updatedByUser: new User(),
				uniqueId: 'unique-id',
				chargeablePrice: 100,
				tax: 10,
				associatedLab: 1,
				description: 'Test description',
				brandId: 'brand-id'
			};
			const mockLabReport: LabReport = {
				id: 'lab-report-id',
				files: [],
				appointmentId: 'appointment-id',
				appointment: mockAppointment,
				clinicLabReportId: 'report-id',
				clinicLabReport: mockClinicLabReport,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'user-id',
				createdByUser: new User(),
				updatedBy: 'user-id',
				updatedByUser: new User(),
				status: 'PENDING',
				clinicId: '',
				clinic: new ClinicEntity(),
				patient: new Patient(),
				patientId: 'p_1',
				brandId: 'b_1',
				diagnosticNotes: [{id: 'u_1', type: 'upload', data: {notes: "okay", title:" title 1"}}]
			};

			jest.spyOn(appointmentRepository, 'findOne').mockResolvedValue(
				mockAppointment
			);
			jest.spyOn(clinicLabReportRepository, 'findOne').mockResolvedValue(
				mockClinicLabReport
			);
			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(null); // Simulate no existing lab report

			jest.spyOn(labReportRepository, 'create').mockReturnValue(
				mockLabReport
			);
			jest.spyOn(labReportRepository, 'save').mockResolvedValue(
				mockLabReport
			);

			const result =
				await service.createOrUpdateLabReport(createLabReportDto, 'b_1');
			expect(result).toEqual(mockLabReport);
			expect(appointmentRepository.findOne).toHaveBeenCalledWith({
				where: { id: 'appointment-id' }
			});
			expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({
				where: { id: 'report-id' }
			});
			expect(labReportRepository.create).toHaveBeenCalled();
			expect(labReportRepository.save).toHaveBeenCalledWith(
				mockLabReport
			);
		});

		it('should throw NotFoundException if appointment is not found', async () => {
			jest.spyOn(appointmentRepository, 'findOne').mockResolvedValue(
				null
			);

			const createLabReportDto: CreateLabReportDto = {
				appointmentId: 'invalid-id',
				clinicLabReportId: 'report-id',
				files: [
					{
						fileName: 'test.pdf',
						fileSize: 1024 * 1024,
						fileKey: 's3Key'
					}
				],
				clinicId: 'clinic-id',
				status: 'PENDING',
				integrationDetails: {}
			};

			await expect(
				service.createOrUpdateLabReport(createLabReportDto, 'b_1')
			).rejects.toThrow(
				new NotFoundException(
					`Appointment with ID "invalid-id" not found`
				)
			);
		});

		it('should throw bad request if fileSize is undefiend', async() => {
			const createLabReportDto: CreateLabReportDto = {
				appointmentId: 'invalid-id',
				clinicLabReportId: 'report-id',
				files: [
					{
						fileName: 'test.pdf',
						fileSize: undefined,
						fileKey: 's3Key'
					}
				],
				clinicId: 'clinic-id',
				status: 'PENDING',
				integrationDetails: {}
			};

			await expect(
				service.createOrUpdateLabReport(createLabReportDto, 'b_!')
			).rejects.toThrow(
				new BadRequestException(
					`File size is missing for "${createLabReportDto.files[0].fileName}".`
				)
			);
		})

		it.skip('should throw bad request if fileSize is greater than  5 MB', async() => {
			const createLabReportDto: CreateLabReportDto = {
				appointmentId: 'invalid-id',
				clinicLabReportId: 'report-id',
				files: [
					{
						fileName: 'test.pdf',
						fileSize: 5 * 1024 * 1024 + 10,
						fileKey: 's3Key'
					}
				],
				clinicId: 'clinic-id',
				status: 'PENDING',
				integrationDetails: {}
			};

			await expect(
				service.createOrUpdateLabReport(createLabReportDto, 'b_1')
			).rejects.toThrow(
				new BadRequestException(
					`File "${createLabReportDto.files[0].fileName}" exceeds the 5MB size limit.`
				)
			);
		})

		it('should throw NotFoundException if clinicLabReport is not found', async () => {
			jest.spyOn(clinicLabReportRepository, 'findOne').mockRejectedValue(
				null
			);

			const createLabReportDto: CreateLabReportDto = {
				appointmentId: 'invalid-id',
				clinicLabReportId: 'report-id',
				files: [
					{
						fileName: 'test.pdf',
						fileSize: 1024 * 1024,
						fileKey: 's3Key'
					}
				],
				clinicId: 'clinic-id',
				status: 'PENDING',
				integrationDetails: {}
			};

			await expect(
				service.createOrUpdateLabReport(createLabReportDto, 'b_1')
			).rejects.toThrow(
				new NotFoundException(
					`Cannot read properties of undefined (reading 'findOne')`
				)
			);
		});
	});

	describe('deleteFile', () => {
		it('should delete a file from a lab report', async () => {
			const labReport: LabReport = {
				id: 'report-id',
				files: [{ id: 'file-id', fileKey: 's3Key' }],
				appointmentId: 'appointment-id',
				appointment: new AppointmentEntity(),
				clinicLabReportId: 'clinic-lab-report-id',
				clinicLabReport: new ClinicLabReport(),
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'user-id',
				createdByUser: new User(),
				updatedBy: 'user-id',
				updatedByUser: new User(),
				status: 'PENDING',
				clinicId: 'clinic-id',
				clinic: new ClinicEntity(),
				patient: new Patient(),
				patientId: 'p_1',
				brandId: 'b_1',
				diagnosticNotes: [{id: 'u_1', type: 'upload', data: {notes: "okay", title:" title 1"}}]
			};
			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(
				labReport
			);

			const updatedLabReport = { ...labReport, files: [] };
			jest.spyOn(labReportRepository, 'save').mockResolvedValue(
				updatedLabReport
			);
			jest.spyOn(s3Service, 'deleteFile').mockResolvedValue(
				Promise.resolve()
			);

			const result = await service.deleteFile('report-id', 'file-id');
			expect(result).toEqual(updatedLabReport);
			expect(s3Service.deleteFile).toHaveBeenCalledWith('s3Key');
			expect(labReportRepository.save).toHaveBeenCalledWith(
				updatedLabReport
			);
		});

		it('should throw NotFoundException if the file is not found', async () => {
			const labReport: LabReport = {
				id: 'report-id',
				files: [{ id: 'file-id-2', fileKey: 's3Key' }],
				appointmentId: 'appointment-id',
				appointment: new AppointmentEntity(),
				clinicLabReportId: 'clinic-lab-report-id',
				clinicLabReport: new ClinicLabReport(),
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'user-id',
				createdByUser: new User(),
				updatedBy: 'user-id',
				updatedByUser: new User(),
				status: 'PENDING',
				clinicId: 'clinic-id',
				clinic: new ClinicEntity(),
				patient: new Patient(),
				patientId: 'p_1',
				brandId: 'b_1',
				diagnosticNotes: [{id: 'u_1', type: 'upload', data: {notes: "okay", title:" title 1"}}]

			};
			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(
				null
			);

			await expect(
				service.deleteFile('report-id', 'file-id')
			).rejects.toThrow(
				new NotFoundException(
					'Lab report with ID \"report-id\" not found'
				)
			);
		});
	});

	describe('deleteLabReport', () => {
		it('should delete a lab report and associated files', async () => {
			const labReport: LabReport = {
				id: 'report-id',
				files: [{ fileName: 'file1.pdf', fileKey: 's3Key' }],
				appointmentId: 'appointment-id',
				appointment: new AppointmentEntity(),
				clinicLabReportId: 'clinic-lab-report-id',
				clinicLabReport: new ClinicLabReport(),
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'user-id',
				createdByUser: new User(),
				updatedBy: 'user-id',
				updatedByUser: new User(),
				status: 'PENDING',
				clinicId: 'clinic-id',
				clinic: new ClinicEntity(),
				patient: new Patient(),
				patientId: 'p_1',
				brandId: 'b_1',
				diagnosticNotes: [{id: 'u_1', type: 'upload', data: {notes: "okay", title:" title 1"}}]

			};
			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(
				labReport
			);
			jest.spyOn(labReportRepository, 'remove').mockResolvedValue(
				labReport
			);
			jest.spyOn(s3Service, 'deleteFile').mockResolvedValue(undefined);

			await service.deleteLabReport('report-id',labReport.appointmentId);
			expect(s3Service.deleteFile).toHaveBeenCalledWith(
				'/labReports/appointment-id/report-id/file1.pdf'
			);
			expect(labReportRepository.remove).toHaveBeenCalledWith(labReport);
		});

		it('should throw NotFoundException if the lab report is not found', async () => {
			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(null);

			await expect(
				service.deleteLabReport('invalid-report-id', 'mockAppointmentId')
			).rejects.toThrow(
				new NotFoundException(
					'Lab report with ID "invalid-report-id" not found'
				)
			);
		});
	});
	describe('get lab_reports for patient ', () => {
		it('should be defined', () => {
			expect(service.getLabReportForPatient).toBeDefined();
		})

		it('should return all the labReports', async () => {
			const mockPatientId: string = 'p_1';

			const mockLabReports = [
				{
					id: 'lab-report-1',
					appointmentId: 'appointment-1',
					patientId: 'p_1',
					clinicLabReportId: 'clinic-lab-report-1',
					clinicLabReport: { name: 'Blood Test' },
					files: [{ id: 'file-1', name: 'test.pdf' }],
					createdAt: new Date(),
					appointment: {
						patient: {
							id: 'patient-1',
							patientName: 'Max',
							breed: 'Labrador',
							patientOwners: [
								{
									owner: {
										firstName: 'John',
										lastName: 'Doe',
										phoneNumber: '**********'
									}
								}
							]
						},
						appointmentDoctors: [
							{
								clinicUser: {
									user: {
										firstName: 'Dr',
										lastName: 'Smith',
										email: "<EMAIL>",
										id: 'doctor-1'
									}

								}
							}
						]
					}
				}
			] as unknown as LabReport[];
			jest.spyOn(labReportRepository, 'find').mockResolvedValue(mockLabReports);

			const response = await service.getLabReportForPatient(mockPatientId);

			expect(labReportRepository.find).toHaveBeenCalled()
			// expect(response).toEqual(mockLabReports) --need to be fixed later according to new structure
		})
	})
	describe('getLabReportsForClinic', () => {
		it('should return lab reports for a clinic', async () => {
			// Arrange
			const mockLabReports = [
				{
					id: 'report-id-1',
					appointmentId: 'appointment-id-1',
					clinicId: 'clinic-id',
					createdAt: new Date(),
					updatedAt: new Date(),
					status: 'PENDING',
					files: [],
					patientId: 'patient-id',
					clinicLabReportId: 'clinic-report-id',
					appointment: {
						patient: {
							id: 'patient-id',
							patientName: 'Max',
							breed: 'Labrador',
						},
						appointmentDoctors: [],
					},
				},
			] as unknown as LabReport[]; 
		
			const total = mockLabReports.length;
		
			const queryBuilderMock = {
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				skip: jest.fn().mockReturnThis(),
				take: jest.fn().mockReturnThis(),
				orderBy: jest.fn().mockReturnThis(),
				getManyAndCount: jest.fn().mockResolvedValue([mockLabReports, mockLabReports.length]),
			};
	
			jest.spyOn(labReportRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as unknown as SelectQueryBuilder<LabReport>);
		
			// Act
			const result = await service.getLabReportsForClinic('clinic-id', 1, 10);
		
			// Assert
			expect(result).toEqual({ data: mockLabReports, total });
			expect(labReportRepository.createQueryBuilder).toHaveBeenCalledWith('labReport');
		});
	
		  it('should filter lab reports based on search term', async () => {
			const mockLabReports = [
				{
					id: 'report-id-1',
					appointmentId: 'appointment-id-1',
					clinicId: 'clinic-id',
					createdAt: new Date(),
					updatedAt: new Date(),
					status: 'PENDING',
					files: [],
					appointment: {
						patient: {
							id: 'patient-id',
							patientName: 'Max',
							breed: 'Labrador',
						},
						appointmentDoctors: [],
					},
					patientId: 'patient-id', 
					clinicLabReportId: 'clinic-report-id', 
				},
			] as unknown as LabReport[];
	
			const queryBuilderMock = {
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				skip: jest.fn().mockReturnThis(),
				take: jest.fn().mockReturnThis(),
				orderBy: jest.fn().mockReturnThis(),
				getManyAndCount: jest.fn().mockResolvedValue([mockLabReports, mockLabReports.length]),
			};
	
			jest.spyOn(labReportRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as unknown as SelectQueryBuilder<LabReport>);
	
			const result = await service.getLabReportsForClinic('clinic-id', 1, 10, undefined, undefined, 'Max');
	
			expect(result.data).toEqual(mockLabReports);
			expect(result.total).toEqual(mockLabReports.length);
			expect(queryBuilderMock.where).toHaveBeenCalled();
		});
	
		it('should filter lab reports by status "COMPLETED"', async () => {
			const mockLabReports = [
				{
					id: 'report-id-1',
					appointmentId: 'appointment-id-1',
					clinicId: 'clinic-id',
					createdAt: new Date(),
					updatedAt: new Date(),
					status: 'PENDING',
					files: [],
					appointment: {
						patient: {
							id: 'patient-id',
							patientName: 'Max',
							breed: 'Labrador',
						},
						appointmentDoctors: [],
					},
					patientId: 'patient-id', 
					clinicLabReportId: 'clinic-report-id', 
				},
			] as unknown as LabReport[];
	
			const queryBuilderMock = {
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				skip: jest.fn().mockReturnThis(),
				take: jest.fn().mockReturnThis(),
				orderBy: jest.fn().mockReturnThis(),
				getManyAndCount: jest.fn().mockResolvedValue([mockLabReports, mockLabReports.length]),
			};
	
			jest.spyOn(labReportRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as unknown as SelectQueryBuilder<LabReport>);
	
			const result = await service.getLabReportsForClinic('clinic-id', 1, 10, undefined, undefined, undefined, 'COMPLETED');
	
			expect(result.data).toEqual(mockLabReports);
			expect(result.total).toEqual(mockLabReports.length);
			expect(queryBuilderMock.andWhere).toHaveBeenCalled();
		});
	
		it('should filter lab reports by date range', async () => {
			const mockLabReports = [
				{
					id: 'report-id-1',
					appointmentId: 'appointment-id-1',
					clinicId: 'clinic-id',
					createdAt: new Date(),
					updatedAt: new Date(),
					status: 'PENDING',
					files: [],
					appointment: {
						patient: {
							id: 'patient-id',
							patientName: 'Max',
							breed: 'Labrador',
						},
						appointmentDoctors: [],
					},
					patientId: 'patient-id', 
					clinicLabReportId: 'clinic-report-id', 
				},
			] as unknown as LabReport[];
	
			const queryBuilderMock = {
				leftJoinAndSelect: jest.fn().mockReturnThis(),
				select: jest.fn().mockReturnThis(),
				where: jest.fn().mockReturnThis(),
				andWhere: jest.fn().mockReturnThis(),
				skip: jest.fn().mockReturnThis(),
				take: jest.fn().mockReturnThis(),
				orderBy: jest.fn().mockReturnThis(),
				getManyAndCount: jest.fn().mockResolvedValue([mockLabReports, mockLabReports.length]),
			};
	
			jest.spyOn(labReportRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as unknown as SelectQueryBuilder<LabReport>);
	
			const startDate = new Date('2024-01-01');
			const endDate = new Date('2024-12-31');
	
			const result = await service.getLabReportsForClinic('clinic-id', 1, 10, startDate, endDate);
	
			expect(result.data).toEqual(mockLabReports);
			expect(result.total).toEqual(mockLabReports.length);
		});
	});

	describe('updateLabReportStatus', () => {
		it('should update the status of a lab report', async () => {
			const updateStatusDto: UpdateStatusDto = {
				id: 'report-id-1',
				status: 'COMPLETED',
			};

			const mockLabReport: LabReport = {
				id: 'report-id-1',
				status: 'PENDING',
				updatedAt: new Date(),
			} as LabReport;

			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(mockLabReport);
			jest.spyOn(labReportRepository, 'save').mockResolvedValue(mockLabReport);

			const result = await service.updateLabReportStatus(updateStatusDto);

			expect(result.status).toBe('COMPLETED');
			expect(result.updatedAt).toBeInstanceOf(Date);
			expect(labReportRepository.findOne).toHaveBeenCalledWith({ where: { id: updateStatusDto.id } });
			expect(labReportRepository.save).toHaveBeenCalledWith(mockLabReport);
		});

		it('should throw NotFoundException if the lab report does not exist', async () => {
			const updateStatusDto: UpdateStatusDto = {
				id: 'non-existent-id',
				status: 'COMPLETED',
			};

			jest.spyOn(labReportRepository, 'findOne').mockResolvedValue(null);

			await expect(service.updateLabReportStatus(updateStatusDto)).rejects.toThrow(NotFoundException);
			await expect(service.updateLabReportStatus(updateStatusDto)).rejects.toThrow('No lab reports found with the given ID');
			expect(labReportRepository.findOne).toHaveBeenCalledWith({ where: { id: updateStatusDto.id } });
		});
	})

	describe('bulkInsert', () => {
		// it('should successfully insert multiple clinic lab reports', async () => {
		// 	const clinicLabReport = [{ /* report 1 data */ }, { /* report 2 data */ }];
		// 	const savedClinicLabReport = { id: '1' ,clinicId: 'uuid',name: 'sa',createdAt: new Date(), } as ClinicLabReport;

		// 	jest.spyOn(clinicLabReportRepository, 'save').mockResolvedValue(savedClinicLabReport);
		// 	const logSpy = jest.spyOn(logger, 'log');

		// 	const result = await service.bulkInsert(clinicLabReport);

		// 	expect(result).toBe('Bulk insert of diagnostics completed successfully');
		// 	expect(clinicLabReportRepository.save).toHaveBeenCalledWith(clinicLabReport);
		// });

		// it('should throw an error if bulk insert fails', async () => {
		// 	const clinicLabReport = [{ }];
		// 	const errorMessage = 'Database error';
		// 	jest.spyOn(clinicLabReportRepository, 'save').mockRejectedValue(new Error(errorMessage));
		// 	const errorSpy = jest.spyOn(logger, 'error');

		// 	await expect(service.bulkInsert(clinicLabReport)).rejects.toThrow('Failed to insert diagnostics');
		// 	expect(clinicLabReportRepository.save).toHaveBeenCalledWith(clinicLabReport);
		// 	expect(errorSpy).toHaveBeenCalledWith('Error during bulk insert of diagnostics', {
		// 		error: expect.any(Error),
		// 	});
		// });
	})
	describe('findOneEntry', () => {
		// it('should return a clinic lab report entry for valid criteria', async () => {
		// 	const criteria = { uniqueId: 'unique-id-1', clinicId: 'clinic-id-1' };
		// 	const mockEntry = { id: 'entry-id', uniqueId: 'unique-id-1', clinicId: 'clinic-id-1' } as ClinicLabReport;
			
		// 	jest.spyOn(clinicLabReportRepository, 'findOne').mockResolvedValue(mockEntry);

		// 	const result = await service.findOneEntry(criteria);

		// 	expect(result).toEqual(mockEntry);
		// 	expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({ where: criteria });
		// });

		// it('should return null for non-existent entry', async () => {
		// 	const criteria = { uniqueId: 'unique-id-2', clinicId: 'clinic-id-2' };
			
		// 	jest.spyOn(clinicLabReportRepository, 'findOne').mockResolvedValue(null);

		// 	const result = await service.findOneEntry(criteria);

		// 	expect(result).toBeNull();
		// 	expect(clinicLabReportRepository.findOne).toHaveBeenCalledWith({ where: criteria });
		// });
	});
	describe('deleteItem', () => {
		it('should delete an item successfully', async () => {
			const itemId = 'item-id-1';
			const mockDeleteResult = { affected: 1 } as UpdateResult; // Simulate that one item was deleted
			jest.spyOn(clinicLabReportRepository, 'update').mockResolvedValue(mockDeleteResult);

			const result = await service.deleteItem(itemId);

			expect(clinicLabReportRepository.update).toHaveBeenCalled();
		});

		it.skip('should handle deletion of a non-existent item', async () => {
			const itemId = 'item-id-2';
			const mockDeleteResult = { affected: 0 } as DeleteResult; // Simulate that no items were deleted
			jest.spyOn(clinicLabReportRepository, 'delete').mockResolvedValue(mockDeleteResult);

			const result = await service.deleteItem(itemId);

			expect(result).toEqual(mockDeleteResult);
			expect(clinicLabReportRepository.delete).toHaveBeenCalledWith(itemId);
		});
	});

	describe('deleteLabReportsByAppointmentId', () => {
		it('should be defined', () => {
			expect(service.deleteLabReportsByAppointmentId).toBeDefined();
		})

		it('should delete all the labReports which asscociated with appointmentId', async() => {
			const mockAppointmentId = 'appointment-id';

			const mockLabReports: LabReport[] = [{
				id: 'report-id',
				files: [{ id: 'file-id', fileKey: 's3Key' }],
				appointmentId: 'appointment-id',
				appointment: new AppointmentEntity(),
				clinicLabReportId: 'clinic-lab-report-id',
				clinicLabReport: new ClinicLabReport(),
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: 'user-id',
				createdByUser: new User(),
				updatedBy: 'user-id',
				updatedByUser: new User(),
				status: 'PENDING',
				clinicId: 'clinic-id',
				clinic: new ClinicEntity(),
				patient: new Patient(),
				patientId: 'p_1',
				brandId: 'b_1',
				diagnosticNotes: [{id: 'u_1', type: 'upload', data: {notes: "okay", title:" title 1"}}]

			}];
			const mockDeleteResult = { affected: 1 } as DeleteResult; 
			jest.spyOn(labReportRepository, 'find').mockResolvedValue(
				mockLabReports
			);
			jest.spyOn(s3Service, 'deleteFile').mockResolvedValue(
				Promise.resolve()
			);
			jest.spyOn(labReportRepository, 'delete').mockResolvedValue(
				mockDeleteResult
			);

			const result = await service.deleteLabReportsByAppointmentId(mockAppointmentId);

			expect(result).toEqual(mockDeleteResult);
			expect(labReportRepository.find).toHaveBeenCalled();
			expect(labReportRepository.delete).toHaveBeenCalled();
		})
	})
	
});
