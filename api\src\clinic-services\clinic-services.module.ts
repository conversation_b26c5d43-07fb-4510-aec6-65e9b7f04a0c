import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicServiceEntity } from './entities/clinic-service.entity';
import { ClinicServicesController } from './clinic-services.controller';
import { ClinicServicesService } from './clinic-services.service';
import { RoleModule } from '../roles/role.module';

@Module({
  imports: [TypeOrmModule.forFeature([ClinicServiceEntity]),RoleModule],
  controllers: [ClinicServicesController],
  providers: [ClinicServicesService],
  exports:[ClinicServicesService]
})
export class  ClinicServicesModule {}
