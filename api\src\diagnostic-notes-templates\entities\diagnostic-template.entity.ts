import {
    <PERSON><PERSON><PERSON>,
    Column,
    CreateDateC<PERSON>umn,
    UpdateDateColumn,
    PrimaryGeneratedColumn,
    ManyToOne,
    JoinColumn,
    Index
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';

export enum TemplateType {
    NOTES = 'notes',
    TABLE = 'table'
}

interface AssignedDiagnostic {
    id: string;
    name: string;
}

@Entity('diagnostic_templates')
@Index(['clinicId', 'templateName'], { unique: true })
export class DiagnosticTemplate {
    @PrimaryGeneratedColumn('uuid')
    id!: string;

    @Column({ length: 100,name:'template_name' })
    templateName!: string;

    @Column({ name: 'clinic_id', type: 'uuid', nullable: true })
	clinicId!: string;

	@ManyToOne(() => ClinicEntity, clinic => clinic.id, { onDelete: 'CASCADE' })
	@JoinColumn({ name: 'clinic_id' })
	clinic?: ClinicEntity;

    @Column({name:'assigned_diagnostics',type:'jsonb'})
    assignedDiagnostics!: AssignedDiagnostic[];

    @Column({
        type: 'enum',
        name:'template_type',
        enum: TemplateType,
        default: TemplateType.NOTES
    })
    templateType!: TemplateType;

    @Column('jsonb', { nullable: true,name:'table_structure' })
    tableStructure?: {
        columns: {
            name: string;
            type: 'text' | 'number' | 'range';
            options?: string[];
        }[];
    } | null;

    @Column({ type: 'text', nullable: true })
    notes?: string;

    @Column({ default: true,name:'is_active' })
    isActive?: boolean;

    @Column({ nullable: true, name: 'created_by' })
	createdBy!: string;

	@Column({ nullable: true, name: 'updated_by' })
	updatedBy!: string;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'created_by' })
    creator?: User;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'updated_by' })
    updater?: User;

    @CreateDateColumn({ name: 'created_at' })
    createdAt!: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt!: Date;
}