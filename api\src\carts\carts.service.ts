import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { CartEntity } from './entites/cart.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CartItemEntity } from '../cart-items/entities/cart-item.entity';
import { AppointmentsService } from '../appointments/appointments.service';
import { EnumAppointmentType } from '../appointments/enums/enum-appointment-type';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { Logger } from '@nestjs/common';

@Injectable()
export class CartsService {
	private readonly logger = new Logger(CartsService.name);

	constructor(
		@InjectRepository(CartEntity)
		private cartRepository: Repository<CartEntity>,
		@InjectRepository(CartItemEntity)
		private cartItemRepository: Repository<CartItemEntity>,
		@Inject(forwardRef(() => AppointmentsService))
		private readonly appointmentsService: AppointmentsService
	) {}

	async findCartById(cartId: string) {
		return this.cartRepository.findOne({
			where: { id: cartId }
		});
	}

	async createCart(
		appointmentId?: string,
		cartId?: string,
		impromptuData?: {
			impromptu: boolean;
			patientId: string;
			clinicId: string;
			brandId: string;
			userId: string;
		}
	) {
		// First check if cartId exists
		if (cartId) {
			const existingCart = await this.findCartById(cartId);
			if (existingCart) {
				return existingCart;
			}
		}

		// Then check appointmentId
		if (appointmentId) {
			const existingCart = await this.cartRepository.findOne({
				where: { appointmentId }
			});
			if (existingCart) {
				return existingCart;
			}
		}

		// If no appointmentId but impromptu flag is true, create an impromptu appointment
		if (
			!appointmentId &&
			impromptuData?.impromptu &&
			impromptuData.patientId
		) {
			this.logger.log('Creating impromptu appointment for cart', {
				patientId: impromptuData.patientId,
				impromptu: impromptuData.impromptu,
				clinicId: impromptuData.clinicId,
				brandId: impromptuData.brandId,
				userId: impromptuData.userId,
				fullData: impromptuData
			});

			const currentTime = new Date();

			const appointmentData = {
				clinicId: impromptuData.clinicId,
				patientId: impromptuData.patientId,
				brandId: impromptuData.brandId,
				date: currentTime,
				startTime: currentTime,
				endTime: currentTime,
				reason: 'Impromptu',
				type: EnumAppointmentType.Impromptu,
				status: EnumAppointmentStatus.Completed,
				createdBy: impromptuData.userId
			};

			const newAppointment =
				await this.appointmentsService.createImpromptuAppointment(
					appointmentData
				);

			this.logger.log('Created impromptu appointment for cart', {
				appointmentId: newAppointment.id,
				type: appointmentData.type,
				status: appointmentData.status
			});

			appointmentId = newAppointment.id;
		}

		// Create new cart if neither found
		return this.cartRepository.save({
			appointmentId
		});
	}
	async deleteCart(
		cartId: string,
		softDelete: boolean = false,
		entityManager?: any,
		deleteAppointment: boolean = true
	) {
		// First, fetch the cart to check for associated appointment
		const cart = await this.findCartById(cartId);

		// If the cart has an associated appointment, check if it's an impromptu appointment
		if (cart?.appointmentId) {
			// Fetch the appointment entity
			const appointment = await this.appointmentsService[
				'appointmentRepository'
			].findOne({
				where: { id: cart.appointmentId }
			});

			// If the appointment exists and is of type Impromptu, delete its details and the appointment
			if (
				appointment &&
				appointment.type === EnumAppointmentType.Impromptu &&
				deleteAppointment
			) {
				// Delete appointment details first
				await this.appointmentsService[
					'appointmentDetailsRepository'
				].delete({ appointmentId: appointment.id });
				// Delete the appointment itself
				await this.appointmentsService['appointmentRepository'].delete(
					appointment.id
				);
			}
		}

		// Use entity manager if provided, otherwise use default repository
		const manager = entityManager || this.cartRepository.manager;

		// Delete cart items (always hard delete for cart items)
		await this.cartItemRepository.delete({ cartId });

		// Delete the cart itself (soft or hard delete based on parameter)
		if (softDelete) {
			await manager.softDelete(CartEntity, cartId);
		} else {
			await this.cartRepository.delete(cartId);
		}

		return {
			success: true,
			message: `Cart, associated items, and impromptu appointment (if any) ${softDelete ? 'soft deleted' : 'deleted'} successfully`
		};
	}
}
