import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class CreateClinicAlertDto {
	@ApiProperty({
		description: 'Unique identifier for the clinic',
		example: '9c1aaa0e-847c-4bca-9da0-72960eaa269d'
	})
	@IsNotEmpty()
	@IsUUID()
	clinicId!: string;

	@ApiProperty({
		description: 'name of the alert for creating clinic-alert',
		example: 'Aggressive'
	})
	@IsNotEmpty()
	alertName!: string;

	@ApiProperty({
		description: 'severity of the alert for creating patient-alert',
		example: 'Moderate'
	})
	@IsNotEmpty()
	severity!: string;
}
