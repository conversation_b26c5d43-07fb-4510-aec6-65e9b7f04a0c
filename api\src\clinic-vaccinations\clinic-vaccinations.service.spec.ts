import { Test, TestingModule } from '@nestjs/testing';
import { ILike, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ClinicVaccinationsService } from './clinic-vaccinations.service';
import { ClinicVaccinationEntity } from './entities/clinic-vaccination.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { UpdateVaccinationDto } from './dto/update-vaccinations.dto';
import { CreateVaccinationDto } from './dto/create-vaccinations.dto';

describe('ClinicVaccinationsService', () => {
	let service: ClinicVaccinationsService;
	let repository: jest.Mocked<Repository<ClinicVaccinationEntity>>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockVaccinations: ClinicVaccinationEntity[] = [
		{
			id: 'vaccination_uuid_1',
			clinicId: 'clinic_uuid_1',
			brandId: 'brand_uuid_1',
			uniqueId: 'unique_id_1',
			productName: 'vaccination_name_A',
			chargeablePrice: 0,
			tax: 10,
			currentStock: 0,
			minimumQuantity: 0
		},
		{
			id: 'vaccination_uuid_2',
			clinicId: 'clinic_uuid_1',
			brandId: 'brand_uuid_1',
			uniqueId: 'unique_id_2',
			productName: 'vaccination_name_B',
			chargeablePrice: 0,
			tax: 10,
			currentStock: 0,
			minimumQuantity: 0
		}
	];

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicVaccinationsService,
				{
					provide: getRepositoryToken(ClinicVaccinationEntity),
					useValue: {
						find: jest.fn(),
						save: jest.fn(),
						findOne: jest.fn(),
						findOneBy: jest.fn(),
						delete: jest.fn(),
						create: jest.fn(),
						count: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicVaccinationsService>(
			ClinicVaccinationsService
		);
		repository = module.get(getRepositoryToken(ClinicVaccinationEntity));
		logger = module.get(WinstonLogger);

		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('generateUniqueId', () => {
		it('should generate a unique ID with correct format', async () => {
			repository.count.mockResolvedValue(5);
			const uniqueId = await (service as any).generateUniqueId(
				'V_',
				'clinic_uuid'
			);
			expect(uniqueId).toBe('V_000006');
			expect(repository.count).toHaveBeenCalledWith({
				where: { clinicId: 'clinic_uuid' }
			});
		});

		it('should handle first vaccination case', async () => {
			repository.count.mockResolvedValue(0);
			const uniqueId = await (service as any).generateUniqueId(
				'V_',
				'clinic_uuid'
			);
			expect(uniqueId).toBe('V_000001');
		});

		it('should handle count error', async () => {
			const error = new Error('Database error');
			repository.count.mockRejectedValue(error);
			await expect(
				(service as any).generateUniqueId('V_', 'clinic_uuid')
			).rejects.toThrow(error);
		});
	});

	describe('getVaccinations', () => {
		it('should return all vaccinations for a clinic without a search keyword', async () => {
			repository.find.mockResolvedValue(mockVaccinations);

			const result = await service.getVaccinations(
				'clinic_uuid_1',
				undefined
			);

			expect(result).toEqual(mockVaccinations);
			expect(repository.find).toHaveBeenCalledWith({
				where: { clinicId: 'clinic_uuid_1' }
			});
		});

		it('should return filtered vaccinations for a clinic with a search keyword', async () => {
			const filteredVaccinations = [mockVaccinations[0]];
			repository.find.mockResolvedValue(filteredVaccinations);

			const result = await service.getVaccinations('clinic_uuid_1', 'A');

			expect(result).toEqual(filteredVaccinations);
			expect(repository.find).toHaveBeenCalledWith({
				where: { productName: ILike('%A%'), clinicId: 'clinic_uuid_1' }
			});
		});

		it('should handle database errors', async () => {
			const error = new Error('Database error');
			repository.find.mockRejectedValue(error);
			await expect(
				service.getVaccinations('clinic_uuid_1')
			).rejects.toThrow(error);
		});

		it('should return vaccinations with search keyword and clinicId', async () => {
			const searchKeyword = 'test';
			const clinicId = 'clinic_uuid';
			const vaccinations = [
				{
					id: 'vaccination_uuid',
					clinicId,
					brandId: 'brand_uuid',
					uniqueId: 'V_000001',
					productName: 'Test Vaccination',
					chargeablePrice: 100,
					tax: 10,
					currentStock: 50,
					minimumQuantity: 5
				}
			];
			repository.find.mockResolvedValue(vaccinations);

			const result = await service.getVaccinations(
				clinicId,
				searchKeyword
			);

			expect(result).toEqual(vaccinations);
			expect(repository.find).toHaveBeenCalledWith({
				where: {
					productName: ILike(`%${searchKeyword}%`),
					clinicId
				}
			});
		});


		it('should return vaccinations with only clinicId', async () => {
			const clinicId = 'clinic_uuid';
			const vaccinations = [
				{
					id: 'vaccination_uuid',
					clinicId,
					brandId: 'brand_uuid',
					uniqueId: 'V_000001',
					productName: 'Test Vaccination',
					chargeablePrice: 100,
					tax: 10,
					currentStock: 50,
					minimumQuantity: 5
				}
			];
			repository.find.mockResolvedValue(vaccinations);

			const result = await service.getVaccinations(clinicId);

			expect(result).toEqual(vaccinations);
			expect(repository.find).toHaveBeenCalledWith({
				where: { clinicId }
			});
		});
	});

	describe('bulkInsert', () => {
		const vaccinations: CreateVaccinationDto[] = [
			{
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				productName: 'Test Vaccination 1',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			},
			{
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				productName: 'Test Vaccination 2',
				chargeablePrice: 150,
				tax: 15,
				currentStock: 75,
				minimumQuantity: 10
			}
		];

		it('should insert new vaccinations successfully', async () => {
			repository.findOne.mockResolvedValue(null);
			repository.count.mockResolvedValue(0);
			repository.save.mockImplementation((entity: any) =>
				Promise.resolve(entity)
			);

			const result = await service.bulkInsert(vaccinations);

			expect(result).toBe(
				'Bulk insert of 2 vaccinations completed successfully'
			);
			expect(repository.save).toHaveBeenCalledTimes(2);
		});

		it('should update existing vaccinations', async () => {
			const existingVaccination = {
				id: 'vaccination_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'V_000001',
				productName: 'Test Vaccination 1',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			};
			repository.findOne.mockResolvedValue(existingVaccination);
			repository.save.mockImplementation((entity: any) =>
				Promise.resolve(entity)
			);

			const result = await service.bulkInsert([vaccinations[0]]);

			expect(result).toBe(
				'Bulk insert of 1 vaccinations completed successfully'
			);
			expect(repository.save).toHaveBeenCalledWith(
				expect.objectContaining({
					id: existingVaccination.id,
					productName: vaccinations[0].productName
				})
			);
		});

		it('should handle database error during bulk insert', async () => {
			repository.findOne.mockRejectedValue(new Error('Database error'));

			await expect(service.bulkInsert(vaccinations)).rejects.toThrow(
				'Failed to insert vaccinations'
			);
		});
	});

	describe('findOneEntry', () => {
		it('should find a vaccination by criteria', async () => {
			const criteria = {
				productName: 'Test Vaccination',
				clinicId: 'clinic_uuid'
			};
			const vaccination = {
				id: 'vaccination_uuid',
				...criteria,
				brandId: 'brand_uuid',
				uniqueId: 'V_000001',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			};
			repository.findOne.mockResolvedValue(vaccination);

			const result = await service.findOneEntry(criteria);

			expect(result).toEqual(vaccination);
			expect(repository.findOne).toHaveBeenCalledWith({
				where: criteria
			});
		});

		it('should return null if no vaccination found', async () => {
			const criteria = {
				productName: 'Nonexistent Vaccination',
				clinicId: 'clinic_uuid'
			};
			repository.findOne.mockResolvedValue(null);

			const result = await service.findOneEntry(criteria);

			expect(result).toBeNull();
		});
	});

	describe('deleteItem', () => {
		it('should delete a vaccination by id', async () => {
			const deleteResult = { affected: 1, raw: [] };
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('vaccination_uuid');

			expect(result).toEqual(deleteResult);
			expect(repository.delete).toHaveBeenCalledWith('vaccination_uuid');
		});
	});

	describe('create', () => {
		const createVaccinationDto: CreateVaccinationDto = {
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			productName: 'Test Vaccination',
			chargeablePrice: 100,
			tax: 10,
			currentStock: 50,
			minimumQuantity: 5
		};

		it('should create a new vaccination', async () => {
			repository.count.mockResolvedValue(0);
			repository.create.mockReturnValue({
				id: 'new_uuid',
				...createVaccinationDto,
				uniqueId: 'V_000001'
			} as ClinicVaccinationEntity);
			repository.save.mockImplementation((entity: any) => {
				return Promise.resolve(entity as ClinicVaccinationEntity);
			});

			const result = await service.create(createVaccinationDto);

			expect(result).toBeDefined();
			expect(result.uniqueId).toBe('V_000001');
			expect(result.productName).toBe(createVaccinationDto.productName);
			expect(repository.save).toHaveBeenCalled();
		});

		it('should handle database error during creation', async () => {
			repository.count.mockRejectedValue(new Error('Database error'));

			await expect(service.create(createVaccinationDto)).rejects.toThrow(
				'Database error'
			);
		});
	});

	describe('update', () => {
		const updateVaccinationDto: UpdateVaccinationDto = {
			productName: 'Updated Vaccination',
			chargeablePrice: 150,
			currentStock: 75
		};

		it('should update an existing vaccination', async () => {
			const existingVaccination = {
				id: 'vaccination_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'V_000001',
				productName: 'Test Vaccination',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			};

			repository.findOneBy.mockResolvedValue(existingVaccination);
			repository.save.mockImplementation((entity: any) =>
				Promise.resolve({
					...existingVaccination,
					...entity
				} as ClinicVaccinationEntity)
			);

			const result = await service.update(
				'vaccination_uuid',
				updateVaccinationDto
			);

			expect(result).toBeDefined();
			expect(result.productName).toBe(updateVaccinationDto.productName);
			expect(result.chargeablePrice).toBe(
				updateVaccinationDto.chargeablePrice
			);
			expect(repository.save).toHaveBeenCalled();
		});

		it('should throw error if vaccination not found during update', async () => {
			repository.findOneBy.mockResolvedValue(null);

			await expect(
				service.update('nonexistent_uuid', updateVaccinationDto)
			).rejects.toThrow('Vaccination with ID nonexistent_uuid not found');
		});

		it('should handle database error during update', async () => {
			repository.findOneBy.mockRejectedValue(new Error('Database error'));
			await expect(
				service.update('vaccination_uuid', updateVaccinationDto)
			).rejects.toThrow('Database error');
		});
	});

	describe('findOne', () => {
		it('should find a vaccination by id', async () => {
			const vaccination = {
				id: 'vaccination_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'V_000001',
				productName: 'Test Vaccination',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			};
			repository.findOneBy.mockResolvedValue(vaccination);

			const result = await service.findOne('vaccination_uuid');

			expect(result).toEqual(vaccination);
			expect(repository.findOneBy).toHaveBeenCalledWith({
				id: 'vaccination_uuid'
			});
		});

		it('should throw error if vaccination not found', async () => {
			repository.findOneBy.mockResolvedValue(null);

			await expect(service.findOne('nonexistent_uuid')).rejects.toThrow(
				'Vaccination with ID nonexistent_uuid not found'
			);
		});

		it('should handle database error during findOne', async () => {
			repository.findOneBy.mockRejectedValue(new Error('Database error'));
			await expect(service.findOne('vaccination_uuid')).rejects.toThrow(
				'Database error'
			);
		});
	});

	describe('remove', () => {
		it('should remove a vaccination successfully', async () => {
			repository.delete.mockResolvedValue({ affected: 1, raw: [] });

			await service.remove('vaccination_uuid');

			expect(repository.delete).toHaveBeenCalledWith('vaccination_uuid');
		});

		it('should throw error if vaccination not found during removal', async () => {
			repository.delete.mockResolvedValue({ affected: 0, raw: [] });

			await expect(service.remove('nonexistent_uuid')).rejects.toThrow(
				'Vaccination with ID nonexistent_uuid not found'
			);
		});

		it('should handle database error during removal', async () => {
			repository.delete.mockRejectedValue(new Error('Database error'));

			await expect(service.remove('vaccination_uuid')).rejects.toThrow(
				'Database error'
			);
		});
	});
});
