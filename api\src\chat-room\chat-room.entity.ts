import {
	<PERSON><PERSON><PERSON>,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	OneToMany
} from 'typeorm';
import { ChatRoomUser } from './chat-room-users.entity';
import { ChatRoomMessage } from './chat-room-messages.entity';
@Entity('chat_rooms')
export class ChatRoom {
	@PrimaryGeneratedColumn('uuid')
	id!: string;
	@Column({ name: 'last_message' })
	lastMessage!: string;

	@Column({ name: 'last_message_sender' })
	lastMessageSender!: string;

	@Column({
		name: 'unread_messages',
		type: 'integer',
		nullable: true
	})
	unreadMessage!: number;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@OneToMany(() => ChatRoomUser, chatRoomUser => chatRoomUser.chatRoom)
	users!: ChatRoomUser[];
	@OneToMany(
		() => ChatRoomMessage,
		chatRoomMessage => chatRoomMessage.chatRoom
	)
	messages!: ChatRoomMessage[];
}
