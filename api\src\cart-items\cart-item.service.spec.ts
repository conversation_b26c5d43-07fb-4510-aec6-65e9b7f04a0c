import { Test, TestingModule } from '@nestjs/testing';
import { CartItemService } from './cart-item.service';
import { Repository, UpdateResult } from 'typeorm';
import { CartItemEntity } from './entities/cart-item.entity';
import { CreateCartItemDto } from './dto/create-cart-item.dto';
import { NotFoundException } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
import { CartsService } from '../carts/carts.service';
import { BulkInsertIntoCartItemDto } from './dto/bulkInsert-cartItem.dto';

describe('CartItemService', () => {
	let service: CartItemService;
	let cartItemRepository: jest.Mocked<Repository<CartItemEntity>>;
	let cartsService: jest.Mocked<CartsService>;

	const cartId = 'cart_uuid';
	const appointmentId = 'appointment_uuid';

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				CartItemService,
				{
					provide: getRepositoryToken(CartItemEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						find: jest.fn(),
						update: jest.fn()
					}
				},
				{
					provide: CartsService,
					useValue: {
						createCart: jest.fn().mockResolvedValue({ id: cartId })
					}
				}
			]
		}).compile();

		service = module.get<CartItemService>(CartItemService);
		cartItemRepository = module.get(getRepositoryToken(CartItemEntity));
		cartsService = module.get(CartsService);

		// Add the missing method to the service mock
		cartsService.createCart = jest.fn().mockResolvedValue({ id: cartId });
		service.getCartItemDetailsByCartId = jest
			.fn()
			.mockImplementation(service.getCartItemDetailsByCartId);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	// Add test to ensure getCartItemDetailsByCartId method is defined and accessible
	it('should have getCartItemDetailsByCartId method', () => {
		expect(service.getCartItemDetailsByCartId).toBeDefined();
	});

	describe('createCartItem', () => {
		const mockInputCarDto: CreateCartItemDto = {
			appointmentId: appointmentId
		};

		const mockOutputEntity: CartItemEntity = {
			id: 'cart_item_uuid',
			appointmentId: appointmentId,
			productId: 'product_uuid',
			type: '',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: cartId
		};

		it('should have createCartItem function', () => {
			expect(service.createCartItem).toBeDefined();
		});

		it('should create a new cart item', async () => {
			const expectedCartItemDto = { ...mockInputCarDto, cartId };

			cartItemRepository.save.mockResolvedValue(mockOutputEntity);
			const result = await service.createCartItem(expectedCartItemDto);

			expect(result).toEqual({
				...mockOutputEntity,
				lineItemId: undefined
			});
			expect(cartItemRepository.save).toHaveBeenCalledWith(
				expectedCartItemDto
			);
		});

		it('should create cart item with impromptu appointment data', async () => {
			const impromptuDto: CreateCartItemDto = {
				impromptu: true,
				patientId: 'patient_uuid',
				cartId: cartId
			};

			const userContext = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				userId: 'user_uuid'
			};

			const mockCart = {
				id: cartId,
				appointmentId: 'new_appointment_uuid',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				invoice: []
			};
			cartsService.createCart.mockResolvedValue(mockCart);
			cartItemRepository.save.mockResolvedValue({
				...mockOutputEntity,
				appointmentId: 'new_appointment_uuid'
			});

			const result = await service.createCartItem(
				impromptuDto,
				userContext
			);

			expect(cartsService.createCart).toHaveBeenCalledWith(
				undefined,
				cartId,
				{
					impromptu: true,
					patientId: 'patient_uuid',
					clinicId: 'clinic_uuid',
					brandId: 'brand_uuid',
					userId: 'user_uuid'
				}
			);
			expect(result.appointmentId).toBe('new_appointment_uuid');
		});

		it('should handle lineItemId in response', async () => {
			const dtoWithLineItem: CreateCartItemDto = {
				...mockInputCarDto,
				lineItemId: 'line_item_uuid'
			};

			cartItemRepository.save.mockResolvedValue(mockOutputEntity);
			const result = await service.createCartItem(dtoWithLineItem);

			expect(result.lineItemId).toBe('line_item_uuid');
		});

		it('should not create impromptu appointment when patientId is missing', async () => {
			const impromptuDto: CreateCartItemDto = {
				impromptu: true,
				// patientId missing
				cartId: cartId
			};

			const userContext = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				userId: 'user_uuid'
			};

			cartItemRepository.save.mockResolvedValue(mockOutputEntity);
			await service.createCartItem(impromptuDto, userContext);

			expect(cartsService.createCart).toHaveBeenCalledWith(
				undefined,
				cartId,
				undefined
			);
		});
	});

	describe('deleteCartItem', () => {
		const mockOutputEntity: CartItemEntity = {
			id: 'cart_item_uuid',
			appointmentId: appointmentId,
			productId: 'product_uuid',
			type: 'product',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: cartId
		};

		it('should have deleteCartItem function', () => {
			expect(service.deleteCartItem).toBeDefined();
		});

		it('should throw an error if the cart with the given cart id is not found', async () => {
			cartItemRepository.findOne.mockResolvedValue(null);

			await expect(service.deleteCartItem(cartId)).rejects.toThrow(
				NotFoundException
			);
			expect(cartItemRepository.findOne).toHaveBeenCalledWith({
				where: { id: cartId }
			});
		});

		it('should delete the record successfully (when one record is found)', async () => {
			cartItemRepository.findOne.mockResolvedValue(mockOutputEntity);

			const mockDeleteResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 1
			};

			cartItemRepository.delete.mockResolvedValue(mockDeleteResult);
			const result = await service.deleteCartItem(cartId);
			expect(result).toEqual({ status: true });
			expect(cartItemRepository.findOne).toHaveBeenCalledWith({
				where: { id: cartId }
			});
		});

		it('should return false if delete fails (multiple records found)', async () => {
			const mockCartResult = {
				status: false,
				message: 'Multiple records deleted. Check your query.'
			};
			cartItemRepository.findOne.mockResolvedValue(mockOutputEntity);

			const mockDeleteResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 2
			};

			cartItemRepository.delete.mockResolvedValue(mockDeleteResult);
			const result = await service.deleteCartItem(cartId);
			expect(result).toEqual(mockCartResult);
		});

		it('should return false if record is not found to delete', async () => {
			const mockCartResult = {
				status: false,
				message: 'No records found to delete.'
			};
			cartItemRepository.findOne.mockResolvedValue(mockOutputEntity);

			const mockDeleteResult: UpdateResult = {
				generatedMaps: [],
				raw: [],
				affected: 0
			};

			cartItemRepository.delete.mockResolvedValue(mockDeleteResult);
			const result = await service.deleteCartItem(cartId);
			expect(result).toEqual(mockCartResult);
		});

		it('should handle delete with source parameter', async () => {
			cartItemRepository.findOne.mockResolvedValue(mockOutputEntity);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: [],
				generatedMaps: []
			} as UpdateResult);

			const result = await service.deleteCartItem(cartId, 'api');
			expect(result).toEqual({ status: true });
		});
	});

	describe('getCartItemDetailsByAppointmentId', () => {
		const mockOutputEntity: CartItemEntity = {
			id: 'cart_item_uuid',
			appointmentId: appointmentId,
			productId: 'product_uuid',
			type: 'product',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: cartId
		};

		it('should have getCartItemDetailsByAppointmentId function', () => {
			expect(service.getCartItemDetailsByAppointmentId).toBeDefined();
		});

		it('should return the list of cart items with transformed data', async () => {
			const mockInputCartEntityList: CartItemEntity[] = [
				{
					...mockOutputEntity,
					product: { name: 'Test Product' },
					service: undefined,
					vaccination: undefined,
					prescription: undefined,
					labReport: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockInputCartEntityList);
			const result =
				await service.getCartItemDetailsByAppointmentId(appointmentId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toEqual({ name: 'Test Product' });
			expect(result[0].product).toBeUndefined();
			expect(cartItemRepository.find).toHaveBeenCalledWith({
				where: { appointmentId },
				order: { createdAt: 'DESC' }
			});
		});

		it('should transform items with different types', async () => {
			const mockItems = [
				{ ...mockOutputEntity, service: { name: 'Test Service' } },
				{ ...mockOutputEntity, vaccination: { name: 'Test Vaccine' } },
				{
					...mockOutputEntity,
					prescription: { name: 'Test Medicine' }
				},
				{ ...mockOutputEntity, labReport: { name: 'Test Lab' } }
			] as any[];

			cartItemRepository.find.mockResolvedValue(mockItems);
			const result =
				await service.getCartItemDetailsByAppointmentId(appointmentId);

			expect(result).toHaveLength(4);
			expect(result[0].itemDetails).toEqual({ name: 'Test Service' });
			expect(result[1].itemDetails).toEqual({ name: 'Test Vaccine' });
			expect(result[2].itemDetails).toEqual({ name: 'Test Medicine' });
			expect(result[3].itemDetails).toEqual({ name: 'Test Lab' });
		});

		it('should handle items with no specific type details', async () => {
			const mockItems = [{ ...mockOutputEntity }] as any[];

			cartItemRepository.find.mockResolvedValue(mockItems);
			const result =
				await service.getCartItemDetailsByAppointmentId(appointmentId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toBeNull();
		});
	});

	describe('getCartItemDetailsByCartId', () => {
		const mockOutputEntity: CartItemEntity = {
			id: 'cart_item_uuid',
			appointmentId: appointmentId,
			productId: 'product_uuid',
			type: 'product',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: cartId
		};

		it('should have getCartItemDetailsByCartId function', () => {
			expect(service.getCartItemDetailsByCartId).toBeDefined();
		});

		it('should return the list of cart items by cartId', async () => {
			const mockInputCartEntityList: CartItemEntity[] = [
				{
					...mockOutputEntity,
					product: { name: 'Test Product' }
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockInputCartEntityList);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toEqual({ name: 'Test Product' });
			expect(cartItemRepository.find).toHaveBeenCalledWith({
				where: { cartId },
				order: { createdAt: 'DESC' }
			});
		});

		// Test all different item types to improve coverage
		it('should transform cart items with service details', async () => {
			const mockServiceItem: CartItemEntity[] = [
				{
					...mockOutputEntity,
					service: { name: 'Test Service', price: 100 },
					product: undefined,
					vaccination: undefined,
					prescription: undefined,
					labReport: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockServiceItem);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toEqual({
				name: 'Test Service',
				price: 100
			});
			expect(result[0].service).toBeUndefined();
			expect(result[0].product).toBeUndefined();
		});

		it('should transform cart items with vaccination details', async () => {
			const mockVaccinationItem: CartItemEntity[] = [
				{
					...mockOutputEntity,
					vaccination: { name: 'Test Vaccine', dosage: '0.5ml' },
					product: undefined,
					service: undefined,
					prescription: undefined,
					labReport: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockVaccinationItem);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toEqual({
				name: 'Test Vaccine',
				dosage: '0.5ml'
			});
			expect(result[0].vaccination).toBeUndefined();
			expect(result[0].product).toBeUndefined();
		});

		it('should transform cart items with prescription details', async () => {
			const mockPrescriptionItem: CartItemEntity[] = [
				{
					...mockOutputEntity,
					prescription: { name: 'Test Medicine', strength: '500mg' },
					product: undefined,
					service: undefined,
					vaccination: undefined,
					labReport: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockPrescriptionItem);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toEqual({
				name: 'Test Medicine',
				strength: '500mg'
			});
			expect(result[0].prescription).toBeUndefined();
			expect(result[0].product).toBeUndefined();
		});

		it('should transform cart items with labReport details', async () => {
			const mockLabReportItem: CartItemEntity[] = [
				{
					...mockOutputEntity,
					labReport: { name: 'Blood Test', reportType: 'CBC' },
					product: undefined,
					service: undefined,
					vaccination: undefined,
					prescription: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockLabReportItem);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toEqual({
				name: 'Blood Test',
				reportType: 'CBC'
			});
			expect(result[0].labReport).toBeUndefined();
			expect(result[0].product).toBeUndefined();
		});

		it('should handle cart items with no specific item details (null case)', async () => {
			const mockItemWithNoDetails: CartItemEntity[] = [
				{
					...mockOutputEntity,
					product: undefined,
					service: undefined,
					vaccination: undefined,
					prescription: undefined,
					labReport: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockItemWithNoDetails);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(1);
			expect(result[0].itemDetails).toBeNull();
			expect(result[0].product).toBeUndefined();
			expect(result[0].service).toBeUndefined();
			expect(result[0].vaccination).toBeUndefined();
			expect(result[0].prescription).toBeUndefined();
			expect(result[0].labReport).toBeUndefined();
		});

		it('should handle mixed item types in the same cart', async () => {
			const mockMixedItems: CartItemEntity[] = [
				{
					...mockOutputEntity,
					id: 'item1',
					product: { name: 'Test Product' },
					service: undefined,
					vaccination: undefined,
					prescription: undefined,
					labReport: undefined
				} as any,
				{
					...mockOutputEntity,
					id: 'item2',
					product: undefined,
					service: { name: 'Test Service' },
					vaccination: undefined,
					prescription: undefined,
					labReport: undefined
				} as any,
				{
					...mockOutputEntity,
					id: 'item3',
					product: undefined,
					service: undefined,
					vaccination: { name: 'Test Vaccine' },
					prescription: undefined,
					labReport: undefined
				} as any
			];

			cartItemRepository.find.mockResolvedValue(mockMixedItems);
			const result = await service.getCartItemDetailsByCartId(cartId);

			expect(result).toHaveLength(3);
			expect(result[0].itemDetails).toEqual({ name: 'Test Product' });
			expect(result[1].itemDetails).toEqual({ name: 'Test Service' });
			expect(result[2].itemDetails).toEqual({ name: 'Test Vaccine' });
		});
	});

	describe('updateCartItemDetails', () => {
		const mockCartId = 'cart_item_uuid';
		const mockUpdateDto: UpdateCartItemDto = {
			quantity: 10,
			price: 100
		};

		const mockCartItem: CartItemEntity = {
			id: mockCartId,
			appointmentId: appointmentId,
			productId: 'product_uuid',
			type: 'product',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: cartId,
			quantity: 5,
			price: 50
		};

		it('should have updateCartItemDetails function', () => {
			expect(service.updateCartItemDetails).toBeDefined();
		});

		it('should throw NotFoundException if cart item not found', async () => {
			cartItemRepository.findOne.mockResolvedValue(null);

			await expect(
				service.updateCartItemDetails(mockCartId, mockUpdateDto)
			).rejects.toThrow(NotFoundException);
		});

		it('should update cart item details successfully', async () => {
			const updatedCartItem = { ...mockCartItem, ...mockUpdateDto };

			cartItemRepository.findOne
				.mockResolvedValueOnce(mockCartItem) // First call for finding existing item
				.mockResolvedValueOnce(updatedCartItem); // Second call for returning updated item

			cartItemRepository.update.mockResolvedValue({
				affected: 1
			} as UpdateResult);

			const result = await service.updateCartItemDetails(
				mockCartId,
				mockUpdateDto
			);

			expect(result).toEqual(updatedCartItem);
			expect(cartItemRepository.update).toHaveBeenCalledWith(mockCartId, {
				...mockCartItem,
				...mockUpdateDto
			});
		});

		it('should return status false if update affected no rows', async () => {
			cartItemRepository.findOne.mockResolvedValue(mockCartItem);
			cartItemRepository.update.mockResolvedValue({
				affected: 0
			} as UpdateResult);

			const result = await service.updateCartItemDetails(
				mockCartId,
				mockUpdateDto
			);

			expect(result).toEqual({ status: false });
		});

		it('should return status false if updated item not found after update', async () => {
			cartItemRepository.findOne
				.mockResolvedValueOnce(mockCartItem)
				.mockResolvedValueOnce(null); // Updated item not found

			cartItemRepository.update.mockResolvedValue({
				affected: 1
			} as UpdateResult);

			const result = await service.updateCartItemDetails(
				mockCartId,
				mockUpdateDto
			);

			expect(result).toEqual({ status: false });
		});

		it('should handle update with source parameter', async () => {
			const updatedCartItem = { ...mockCartItem, ...mockUpdateDto };
			cartItemRepository.findOne
				.mockResolvedValueOnce(mockCartItem)
				.mockResolvedValueOnce(updatedCartItem);
			cartItemRepository.update.mockResolvedValue({
				affected: 1
			} as UpdateResult);

			const result = await service.updateCartItemDetails(
				mockCartId,
				mockUpdateDto,
				'api'
			);

			expect(result).toEqual(updatedCartItem);
		});
	});

	describe('createCartAndBulkInsert', () => {
		const mockBulkDto: BulkInsertIntoCartItemDto = {
			appointmentId: appointmentId,
			prescriptionIds: ['prescription1', 'prescription2'],
			addedFrom: 'api'
		};

		it('should have createCartAndBulkInsert function', () => {
			expect(service.createCartAndBulkInsert).toBeDefined();
		});

		it('should create cart and bulk insert prescription items', async () => {
			const mockCart = {
				id: cartId,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				invoice: []
			};
			const expectedInsertArray = [
				{
					appointmentId: appointmentId,
					cartId: cartId,
					prescriptionId: 'prescription1',
					type: 'Medication',
					isAddedToCart: false,
					addedFrom: 'api'
				},
				{
					appointmentId: appointmentId,
					cartId: cartId,
					prescriptionId: 'prescription2',
					type: 'Medication',
					isAddedToCart: false,
					addedFrom: 'api'
				}
			];

			cartsService.createCart.mockResolvedValue(mockCart);
			cartItemRepository.save.mockResolvedValue(
				expectedInsertArray as any
			);

			const result = await service.createCartAndBulkInsert(mockBulkDto);

			expect(cartsService.createCart).toHaveBeenCalledWith(appointmentId);
			expect(cartItemRepository.save).toHaveBeenCalledWith(
				expectedInsertArray
			);
			expect(result).toEqual(expectedInsertArray);
		});

		it('should handle empty prescription ids', async () => {
			const emptyBulkDto: BulkInsertIntoCartItemDto = {
				appointmentId: appointmentId,
				prescriptionIds: [],
				addedFrom: 'api'
			};

			const mockCart = {
				id: cartId,
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				invoice: []
			};
			cartsService.createCart.mockResolvedValue(mockCart);
			cartItemRepository.save.mockResolvedValue([] as any);

			const result = await service.createCartAndBulkInsert(emptyBulkDto);

			expect(cartItemRepository.save).toHaveBeenCalledWith([]);
			expect(result).toEqual([]);
		});
	});

	// Test private helper methods indirectly through public methods
	describe('Helper methods (tested indirectly)', () => {
		it('should determine item type correctly for different items', async () => {
			const dtoWithProduct: CreateCartItemDto = {
				appointmentId: appointmentId,
				productId: 'product_uuid'
			};

			cartItemRepository.save.mockResolvedValue({} as CartItemEntity);
			await service.createCartItem(dtoWithProduct);

			// The determineItemType method should be called internally
			expect(cartsService.createCart).toHaveBeenCalled();
		});

		it('should get item details correctly', async () => {
			const mockCartItem: CartItemEntity = {
				id: 'cart_item_uuid',
				appointmentId: appointmentId,
				productId: 'product_uuid',
				type: 'product',
				quantity: 5,
				price: 100,
				isAddedToCart: true,
				addedFrom: 'manual',
				comment: 'test comment',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			};

			cartItemRepository.findOne.mockResolvedValue(mockCartItem);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: [],
				generatedMaps: []
			} as UpdateResult);

			await service.deleteCartItem('cart_item_uuid');

			// The getItemDetails method should be called internally during logging
			expect(cartItemRepository.findOne).toHaveBeenCalled();
		});

		// Test determineItemType method indirectly with different item types
		it('should handle creating cart item with serviceId (test determineItemType)', async () => {
			const dtoWithService: CreateCartItemDto = {
				appointmentId: appointmentId,
				serviceId: 'service_uuid'
			};

			cartItemRepository.save.mockResolvedValue({
				...dtoWithService,
				id: 'cart_item_uuid',
				type: 'Service',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			} as CartItemEntity);

			const result = await service.createCartItem(dtoWithService);
			expect(cartItemRepository.save).toHaveBeenCalledWith({
				...dtoWithService,
				cartId: cartId
			});
		});

		it('should handle creating cart item with vaccinationId (test determineItemType)', async () => {
			const dtoWithVaccination: CreateCartItemDto = {
				appointmentId: appointmentId,
				vaccinationId: 'vaccination_uuid'
			};

			cartItemRepository.save.mockResolvedValue({
				...dtoWithVaccination,
				id: 'cart_item_uuid',
				type: 'Vaccination',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			} as CartItemEntity);

			const result = await service.createCartItem(dtoWithVaccination);
			expect(cartItemRepository.save).toHaveBeenCalledWith({
				...dtoWithVaccination,
				cartId: cartId
			});
		});

		it('should handle creating cart item with prescriptionId (test determineItemType)', async () => {
			const dtoWithPrescription: CreateCartItemDto = {
				appointmentId: appointmentId,
				prescriptionId: 'prescription_uuid'
			};

			cartItemRepository.save.mockResolvedValue({
				...dtoWithPrescription,
				id: 'cart_item_uuid',
				type: 'Prescription',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			} as CartItemEntity);

			const result = await service.createCartItem(dtoWithPrescription);
			expect(cartItemRepository.save).toHaveBeenCalledWith({
				...dtoWithPrescription,
				cartId: cartId
			});
		});

		it('should handle creating cart item with labReportId (test determineItemType)', async () => {
			const dtoWithLabReport: CreateCartItemDto = {
				appointmentId: appointmentId,
				labReportId: 'labreport_uuid'
			};

			cartItemRepository.save.mockResolvedValue({
				...dtoWithLabReport,
				id: 'cart_item_uuid',
				type: 'LabReport',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			} as CartItemEntity);

			const result = await service.createCartItem(dtoWithLabReport);
			expect(cartItemRepository.save).toHaveBeenCalledWith({
				...dtoWithLabReport,
				cartId: cartId
			});
		});

		it('should handle creating cart item with no specific ID (test determineItemType Unknown case)', async () => {
			const dtoWithNoSpecificId: CreateCartItemDto = {
				appointmentId: appointmentId
				// No productId, serviceId, vaccinationId, prescriptionId, or labReportId
			};

			cartItemRepository.save.mockResolvedValue({
				...dtoWithNoSpecificId,
				id: 'cart_item_uuid',
				type: 'Unknown',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			} as CartItemEntity);

			const result = await service.createCartItem(dtoWithNoSpecificId);
			expect(cartItemRepository.save).toHaveBeenCalledWith({
				...dtoWithNoSpecificId,
				cartId: cartId
			});
		});

		// Test getItemDetails method indirectly with different item types
		it('should get item details correctly for service item (test getItemDetails)', async () => {
			const mockServiceCartItem: CartItemEntity = {
				id: 'cart_item_uuid',
				appointmentId: appointmentId,
				serviceId: 'service_uuid', // serviceId instead of productId
				type: 'service',
				quantity: 3,
				price: 200,
				isAddedToCart: false,
				addedFrom: 'api',
				comment: 'service comment',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			};

			cartItemRepository.findOne.mockResolvedValue(mockServiceCartItem);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: [],
				generatedMaps: []
			} as UpdateResult);

			await service.deleteCartItem('cart_item_uuid');
			expect(cartItemRepository.findOne).toHaveBeenCalled();
		});

		it('should get item details correctly for vaccination item (test getItemDetails)', async () => {
			const mockVaccinationCartItem: CartItemEntity = {
				id: 'cart_item_uuid',
				appointmentId: appointmentId,
				vaccinationId: 'vaccination_uuid', // vaccinationId
				type: 'vaccination',
				quantity: 1,
				price: 150,
				isAddedToCart: true,
				addedFrom: 'manual',
				comment: 'vaccination comment',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			};

			cartItemRepository.findOne.mockResolvedValue(
				mockVaccinationCartItem
			);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: [],
				generatedMaps: []
			} as UpdateResult);

			await service.deleteCartItem('cart_item_uuid');
			expect(cartItemRepository.findOne).toHaveBeenCalled();
		});

		it('should get item details correctly for prescription item (test getItemDetails)', async () => {
			const mockPrescriptionCartItem: CartItemEntity = {
				id: 'cart_item_uuid',
				appointmentId: appointmentId,
				prescriptionId: 'prescription_uuid', // prescriptionId
				type: 'prescription',
				quantity: 2,
				price: 50,
				isAddedToCart: true,
				addedFrom: 'automated',
				comment: 'prescription comment',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			};

			cartItemRepository.findOne.mockResolvedValue(
				mockPrescriptionCartItem
			);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: [],
				generatedMaps: []
			} as UpdateResult);

			await service.deleteCartItem('cart_item_uuid');
			expect(cartItemRepository.findOne).toHaveBeenCalled();
		});

		it('should get item details correctly for lab report item (test getItemDetails)', async () => {
			const mockLabReportCartItem: CartItemEntity = {
				id: 'cart_item_uuid',
				appointmentId: appointmentId,
				labReportId: 'labreport_uuid', // labReportId
				type: 'labreport',
				quantity: 1,
				price: 300,
				isAddedToCart: false,
				addedFrom: 'manual',
				comment: 'lab report comment',
				createdAt: new Date(),
				updatedAt: new Date(),
				createdBy: '',
				updatedBy: '',
				cartId: cartId
			};

			cartItemRepository.findOne.mockResolvedValue(mockLabReportCartItem);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: [],
				generatedMaps: []
			} as UpdateResult);

			await service.deleteCartItem('cart_item_uuid');
			expect(cartItemRepository.findOne).toHaveBeenCalled();
		});
	});
});
