import { IsObject, IsString, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

class DaySchedule {
	@ApiProperty({ example: '09:00', required: false })
	@IsString()
	@IsOptional()
	startTime!: string | null;

	@ApiProperty({ example: '17:00', required: false })
	@IsString()
	@IsOptional()
	endTime!: string | null;

	@ApiProperty({ example: true })
	@IsBoolean()
	isWorkingDay!: boolean;
}

export class WorkingHoursDto {
	@ApiProperty({
		example: {
			monday: {
				startTime: '09:00',
				endTime: '17:00',
				isWorkingDay: true
			},
			tuesday: {
				startTime: '09:00',
				endTime: '17:00',
				isWorkingDay: true
			},
			wednesday: {
				startTime: '09:00',
				endTime: '17:00',
				isWorkingDay: true
			},
			thursday: {
				startTime: '09:00',
				endTime: '17:00',
				isWorkingDay: true
			},
			friday: {
				startTime: '09:00',
				endTime: '17:00',
				isWorkingDay: true
			},
			saturday: { startTime: null, endTime: null, isWorkingDay: false },
			sunday: { startTime: null, endTime: null, isWorkingDay: false }
		}
	})
	@IsObject()
	workingHours!: {
		[key: string]: DaySchedule;
	};
}
