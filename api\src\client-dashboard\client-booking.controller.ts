import {
	Body,
	Controller,
	Get,
	Param,
	Post,
	Put,
	Req,
	UseGuards,
	ForbiddenException,
	Logger,
	Delete
} from '@nestjs/common';
import {
	ApiBearerAuth,
	ApiOperation,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import {
	CreateClientBookingDto,
	UpdateClientBookingDto,
	ClientBookingResponseDto
} from './dto/client-booking.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';
import { ClientBookingService } from './client-booking.service';
import { Role } from '../roles/role.enum';

@ApiTags('Client Booking')
@Controller()
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ClientBookingController {
	private readonly logger = new Logger(ClientBookingController.name);

	constructor(private readonly clientBookingService: ClientBookingService) {}

	@Post('client-booking')
	@ApiOperation({ summary: 'Create a new booking' })
	@ApiResponse({
		status: 201,
		description: 'Booking created successfully',
		type: ClientBookingResponseDto
	})
	@Roles(Role.OWNER, Role.ADMIN)
	async createBooking(
		@Body() createBookingDto: CreateClientBookingDto,
		@Req() req: RequestWithUser
	): Promise<ClientBookingResponseDto> {
		// Extract owner ID from the authenticated user (uses 'sub' from JWT)
		const ownerId = req.user.userId;

		// Ensure ownerId is present
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to book appointments'
			);
		}

		return this.clientBookingService.createClientBooking(
			createBookingDto,
			ownerId
		);
	}

	@Get('client-booking/:appointmentId')
	@ApiOperation({ summary: 'Get booking details' })
	@ApiResponse({
		status: 200,
		description: 'Booking details retrieved successfully',
		type: ClientBookingResponseDto
	})
	@Roles(Role.OWNER, Role.ADMIN)
	async getBooking(
		@Param('appointmentId') appointmentId: string,
		@Req() req: RequestWithUser
	): Promise<ClientBookingResponseDto> {
		// Extract owner ID from the authenticated user
		const ownerId = req.user.userId;

		// Ensure ownerId is present
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to view appointments'
			);
		}

		return this.clientBookingService.getClientBooking(
			appointmentId,
			ownerId
		);
	}

	@Put('client-booking/:appointmentId')
	@ApiOperation({ summary: 'Update a booking (reschedule or cancel)' })
	@ApiResponse({
		status: 200,
		description: 'Booking updated successfully',
		type: ClientBookingResponseDto
	})
	@Roles(Role.OWNER, Role.ADMIN)
	async updateBooking(
		@Param('appointmentId') appointmentId: string,
		@Body() updateBookingDto: UpdateClientBookingDto,
		@Req() req: RequestWithUser
	): Promise<ClientBookingResponseDto> {
		// Extract owner ID from the authenticated user
		const ownerId = req.user.userId;

		// Ensure ownerId is present
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to update appointments'
			);
		}

		return this.clientBookingService.updateClientBooking(
			appointmentId,
			updateBookingDto,
			ownerId
		);
	}

	@Delete('client-booking/:appointmentId')
	@ApiOperation({ summary: 'Cancel/delete a booking' })
	@ApiResponse({
		status: 200,
		description: 'Booking cancelled successfully',
		type: ClientBookingResponseDto
	})
	@Roles(Role.OWNER, Role.ADMIN)
	async deleteBooking(
		@Param('appointmentId') appointmentId: string,
		@Req() req: RequestWithUser
	): Promise<ClientBookingResponseDto> {
		// Extract owner ID from the authenticated user
		const ownerId = req.user.userId;

		// Ensure ownerId is present
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to cancel appointments'
			);
		}

		return this.clientBookingService.deleteClientBooking(
			appointmentId,
			ownerId
		);
	}
}
