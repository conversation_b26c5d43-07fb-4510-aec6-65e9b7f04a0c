import {
	Controller,
	Post,
	Body,
	Get,
	InternalServerErrorException,
	BadRequestException,
	UploadedFile,
	UseInterceptors,
	HttpException,
	HttpStatus,
	Param,
	Put,
	Query
} from '@nestjs/common';
import { DataService } from './data.service';
import {
	CreateAppointmentDetailsDto,
	CreateAppointmentDto,
	CreateOwnerDto,
	CreatePatientDto,
	CreatePatientOwnerDto,
	DiagnosticsDataDto,
	InvoiceDataDto,
	MigrateAppointmentDto,
	PatientLookupDto,
	BulkUpdateOpeningBalanceDto
} from './dto/data.dto';
import { S3Service } from '../utils/aws/s3/s3.service';
import axios from 'axios';
import { uuidv4 } from 'uuidv7';
import { delay } from 'rxjs';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { DataSource } from 'typeorm';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';

@Controller('data-migration')
export class DataController {
	constructor(
		private readonly dataService: DataService,
		private readonly s3Service: S3Service,
		private readonly logger: WinstonLogger,
		private dataSource: DataSource
	) {}

	@Get('/health')
	getHello(): string {
		return this.dataService.getHello();
	}

	@Post('owners')
	async createOwner(@Body() createOwnerDto: CreateOwnerDto) {
		return this.dataService.createOwner(createOwnerDto);
	}

	@Post('patients')
	async createPatient(@Body() createPatientDto: CreatePatientDto) {
		return this.dataService.createPatient(createPatientDto);
	}

	@Get('patients/lookup')
	async getPatients(
		@Query('clinicId') clinicId: string,
		@Query('dummyData.patientId') patientId: string
	) {
		try {
			const patient = await this.dataService.findByOldPatientId(
				patientId,
				clinicId
			);

			if (!patient) {
				throw new HttpException(
					`Patient with old ID "${patientId}" not found`,
					HttpStatus.NOT_FOUND
				);
			}

			return {
				id: patient.id,
				oldPatientId: patientId,
				clinicId: patient.clinicId,
				status: 'found'
			};
		} catch (error) {
			if (error instanceof HttpException) {
				throw error;
			}

			throw new HttpException(
				'Error finding patient',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Post('patient-owners')
	async createPatientOwner(
		@Body() createPatientOwnerDto: CreatePatientOwnerDto
	) {
		return this.dataService.createPatientOwner(createPatientOwnerDto);
	}

	@Post('appointments')
	async createAppointment(
		@Body() createAppointmentDto: CreateAppointmentDto
	) {
		return this.dataService.createAppointment(createAppointmentDto);
	}

	// @Post('appointment-details')
	// async createAppointmentDetails(@Body() createAppointmentDetailsDto: CreateAppointmentDetailsDto) {
	//   console.log("App det",createAppointmentDetailsDto)
	//   return this.dataService.createAppointmentDetails(createAppointmentDetailsDto);
	// }

	@Post('appointment-details')
	async createAppointmentDetails(
		@Body() createAppointmentDetailsDto: CreateAppointmentDetailsDto
	) {
		try {
			// Process attachments if they exist
			if (
				createAppointmentDetailsDto.details?.attachments?.list?.length >
				0
			) {
				const processedAttachments = await this.processAttachments(
					createAppointmentDetailsDto.details.attachments.list,
					createAppointmentDetailsDto.appointmentId
				);
				createAppointmentDetailsDto.details.attachments.list =
					processedAttachments;
			}

			return await this.dataService.createAppointmentDetails(
				createAppointmentDetailsDto
			);
		} catch (error) {
			throw new InternalServerErrorException(
				'Failed to process appointment details'
			);
		}
	}
	private async processAttachments(
		attachments: any[],
		appointmentId: string
	): Promise<any[]> {
		const processedAttachments = [];

		for (const attachment of attachments) {
			try {
				console.log('---------------------------------', attachment);
				// Skip processing for EMR Documents - use S3 key directly
				if (attachment.attachementName === 'EMR Document') {
					this.logger.log(
						`Using existing S3 key for EMR Document: ${attachment.fileKey}`
					);
					processedAttachments.push(attachment);
					continue;
				}

				// Add delay to prevent rate limiting
				delay(500);

				// Download file
				const fileBuffer = await this.downloadFile(attachment.fileKey);

				// Generate UUID for unique S3 key
				const fileUUID = uuidv4();
				const fileExtension = attachment.fileName.split('.').pop();
				const s3Key = `appointment_attachments/${appointmentId}/${fileUUID}.${fileExtension}`;

				// Upload image attachment
				this.logger.log(
					`Processing image attachment for appointment ${appointmentId}`
				);
				await this.s3Service.uploadBuffer(
					fileBuffer,
					s3Key,
					'image/jpg'
				);

				// Add processed attachment
				processedAttachments.push({
					...attachment,
					fileKey: s3Key
				});

				this.logger.log(
					`Successfully processed attachment: ${attachment.attachementName} for appointment ${appointmentId}`
				);
			} catch (error) {
				this.logger.error('Attachment processing failed', {
					appointmentId,
					attachmentName: attachment.attachementName
				});

				if (attachment.attachementName === 'EMR Document') {
					// For EMR documents, keep the original S3 key even if validation fails
					processedAttachments.push(attachment);
				} else {
					// For other attachments, throw error to handle upstream
					throw new Error(
						`Failed to process ${attachment.attachementName} for appointment ${appointmentId}`
					);
				}
			}
		}

		return processedAttachments;
	}

	private async downloadFile(url: string): Promise<Buffer> {
		try {
			const response = await axios.get(url, {
				responseType: 'arraybuffer',
				timeout: 30000, // 30 second timeout
				maxContentLength: 50 * 1024 * 1024 // 50MB max
			});
			return Buffer.from(response.data);
		} catch (error) {
			throw new Error(`Failed to download file: ${error}`);
		}
	}

	/**
	 * Process invoices with either a PDF URL or an existing S3 key
	 *
	 * @example With PDF URL:
	 * {
	 *   "patientId": "...",
	 *   "date": "...",
	 *   "pdfUrl": "...",
	 *   "clinicId": "...",
	 *   "brandId": "..."
	 * }
	 *
	 * @example With existing S3 key:
	 * {
	 *   "patientId": "...",
	 *   "date": "...",
	 *   "s3Key": "...",
	 *   "clinicId": "...",
	 *   "brandId": "..."
	 * }
	 */
	@Post('process')
	async processInvoices(@Body() invoiceData: InvoiceDataDto[]) {
		this.logger.log('Processing invoices', { count: invoiceData.length });
		const results = [];

		for (const data of invoiceData) {
			try {
				// Check if s3Key is provided directly
				if (!data.s3Key && data.pdfUrl) {
					// If no s3Key is provided but we have a pdfUrl, download and upload
					this.logger.log('Downloading and uploading PDF to S3', {
						pdfUrl: data.pdfUrl
					});

					// Download PDF
					const pdfBuffer = await this.downloadPDF(data.pdfUrl);

					// Upload to S3
					const invoiceUUID = uuidv4();
					const s3Key = `invoice/${invoiceUUID}`;
					await this.s3Service.uploadPdfToS3(pdfBuffer, s3Key);

					// Update invoiceData with S3 key
					data.s3Key = s3Key;
					this.logger.log('PDF uploaded successfully', { s3Key });
				} else if (!data.s3Key) {
					// If neither s3Key nor pdfUrl is provided, we can't proceed
					const error = 'Either s3Key or pdfUrl must be provided';
					this.logger.error(error, { data });
					results.push({ status: 'error', message: error, data });
					continue;
				} else {
					// Log that we're using the provided S3 key
					this.logger.log(`Using provided S3 key: ${data.s3Key}`);
				}

				// Process invoice using DataService
				const result = await this.dataService.processInvoices([data]);
				results.push(...result);
			} catch (error) {
				this.logger.error('Error processing invoice', {
					error,
					patientId: data.patientId,
					pdfUrl: data.pdfUrl,
					s3Key: data.s3Key
				});
				results.push({ status: 'error', message: error, data });
			}
		}

		// Calculate success and error counts
		const successCount = results.filter(r => r.status === 'success').length;
		const failedCount = results.filter(r => r.status === 'error').length;

		this.logger.log('Invoice processing completed', {
			totalProcessed: results.length,
			successful: successCount,
			failed: failedCount
		});

		// Return structured response
		return {
			success: failedCount === 0, // true if no errors
			results,
			summary: {
				total: results.length,
				successful: successCount,
				failed: failedCount
			}
		};
	}

	// @Post('process-diagnostics')
	// async processDiagnostics(@Body() diagnosticsData: DiagnosticsDataDto[]) {
	//   const results = [];

	//   console.log(diagnosticsData)
	//   for (const data of diagnosticsData) {
	//     try {
	//       // Download PDF
	//       console.log(data.pdfUrl)
	//       const pdfBuffer = await this.downloadPDF(data.pdfUrl);
	//       console.log(pdfBuffer)

	//       // Upload to S3
	//       const fileUUID = uuidv4();
	//       const s3Key = `labReports/${fileUUID}/${data.fileName}`;

	//       const res = await this.s3Service.uploadPdfToS3(pdfBuffer, s3Key);

	//       // Create file metadata
	//       const fileData = {
	//         id: uuidv4(),
	//         fileKey: s3Key,
	//         fileName: data.fileName,
	//         fileSize: pdfBuffer.length,
	//         uploadDate: new Date().toISOString()
	//       };

	//       console.log(fileData)
	//       const result = await this.dataService.processDiagnostics({
	//         ...data,
	//         fileData
	//       });

	//       results.push(result);
	//     } catch (error) {
	//       results.push({
	//         status: 'error',
	//         message: error,
	//         data: data
	//       });
	//     }
	//   }

	//   return results;
	// }

	@Post('upload-emr')
	@UseInterceptors(FileInterceptor('file'))
	async uploadEmr(
		@UploadedFile() file: Express.Multer.File,
		@Body('patientId') patientId: string
	) {
		if (!file || !patientId) {
			throw new BadRequestException('File and patientId are required');
		}

		console.log(file, patientId);
		try {
			this.logger.log('Uploading EMR file', {
				patientId,
				fileName: file.originalname
			});

			// Generate unique S3 key
			const fileUUID = uuidv4();
			const s3Key = `appointment_attachments/migrated_data/emr_documents/${patientId}/${fileUUID}.pdf`;
			console.log(s3Key);

			// Upload to S3
			await this.s3Service.uploadBuffer(
				file.buffer,
				s3Key,
				'application/pdf'
			);

			this.logger.log('EMR file uploaded successfully', {
				patientId,
				s3Key
			});

			return {
				status: 'success',
				patientId,
				s3Key
			};
		} catch (error) {
			this.logger.error('Error uploading EMR file', {
				patientId,
				error
			});
			throw error;
		}
	}

	// @Post('appointments/calendar')
	// async migrateAppointments(
	//   @Body() appointments: CreateAppointmentDto[]
	// ) {
	//   try {
	//     this.logger.log('Starting bulk appointment migration', {
	//       appointmentCount: appointments.length
	//     });

	//     const results = [];

	//     for (const appointment of appointments) {
	//       try {
	//         // Get real patient ID using old ID
	//         const patient = await this.dataService.findByOldId(appointment.patientId);
	//         if (!patient) {
	//           results.push({
	//             status: 'error',
	//             oldPatientId: appointment.patientId,
	//             error: 'Patient not found'
	//           });
	//           continue;
	//         }

	//         // Create appointment with real patient ID
	//         const formattedAppointment = {
	//           ...appointment,
	//           patientId: patient.id
	//         };

	//         const createdAppointment = await this.dataService.createAppointment(formattedAppointment);

	//         results.push({
	//           status: 'success',
	//           oldPatientId: appointment.patientId,
	//           newPatientId: patient.id,
	//           appointmentId: createdAppointment.id,
	//           date: appointment.date
	//         });

	//         this.logger.log('Appointment created successfully', {
	//           oldPatientId: appointment.patientId,
	//           newPatientId: patient.id,
	//           appointmentId: createdAppointment.id
	//         });

	//       } catch (error) {
	//         this.logger.error('Error processing appointment', {
	//           oldPatientId: appointment.patientId,
	//           error: error
	//         });

	//         results.push({
	//           status: 'error',
	//           oldPatientId: appointment.patientId,
	//           error: error
	//         });
	//       }
	//     }

	//     return results;

	//   } catch (error) {
	//     this.logger.error('Bulk appointment migration failed', { error });
	//     throw new InternalServerErrorException('Failed to process appointments');
	//   }
	// }
	private async downloadPDF(url: string): Promise<Buffer> {
		const response = await axios.get(url, { responseType: 'arraybuffer' });
		return Buffer.from(response.data);
	}

	//   @Post('appointments-future/calendar')
	//   async migrateAppointmentsFuture(
	//   @Body() appointments: CreateAppointmentDto[]
	// ) {
	//   try {
	//     this.logger.log('Starting bulk appointment migration', {
	//       appointmentCount: appointments.length
	//     });

	//     const results = [];

	//     for (const appointment of appointments) {
	//       try {
	//         // Start transaction
	//         const queryRunner = this.dataSource.createQueryRunner();
	//         await queryRunner.connect();
	//         await queryRunner.startTransaction();

	//         try {
	//           // Get real patient ID using old ID
	//           const patient = await this.dataService.findByOldId(appointment.patientId);
	//           if (!patient) {
	//             results.push({
	//               status: 'error',
	//               oldPatientId: appointment.patientId,
	//               error: 'Patient not found'
	//             });
	//             continue;
	//           }

	//           // Create appointment with real patient ID
	//           const formattedAppointment = {
	//             ...appointment,
	//             patientId: patient.id
	//           };

	//           const createdAppointment = await this.dataService.createAppointment(formattedAppointment);

	//           // Create appointment details
	//           const appointmentDetails = queryRunner.manager.create(AppointmentDetailsEntity, {
	//             appointmentId: createdAppointment?.id
	//           });

	//           await queryRunner.manager.save(AppointmentDetailsEntity, appointmentDetails);

	//           // Commit transaction
	//           await queryRunner.commitTransaction();

	//           results.push({
	//             status: 'success',
	//             oldPatientId: appointment.patientId,
	//             newPatientId: patient.id,
	//             appointmentId: createdAppointment.id,
	//             date: appointment.date,
	//             detailsId: appointmentDetails.id
	//           });

	//           this.logger.log('Appointment and details created successfully', {
	//             oldPatientId: appointment.patientId,
	//             newPatientId: patient.id,
	//             appointmentId: createdAppointment.id,
	//             detailsId: appointmentDetails.id
	//           });

	//         } catch (error) {
	//           // Rollback transaction on error
	//           await queryRunner.rollbackTransaction();
	//           throw error;
	//         } finally {
	//           // Release query runner
	//           await queryRunner.release();
	//         }

	//       } catch (error) {
	//         this.logger.error('Error processing appointment', {
	//           oldPatientId: appointment.patientId,
	//           error
	//         });
	//       }
	//     }

	// const successCount = results.filter(r => r.status === 'success').length;
	// const errorCount = results.filter(r => r.status === 'error').length;

	// this.logger.log('Bulk appointment migration completed', {
	//   total: appointments.length,
	//   successful: successCount,
	//   failed: errorCount
	// });

	//     return results;

	//   } catch (error) {
	//     this.logger.error('Bulk appointment migration failed', {
	//       error
	//     });
	//     throw new InternalServerErrorException('Failed to process appointments');
	//   }
	// }

	@Post('owners/opening-balance/bulk')
	async updateOwnersOpeningBalance(
		@Body() bulkUpdateDto: BulkUpdateOpeningBalanceDto
	) {
		try {
			this.logger.log('Starting bulk opening balance update', {
				ownerCount: bulkUpdateDto.owners.length
			});

			const results = await this.dataService.bulkUpdateOpeningBalance(
				bulkUpdateDto.owners
			);

			const successCount = results.filter(
				r => r.status === 'success'
			).length;
			const failedCount = results.filter(
				r => r.status === 'error'
			).length;

			this.logger.log('Bulk opening balance update completed', {
				totalProcessed: results.length,
				successful: successCount,
				failed: failedCount
			});

			return {
				success: true,
				results,
				summary: {
					total: results.length,
					successful: successCount,
					failed: failedCount
				}
			};
		} catch (error) {
			this.logger.error('Bulk opening balance update failed', {
				error: error instanceof Error ? error.message : 'Unknown error'
			});
			throw new InternalServerErrorException(
				'Failed to update opening balances',
				error instanceof Error ? error.message : undefined
			);
		}
	}

	@Put('patients/:id/microchip')
    async updateMicrochipId(
        @Param('id') id: string,
        @Body() updateData: { microchipId: string }
    ) {
        try {
            this.logger.log('Updating patient microchip ID', { 
                patientId: id, 
                microchipId: updateData.microchipId 
            });

            const updatedPatient = await this.dataService.updateMicrochipId(
                id,
                updateData.microchipId
            );

            this.logger.log('Microchip ID updated successfully', { 
                patientId: id 
            });

            return {
                success: true,
                data: updatedPatient
            };
        } catch (error) {
            this.logger.log('Error updating microchip ID', { 
                error, 
                patientId: id 
            });
            
            throw new HttpException(
                'Failed to update microchip ID',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
