// api/src/availability/availability.controller.ts

import { <PERSON>, <PERSON>, Logger, Param } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { AvailabilityService } from './availability.service';

@Controller('availability')
export class AvailabilityController {
	private logger = new Logger('AvailabilityController');

	constructor(
		@InjectRepository(ClinicUser)
		private clinicUserRepository: Repository<ClinicUser>,
		private availabilityService: AvailabilityService
	) {}

	@Post('initialize-single/:id')
	async initializeSingleUserSlots(@Param('id') id: string) {
		const startDate = new Date();
		const endDate = new Date();
		endDate.setDate(startDate.getDate() + 90);

		try {
			await this.availabilityService.generateSlotsForUser(
				id,
				startDate,
				endDate
			);
			return { success: true };
		} catch (error: any) {
			return {
				success: false,
				error: error?.message || 'Unknown error'
			};
		}
	}

	@Post('initialize-all')
	async initializeAllSlots() {
		this.logger.log(
			'Starting availability slots initialization for all users'
		);

		const clinicUsers = await this.clinicUserRepository.find();

		this.logger.log(`Found ${clinicUsers.length} active clinic users`);

		// Set up date range - today to 90 days ahead
		const startDate = new Date();
		const endDate = new Date();
		endDate.setDate(startDate.getDate() + 90);

		// Process users in batches
		const batchSize = 10;
		const results = {
			total: clinicUsers.length,
			successful: 0,
			failed: 0,
			failedUserIds: [] as string[]
		};

		for (let i = 0; i < clinicUsers.length; i += batchSize) {
			const batch = clinicUsers.slice(i, i + batchSize);

			await Promise.all(
				batch.map(async user => {
					try {
						await this.availabilityService.generateSlotsForUser(
							user.id,
							startDate,
							endDate
						);
						results.successful++;
					} catch (error: any) {
						// Type the error as any to access stack
						results.failed++;
						results.failedUserIds.push(user.id);
						this.logger.error(
							`Failed to generate slots for user ${user.id}`,
							error?.stack || 'No stack trace'
						);
					}
				})
			);
		}

		this.logger.log('Availability slots initialization completed');
		return {
			message: 'Availability slots initialization completed',
			results
		};
	}
}
