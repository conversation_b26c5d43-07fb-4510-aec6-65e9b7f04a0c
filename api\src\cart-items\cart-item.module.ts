import { Module, forwardRef } from '@nestjs/common';
import { CartItemService } from './cart-item.service';
import { CartItemController } from './cart-item.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CartItemEntity } from './entities/cart-item.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { RoleModule } from '../roles/role.module';
import { CartsModule } from '../carts/carts.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([CartItemEntity, CartEntity]),
		RoleModule,
		forwardRef(() => CartsModule)
	],
	controllers: [CartItemController],
	providers: [CartItemService],
	exports: [CartItemService]
})
export class CartItemModule {}
