"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOtpsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_otp_entity_1 = require("./entities/user-otp.entity");
const users_service_1 = require("../users/users.service");
const role_service_1 = require("../roles/role.service");
const role_enum_1 = require("../roles/role.enum");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const jwt_1 = require("@nestjs/jwt");
const constants_1 = require("../utils/constants");
const get_login_url_1 = require("../utils/common/get-login-url");
let UserOtpsService = class UserOtpsService {
    constructor(userOtpsRepository, usersServices, roleService, jwtService, mailService) {
        this.userOtpsRepository = userOtpsRepository;
        this.usersServices = usersServices;
        this.roleService = roleService;
        this.jwtService = jwtService;
        this.mailService = mailService;
    }
    generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }
    async createOtp(generateOtpDto) {
        const { email } = generateOtpDto;
        const user = await this.usersServices.findOneByEmail(email);
        if (!user) {
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        }
        const role = await this.roleService.findById(user.roleId);
        if (role.name != role_enum_1.Role.SUPER_ADMIN) {
            throw new common_1.HttpException('Only SuperAdmins can login using Email', common_1.HttpStatus.UNAUTHORIZED);
        }
        const otpCode = this.generateOtp();
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 5);
        const createUserOtpDto = {
            userId: user.id,
            otp: otpCode,
            otpExpiresAt: expiresAt
        };
        const userOtp = this.userOtpsRepository.create(createUserOtpDto);
        await this.userOtpsRepository.save(userOtp);
        console.log(`Generated OTP for ${email}: ${otpCode}`);
        if ((0, get_login_url_1.isProduction)() && (user === null || user === void 0 ? void 0 : user.email)) {
            this.mailService.sendMail({
                body: `Dear ${user.firstName} ${user.lastName}, 
				Your OTP for logging into the SuperAdmin platform is: ${otpCode}
				
				Please note that this OTP will expire in 5 minutes.
		
				If you did not request this OTP, please contact support immediately.`,
                subject: 'Your SuperAdmin Login OTP',
                toMailAddress: user.email
            });
        }
        else if (!(0, get_login_url_1.isProduction)()) {
            this.mailService.sendMail({
                body: `Dear ${user.firstName} ${user.lastName}, 
				Your OTP for logging into the SuperAdmin platform is: ${otpCode}
				
				Please note that this OTP will expire in 5 minutes.
		
				If you did not request this OTP, please contact support immediately.`,
                subject: 'Your SuperAdmin Login OTP',
                toMailAddress: constants_1.DEV_SES_EMAIL //user.email
            });
        }
        return { statusCode: 201, message: 'OTP has been sent to your email.' };
    }
    async validateOtp(validateOtpDto) {
        const { email, otp } = validateOtpDto;
        const user = await this.usersServices.findOneByEmail(email);
        if (!user) {
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        }
        const role = await this.roleService.findById(user.roleId);
        if (role.name !== role_enum_1.Role.SUPER_ADMIN) {
            throw new common_1.HttpException('Only SuperAdmins can login using Email', common_1.HttpStatus.UNAUTHORIZED);
        }
        const userOtp = await this.userOtpsRepository.findOne({
            where: {
                userId: user.id,
                otp
            },
            order: { otpExpiresAt: 'DESC' }
        });
        if (!userOtp) {
            throw new common_1.HttpException('Invalid OTP', common_1.HttpStatus.UNAUTHORIZED);
        }
        const currentTime = new Date();
        if (currentTime > userOtp.otpExpiresAt) {
            await this.userOtpsRepository.delete(userOtp.id);
            throw new common_1.HttpException('OTP has expired', common_1.HttpStatus.UNAUTHORIZED);
        }
        const roleName = role.name;
        const payload = {
            sub: user.id,
            email: user.email,
            role: role.name
        };
        const token = this.jwtService.sign(payload);
        return { token, roleName };
    }
};
exports.UserOtpsService = UserOtpsService;
exports.UserOtpsService = UserOtpsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_otp_entity_1.UserOtp)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        users_service_1.UsersService,
        role_service_1.RoleService,
        jwt_1.JwtService,
        send_mail_service_1.SESMailService])
], UserOtpsService);
//# sourceMappingURL=user-otps.service.js.map