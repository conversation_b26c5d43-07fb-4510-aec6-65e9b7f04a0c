"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const user_dto_1 = require("./dto/user.dto");
const calendar_working_hours_dto_1 = require("./dto/calendar-working-hours.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const exception_dto_1 = require("./dto/exception.dto");
const role_service_1 = require("../roles/role.service");
let UsersController = class UsersController {
    constructor(usersService, logger, roleService) {
        this.usersService = usersService;
        this.logger = logger;
        this.roleService = roleService;
    }
    async createUser(createUserDto, req, clinicId, brandId) {
        try {
            this.logger.log('Creating new user', {
                dto: createUserDto,
                clinicId
            });
            const user = await this.usersService.createUser(createUserDto, clinicId, brandId, req.user);
            this.logger.log('User created or associated successfully', {
                userId: user.id,
                clinicId
            });
            return user;
        }
        catch (error) {
            this.logger.error('Error creating user', { error, clinicId });
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to create user', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async searchUsersAcrossClinics(brandId, searchTerm, excludeClinicId) {
        return this.usersService.findUsersAcrossClinics(brandId, excludeClinicId, searchTerm);
    }
    async getClinicUsersAvailability(clinicId, role, search, orderBy, date, startTime, endTime) {
        return this.usersService.findClinicUsersAvailability(clinicId, role, search, orderBy, date, startTime, endTime);
    }
    async getClinicDoctors(clinicId) {
        return this.usersService.getClinicDoctors(clinicId);
    }
    async getClinicUsers(clinicId, role, search, page, limit, orderBy) {
        return this.usersService.findClinicUsers(clinicId, page, limit, role, search, orderBy);
    }
    async updateUserStatus(id, isActive
    // @Req() req: RequestWithUser
    ) {
        return this.usersService.updateUserStatus(id, isActive);
    }
    async updateClinicUser(id, updateUserDto, req) {
        return this.usersService.updateClinicUser(id, updateUserDto, req.user.userId);
    }
    async addUserToClinic(userId, clinicId, brandId, isPrimary = false) {
        return this.usersService.addUserToClinic(userId, clinicId, brandId, isPrimary);
    }
    async getClinicUserData(id) {
        const clinicUserData = await this.usersService.getClinicUserData(id);
        if (!clinicUserData) {
            throw new common_1.NotFoundException('Clinic user not found');
        }
        return clinicUserData;
    }
    async remove(id) {
        try {
            this.logger.log('Removing user', { userId: id });
            await this.usersService.remove(id);
            this.logger.log('User removed successfully', { userId: id });
            return { message: 'User removed successfully' };
        }
        catch (error) {
            this.logger.error('Error removing user', { error, userId: id });
            throw new common_1.HttpException('Failed to remove user', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getCalendarWorkingHours(date, clinicId) {
        try {
            this.logger.log('Fetching calendar working hours', {
                date,
                clinicId
            });
            const workingHours = await this.usersService.getCalendarWorkingHours(date, clinicId);
            this.logger.log('Calendar working hours fetched successfully', {
                date,
                clinicId,
                doctorsCount: workingHours.doctors.length
            });
            return workingHours;
        }
        catch (error) {
            this.logger.error('Error fetching calendar working hours', {
                error,
                date,
                clinicId
            });
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch calendar working hours', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findOne(id) {
        try {
            this.logger.log('Fetching user by ID', { userId: id });
            const user = await this.usersService.findOne(id);
            // Include role information in the response
            const userWithRole = (user === null || user === void 0 ? void 0 : user.roleId) ? {
                ...user,
                role: await this.roleService.findOneById(user.roleId)
            } : user;
            this.logger.log('User fetched successfully', { userId: id });
            return userWithRole;
        }
        catch (error) {
            this.logger.error('Error fetching user', { error, userId: id });
            throw new common_1.HttpException('Failed to fetch user', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async completeProfile(userId, updateProfileDto) {
        console.log('updateProfileDto-1');
        try {
            const updatedUser = await this.usersService.updateStaffProfile(userId, updateProfileDto);
            return {
                message: 'Profile completed successfully',
                user: updatedUser
            };
        }
        catch (error) {
            console.log(error);
            if (error instanceof common_1.UnauthorizedException) {
                throw new common_1.UnauthorizedException('Not able to update staff user');
            }
            throw error;
        }
    }
    async updateWorkingHours(userId, updateWorkingHoursDto) {
        console.log(updateWorkingHoursDto);
        return this.usersService.updateWorkingHours(userId, updateWorkingHoursDto);
    }
    async getUserClinics(userId) {
        return this.usersService.getUserClinics(userId);
    }
    async createException(createExceptionDto, req) {
        try {
            this.logger.log('Creating availability exception', {
                dto: createExceptionDto
            });
            const exception = await this.usersService.createException(createExceptionDto, req.user.userId);
            this.logger.log('Availability exception created successfully', {
                exceptionId: exception.id
            });
            return exception;
        }
        catch (error) {
            this.logger.error('Error creating availability exception', {
                error
            });
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.ConflictException ||
                error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to create availability exception', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getExceptions(clinicUserId, includeHistory) {
        this.logger.log(`Fetching availability exceptions for clinic user: ${clinicUserId}`);
        try {
            // Convert string query params to boolean
            const includeHistoryBool = includeHistory === 'true' || includeHistory === true;
            return await this.usersService.getExceptions(clinicUserId, includeHistoryBool);
        }
        catch (error) {
            this.logger.error(`Failed to get availability exceptions for clinic user: ${clinicUserId}`, error);
            throw new common_1.HttpException(error instanceof Error
                ? error.message
                : 'Failed to get availability exceptions', error instanceof common_1.HttpException
                ? error.getStatus()
                : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getExceptionById(id) {
        try {
            this.logger.log('Fetching availability exception by ID', { id });
            const exception = await this.usersService.getExceptionById(id);
            this.logger.log('Availability exception fetched successfully', {
                id
            });
            return exception;
        }
        catch (error) {
            this.logger.error('Error fetching availability exception', {
                error,
                id
            });
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch availability exception', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateException(id, updateExceptionDto, req) {
        try {
            this.logger.log('Updating availability exception', {
                id,
                dto: updateExceptionDto
            });
            const exception = await this.usersService.updateException(id, updateExceptionDto, req.user.userId);
            this.logger.log('Availability exception updated successfully', {
                id
            });
            return exception;
        }
        catch (error) {
            this.logger.error('Error updating availability exception', {
                error,
                id,
                dto: updateExceptionDto
            });
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.ConflictException ||
                error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to update availability exception', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteException(id) {
        try {
            this.logger.log('Deleting availability exception', { id });
            await this.usersService.deleteException(id);
            this.logger.log('Availability exception deleted successfully', {
                id
            });
            return { message: 'Exception deleted successfully' };
        }
        catch (error) {
            this.logger.error('Error deleting availability exception', {
                error,
                id
            });
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.HttpException(error instanceof Error
                ? error.message
                : 'Failed to delete availability exception', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new user or clinic user' }),
    (0, swagger_1.ApiBody)({ type: user_dto_1.CreateUserDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The user has been successfully created or associated.',
        type: user_dto_1.CreateUserDto
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', required: false, type: String }),
    (0, track_method_decorator_1.TrackMethod)('createUser-users'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('clinicId')),
    __param(3, (0, common_1.Query)('brandId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_dto_1.CreateUserDto, Object, String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createUser", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Search users across clinics under the same brand'
    }),
    (0, swagger_1.ApiQuery)({ name: 'brandId', required: true, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'searchTerm', required: true, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'excludeClinicId', required: false, type: String }),
    (0, track_method_decorator_1.TrackMethod)('searchUsersAcrossClinics-users'),
    __param(0, (0, common_1.Query)('brandId')),
    __param(1, (0, common_1.Query)('searchTerm')),
    __param(2, (0, common_1.Query)('excludeClinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "searchUsersAcrossClinics", null);
__decorate([
    (0, common_1.Get)('clinic/availability/:clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all users availability for a clinic by role and time slot'
    }),
    (0, swagger_1.ApiParam)({ name: 'clinicId', type: 'string' }),
    (0, swagger_1.ApiQuery)({ name: 'role', type: 'string', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'search', type: 'string', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'orderBy', type: 'string', required: false }),
    (0, swagger_1.ApiQuery)({
        name: 'date',
        type: 'string',
        required: false,
        description: 'Date in YYYY-MM-DD format'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startTime',
        type: 'string',
        required: false,
        description: 'Start time in HH:mm format'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endTime',
        type: 'string',
        required: false,
        description: 'End time in HH:mm format'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return all users availability for the clinic by role, time slot and search criteria.',
        type: [user_dto_1.CreateUserDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getClinicUsersAvailability-users'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Query)('role')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('orderBy')),
    __param(4, (0, common_1.Query)('date')),
    __param(5, (0, common_1.Query)('startTime')),
    __param(6, (0, common_1.Query)('endTime')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getClinicUsersAvailability", null);
__decorate([
    (0, common_1.Get)('clinic/doctors/:clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all admin and doctor users for a clinic'
    }),
    (0, swagger_1.ApiParam)({ name: 'clinicId', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return all admin and doctor users for the clinic.',
        type: [user_dto_1.CreateUserDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getClinicDoctors-users'),
    __param(0, (0, common_1.Param)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getClinicDoctors", null);
__decorate([
    (0, common_1.Get)('clinic/:clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all users for a clinic by role and search with pagination'
    }),
    (0, swagger_1.ApiParam)({ name: 'clinicId', type: 'string' }),
    (0, swagger_1.ApiQuery)({ name: 'role', type: 'string', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'search', type: 'string', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'page', type: 'number', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'limit', type: 'number', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'orderBy', type: 'string', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return all users for the clinic by role and search criteria with pagination.',
        type: [user_dto_1.CreateUserDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getClinicUsers-users'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Query)('role')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('page')),
    __param(4, (0, common_1.Query)('limit')),
    __param(5, (0, common_1.Query)('orderBy')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Number, Number, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getClinicUsers", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('updateUserStatus-users'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('isActive')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateUserStatus", null);
__decorate([
    (0, common_1.Put)('clinic/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Update a clinic user' }),
    (0, track_method_decorator_1.TrackMethod)('updateClinicUser-users'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_dto_1.UpdateUserDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateClinicUser", null);
__decorate([
    (0, common_1.Post)(':userId/add-to-clinic/:clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('addUserToClinic-users'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('clinicId')),
    __param(2, (0, common_1.Param)('brandId')),
    __param(3, (0, common_1.Body)('isPrimary')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Boolean]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "addUserToClinic", null);
__decorate([
    (0, common_1.Get)('clinic-user/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get clinic user data including working hours' }),
    (0, swagger_1.ApiParam)({
        name: 'userId',
        required: true,
        description: 'Clinic user ID (from clinic_users table)'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Clinic user data retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Clinic user not found' }),
    (0, track_method_decorator_1.TrackMethod)('getClinicUserData-users'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getClinicUserData", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a user' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The user has been successfully deleted.'
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found.' }),
    (0, track_method_decorator_1.TrackMethod)('remove-users'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('calendar-working-hours'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all doctors working hours for a specific date for the calendar view'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'date',
        required: true,
        type: String,
        description: 'Date in YYYY-MM-DD format'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'clinicId',
        required: true,
        type: String
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns all doctors working hours for the specified date',
        type: calendar_working_hours_dto_1.CalendarWorkingHoursResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request.' }),
    (0, track_method_decorator_1.TrackMethod)('getCalendarWorkingHours-users'),
    __param(0, (0, common_1.Query)('date')),
    __param(1, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getCalendarWorkingHours", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get user by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the user with the specified ID.'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found.' }),
    (0, track_method_decorator_1.TrackMethod)('findOne-users'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)('complete-profile/:userId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Complete user profile using userId' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profile completed successfully.'
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid PIN.' }),
    (0, track_method_decorator_1.TrackMethod)('completeProfile-users'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_dto_1.UpdateProfileDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "completeProfile", null);
__decorate([
    (0, common_1.Put)('working-hours/:userId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, swagger_1.ApiOperation)({ summary: 'Add working hours for a staff member' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profile completed successfully.'
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid PIN.' }),
    (0, track_method_decorator_1.TrackMethod)('updateWorkingHours-users'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, user_dto_1.UpdateWorkingHoursDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateWorkingHours", null);
__decorate([
    (0, common_1.Get)('clinics/:userId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN, role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get all clinics for a user' }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: 'string' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns all clinics associated with the user'
    }),
    (0, track_method_decorator_1.TrackMethod)('getUserClinics-users'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserClinics", null);
__decorate([
    (0, common_1.Post)('exceptions'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new availability exception' }),
    (0, swagger_1.ApiBody)({ type: exception_dto_1.CreateExceptionDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The exception has been successfully created.'
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request.' }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict with existing exception.'
    }),
    (0, track_method_decorator_1.TrackMethod)('createException-users'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [exception_dto_1.CreateExceptionDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createException", null);
__decorate([
    (0, common_1.Get)('exceptions/:clinicUserId'),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Successfully retrieved all availability exceptions for the clinic user'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'includeHistory',
        required: false,
        type: Boolean,
        description: 'Whether to include past exceptions'
    }),
    (0, track_method_decorator_1.TrackMethod)('getExceptions-users'),
    __param(0, (0, common_1.Param)('clinicUserId')),
    __param(1, (0, common_1.Query)('includeHistory')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getExceptions", null);
__decorate([
    (0, common_1.Get)('exceptions/detail/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get an availability exception by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The exception ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the requested availability exception.'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exception not found.' }),
    (0, track_method_decorator_1.TrackMethod)('getExceptionById-users'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getExceptionById", null);
__decorate([
    (0, common_1.Put)('exceptions/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Update an availability exception' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The exception ID' }),
    (0, swagger_1.ApiBody)({ type: exception_dto_1.UpdateExceptionDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The exception has been successfully updated.'
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exception not found.' }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict with existing exception.'
    }),
    (0, track_method_decorator_1.TrackMethod)('updateException-users'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, exception_dto_1.UpdateExceptionDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateException", null);
__decorate([
    (0, common_1.Delete)('exceptions/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an availability exception' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'The exception ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The exception has been successfully deleted.'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Exception not found.' }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict: Cannot delete an exception that has already ended or has appointments.'
    }),
    (0, track_method_decorator_1.TrackMethod)('deleteException-users'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "deleteException", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Users'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        winston_logger_service_1.WinstonLogger,
        role_service_1.RoleService])
], UsersController);
//# sourceMappingURL=users.controller.js.map