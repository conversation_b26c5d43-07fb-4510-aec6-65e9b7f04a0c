import {
	<PERSON>umn,
	CreateDateColumn,
	Entity,
	<PERSON>in<PERSON><PERSON><PERSON><PERSON>,
	ManyToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { NumericTransformer } from '../../utils/common/numeric-transformer';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';
import { CreditTransactionType } from '../enums/enum-credit-transaction-type';
import { DerivedCreditTransactionType } from '../enums/enum-derived-credit-transaction-type';

@Entity('credit_transactions')
export class CreditTransactionEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'owner_id' })
	ownerId!: string;

	@ManyToOne(() => OwnerBrand)
	@JoinColumn({ name: 'owner_id' })
	owner!: OwnerBrand;

	@Column({
		name: 'amount',
		type: 'decimal',
		precision: 10,
		scale: 2,
		transformer: new NumericTransformer()
	})
	amount!: number;

	@Column({
		type: 'enum',
		enum: CreditTransactionType,
		name: 'transaction_type'
	})
	transactionType!: CreditTransactionType;

	@Column({
		type: 'enum',
		enum: DerivedCreditTransactionType,
		name: 'derived_transaction_type',
		nullable: true,
		default: DerivedCreditTransactionType.UNKNOWN
	})
	derivedTransactionType?: DerivedCreditTransactionType;

	@Column({ type: 'text', name: 'description', nullable: true })
	description?: string;

	@Column({ type: 'uuid', name: 'invoice_id', nullable: true })
	invoiceId?: string;

	@Column({ type: 'uuid', name: 'payment_detail_id', nullable: true })
	paymentDetailId?: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ type: 'jsonb', name: 'metadata', nullable: true })
	metadata?: object;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { name: 'created_by', nullable: true })
	createdBy!: string;

	@Column('uuid', { name: 'updated_by', nullable: true })
	updatedBy?: string;

	@ManyToOne(() => InvoiceEntity)
	@JoinColumn({ name: 'invoice_id' })
	invoice?: InvoiceEntity;

	@ManyToOne(() => PaymentDetailsEntity)
	@JoinColumn({ name: 'payment_detail_id' })
	paymentDetail?: PaymentDetailsEntity;
}
