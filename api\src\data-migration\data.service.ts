import {
	Injectable,
	BadRequestException,
	InternalServerErrorException,
	NotFoundException,
	ConflictException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, DeepPartial } from 'typeorm';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { Patient } from '../patients/entities/patient.entity';
import {
	CreateAppointmentDetailsDto,
	CreateAppointmentDto,
	CreateOwnerDto,
	CreatePatientDto,
	CreatePatientOwnerDto,
	InvoiceDataDto,
	MigrateAppointmentDto,
	UpdateOwnerOpeningBalanceDto
} from './dto/data.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { EnumInvoiceType } from '../invoice/enums/enum-invoice-types';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { EnumPaymentType } from '../invoice/enums/enum-payment-types';
import { EnumAmountType } from '../payment-details/enums/enum-credit-types';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';

interface LabReportFile {
	id: string;
	s3Url: string;
	fileKey: string;
	fileName: string;
	fileSize: number;
	uploadDate: string;
}

interface LabReportItem {
	files: LabReportFile[];
	label: string;
	value: string;
	labReportId?: string;
	cartId?: string;
	chargeablePrice?: number;
}

interface AppointmentDetails {
	details: {
		objective: {
			labReports: LabReportItem[];
			[key: string]: any;
		};
		[key: string]: any;
	};
	appointmentId: string;
}

@Injectable()
export class DataService {
	constructor(
		@InjectRepository(GlobalOwner)
		private globalOwnerRepository: Repository<GlobalOwner>,
		@InjectRepository(OwnerBrand)
		private ownerBrandRepository: Repository<OwnerBrand>,
		@InjectRepository(Patient)
		private patientsRepository: Repository<Patient>,
		@InjectRepository(PatientOwner)
		private patientOwnersRepository: Repository<PatientOwner>,
		@InjectRepository(AppointmentEntity)
		private appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentDetailsEntity)
		private appointmentDetailsRepository: Repository<AppointmentDetailsEntity>,
		@InjectRepository(AppointmentDoctorsEntity)
		private appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>,
		@InjectRepository(CartEntity)
		private cartRepository: Repository<CartEntity>,
		@InjectRepository(InvoiceEntity)
		private invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(PaymentDetailsEntity)
		private paymentDetailsRepository: Repository<PaymentDetailsEntity>,
		@InjectRepository(LabReport)
		private labReportRepository: Repository<LabReport>,
		@InjectRepository(ClinicLabReport)
		private clinicLabReportRepository: Repository<ClinicLabReport>,
		private dataSource: DataSource,
		private readonly logger: WinstonLogger
	) {}

	getHello(): string {
		return 'Hello World!';
	}
	private async findOwnerByClientId(
		clientId: string
	): Promise<OwnerBrand | null> {
		return this.ownerBrandRepository
			.createQueryBuilder('ownerBrand')
			.where("ownerBrand.dummy_data->>'clientId' = :clientId", {
				clientId
			})
			.getOne();
	}

	private async findExistingPatientOwner(
		patientId: string,
		ownerId: string
	): Promise<PatientOwner | null> {
		return this.patientOwnersRepository.findOne({
			where: {
				patientId,
				globalOwnerId: ownerId
			}
		});
	}

	async createOwner(createOwnerDto: CreateOwnerDto): Promise<OwnerBrand> {
		const queryRunner = this.dataSource.createQueryRunner();

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Processing owner creation/update', {
				dto: createOwnerDto
			});

			// Check if owner already exists using clientId from dummyData
			if (createOwnerDto.dummyData?.clientId) {
				const existingOwner = await this.findOwnerByClientId(
					createOwnerDto.dummyData.clientId
				);

				if (existingOwner) {
					if (existingOwner.dummyData?.isDataImported) {
						this.logger.log(
							'Owner already exists and data imported, skipping',
							{
								clientId: createOwnerDto.dummyData.clientId,
								ownerId: existingOwner.id
							}
						);
						return existingOwner;
					}
				}
			}

			// Create new global owner
			const globalOwner = this.globalOwnerRepository.create({
				phoneNumber: createOwnerDto.phoneNumber,
				countryCode: createOwnerDto.countryCode
			});

			await queryRunner.manager.save(globalOwner);

			// Create new owner brand
			const ownerBrand = this.ownerBrandRepository.create({
				globalOwnerId: globalOwner.id,
				brandId: createOwnerDto.brandId,
				firstName: createOwnerDto.firstName,
				lastName: createOwnerDto.lastName,
				email: createOwnerDto.email,
				address: createOwnerDto.address,
				ownerBalance: createOwnerDto.ownerBalance || 0,
				openingBalance: createOwnerDto.openingBalance || 0,
				dummyData: {
					...createOwnerDto.dummyData,
					isDataImported: true
				}
			});

			await queryRunner.manager.save(ownerBrand);
			await queryRunner.commitTransaction();

			this.logger.log('Owner created successfully', {
				globalOwnerId: globalOwner.id,
				ownerBrandId: ownerBrand.id,
				clientId: createOwnerDto.dummyData?.clientId
			});
			return ownerBrand;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Failure in owner operation', error);

			if (error instanceof ConflictException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to process owner operation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async createPatient(createPatientDto: CreatePatientDto): Promise<Patient> {
		const queryRunner = this.dataSource.createQueryRunner();

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Processing patient creation/update', {
				dto: createPatientDto
			});

			// Check for existing patient using old ID
			if (createPatientDto.dummyData?.patientId) {
				try {
					const existingPatient = await this.findByOldId(
						createPatientDto.dummyData.patientId,
						createPatientDto.clinicId
					);

					// Handle existing patient scenarios
					if (existingPatient) {
						// Case 1: Patient exists and balance already updated
						if (existingPatient.dummyData?.isBalanceUpdated) {
							this.logger.log(
								'Patient exists with updated balance, skipping balance update',
								{
									oldPatientId:
										createPatientDto.dummyData.patientId,
									patientId: existingPatient.id
								}
							);
							return existingPatient;
						}

						// Case 2: Patient exists but balance needs updating
						const updatedPatient = {
							...existingPatient,
							balance: createPatientDto.balance,
							dummyData: {
								...existingPatient.dummyData,
								isBalanceUpdated: true
							}
						};

						const savedPatient = await queryRunner.manager.save(
							Patient,
							updatedPatient
						);
						await queryRunner.commitTransaction();

						this.logger.log(
							'Patient balance updated successfully',
							{
								patientId: savedPatient.id,
								oldPatientId:
									createPatientDto.dummyData.patientId
							}
						);
						return savedPatient;
					}
				} catch (error) {
					if (!(error instanceof NotFoundException)) {
						throw error;
					}
				}
			}

			// Create new patient
			const patient = this.patientsRepository.create({
				...createPatientDto,
				dummyData: {
					...createPatientDto.dummyData,
					isDataImported: true,
					isBalanceUpdated: true
				}
			});

			await queryRunner.manager.save(patient);
			await queryRunner.commitTransaction();

			this.logger.log('New patient created successfully', {
				patientId: patient.id,
				oldPatientId: createPatientDto.dummyData?.patientId
			});
			return patient;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Failure in patient operation', error);

			if (error instanceof ConflictException) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to process patient operation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async createPatientOwner(
		createPatientOwnerDto: CreatePatientOwnerDto
	): Promise<PatientOwner> {
		const queryRunner = this.dataSource.createQueryRunner();

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Processing patient-owner relationship', {
				patientId: createPatientOwnerDto.patientId,
				ownerId: createPatientOwnerDto.ownerId
			});

			// Check for existing relationship
			const existingRelationship = await this.findExistingPatientOwner(
				createPatientOwnerDto.patientId,
				createPatientOwnerDto.ownerId
			);

			if (existingRelationship) {
				this.logger.log(
					'Patient-owner relationship already exists, returning existing',
					{
						patientId: existingRelationship.patientId,
						ownerId: existingRelationship.globalOwnerId,
						relationshipId: existingRelationship.id
					}
				);

				await queryRunner.commitTransaction();
				return existingRelationship;
			}

			// Create new relationship if doesn't exist
			const patientOwner = this.patientOwnersRepository.create({
				...createPatientOwnerDto
			});

			const savedRelationship =
				await queryRunner.manager.save(patientOwner);

			await queryRunner.commitTransaction();

			this.logger.log(
				'New patient-owner relationship created successfully',
				{
					patientId: savedRelationship.patientId,
					ownerId: savedRelationship.globalOwnerId,
					relationshipId: savedRelationship.id
				}
			);

			return savedRelationship;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error(
				'Failure in patient-owner relationship operation',
				{
					error,
					patientId: createPatientOwnerDto.patientId,
					ownerId: createPatientOwnerDto.ownerId
				}
			);

			throw new InternalServerErrorException(
				'Failed to process patient-owner relationship operation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	private async findExistingAppointment(
		patientId: string,
		date: Date
	): Promise<AppointmentEntity | null> {
		return this.appointmentRepository.findOne({
			where: {
				patientId,
				date: date
			},
			relations: ['appointmentDoctors']
		});
	}

	private async findExistingAppointmentDetails(
		appointmentId: string
	): Promise<AppointmentDetailsEntity | null> {
		return this.appointmentDetailsRepository.findOne({
			where: { appointmentId }
		});
	}

	async createAppointment(
		createAppointmentDto: CreateAppointmentDto
	): Promise<AppointmentEntity> {
		const queryRunner = this.dataSource.createQueryRunner();

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Processing appointment creation/update', {
				patientId: createAppointmentDto.patientId,
				date: createAppointmentDto.date
			});

			const appointmentDate = new Date(createAppointmentDto.date);

			// Check for existing appointment
			const existingAppointment = await this.findExistingAppointment(
				createAppointmentDto.patientId,
				appointmentDate
			);

			if (existingAppointment) {
				this.logger.log('Appointment already exists for this date', {
					appointmentId: existingAppointment.id,
					patientId: existingAppointment.patientId,
					date: existingAppointment.date
				});

				await queryRunner.commitTransaction();
				return existingAppointment;
			}

			// Create new appointment if doesn't exist
			const appointment = this.appointmentRepository.create({
				...createAppointmentDto,
				date: appointmentDate
			} as unknown as DeepPartial<AppointmentEntity>);

			const createdAppointment =
				await queryRunner.manager.save(appointment);

			// Create doctor relationships
			const doctorPromises = createAppointmentDto.doctorIds.map(
				async doctorId => {
					const appointmentDoctor =
						this.appointmentDoctorsRepository.create({
							appointmentId: createdAppointment.id,
							doctorId: doctorId,
							primary: true
						});
					return queryRunner.manager.save(appointmentDoctor);
				}
			);

			await Promise.all(doctorPromises);
			await queryRunner.commitTransaction();

			this.logger.log('New appointment created successfully', {
				appointmentId: createdAppointment.id,
				patientId: createdAppointment.patientId,
				date: createdAppointment.date
			});

			return createdAppointment;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Failure in appointment operation', {
				error,
				patientId: createAppointmentDto.patientId,
				date: createAppointmentDto.date
			});

			throw new InternalServerErrorException(
				'Failed to process appointment operation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async createAppointmentDetails(
		createAppointmentDetailsDto: CreateAppointmentDetailsDto
	): Promise<AppointmentDetailsEntity> {
		const queryRunner = this.dataSource.createQueryRunner();

		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			this.logger.log('Processing appointment details', {
				appointmentId: createAppointmentDetailsDto.appointmentId
			});

			// Check for existing appointment details
			const existingDetails = await this.findExistingAppointmentDetails(
				createAppointmentDetailsDto.appointmentId
			);

			if (existingDetails) {
				// Optionally merge new details with existing ones if needed
				const mergedDetails = {
					...existingDetails.details,
					...createAppointmentDetailsDto.details
				};

				existingDetails.details = mergedDetails;

				const updatedDetails =
					await queryRunner.manager.save(existingDetails);

				this.logger.log('Appointment details updated successfully', {
					appointmentId: updatedDetails.appointmentId
				});

				await queryRunner.commitTransaction();
				return updatedDetails;
			}

			// Create new appointment details if don't exist
			const newAppointmentDetails =
				this.appointmentDetailsRepository.create({
					appointmentId: createAppointmentDetailsDto.appointmentId,
					details: createAppointmentDetailsDto.details
				});

			const savedDetails = await queryRunner.manager.save(
				newAppointmentDetails
			);

			await queryRunner.commitTransaction();

			this.logger.log('New appointment details created successfully', {
				appointmentId: savedDetails.appointmentId
			});

			return savedDetails;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			this.logger.error('Failure in appointment details operation', {
				error,
				appointmentId: createAppointmentDetailsDto.appointmentId
			});

			throw new InternalServerErrorException(
				'Failed to process appointment details operation'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async findByOldId(
		oldPatientId: string,
		clinicId: string
	): Promise<Patient> {
		const patient = await this.patientsRepository
			.createQueryBuilder('patient')
			.where("patient.dummy_data->>'patientId' = :oldPatientId", {
				oldPatientId
			})
			.andWhere('patient.clinicId = :clinicId', { clinicId })
			.getOne();

		if (!patient) {
			throw new NotFoundException(
				`Patient with old ID ${oldPatientId} not found`
			);
		}

		return patient;
	}

	async findByPatientAndDate(
		patientId: string,
		date: string
	): Promise<AppointmentEntity> {
		const appointment = await this.appointmentRepository
			.createQueryBuilder('appointment')
			.where('appointment.patientId = :patientId', { patientId })
			.andWhere('DATE(appointment.date) = :date', { date })
			.getOne();

		if (!appointment) {
			throw new NotFoundException(
				`Appointment for patient ${patientId} on ${date} not found`
			);
		}

		return appointment;
	}

	async createCart(appointmentId: string): Promise<CartEntity> {
		const cart = this.cartRepository.create({ appointmentId });
		return this.cartRepository.save(cart);
	}

	async createInvoiceAndPaymentDetail(invoiceData: {
		cartId: string;
		patientId: string;
		ownerId: string;
		clinicId: string;
		brandId: string;
		s3Key: string;
		date: string;
		metadata?: Record<string, any>;
	}): Promise<{
		invoice: InvoiceEntity;
		paymentDetail: PaymentDetailsEntity;
	}> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		try {
			const appointmentDate = new Date(invoiceData.date);
			if (isNaN(appointmentDate.getTime())) {
				throw new BadRequestException('Invalid date format');
			}
			// Create invoice
			const invoice = new InvoiceEntity();
			invoice.cartId = invoiceData.cartId;
			invoice.patientId = invoiceData.patientId;
			invoice.ownerId = invoiceData.ownerId;
			invoice.clinicId = invoiceData.clinicId;
			invoice.brandId = invoiceData.brandId;
			invoice.discount = 0;
			invoice.totalPrice = 0;
			invoice.totalDiscount = 0;
			invoice.priceAfterDiscount = 0;
			invoice.totalTax = 0;
			invoice.totalCredit = 0;
			invoice.amountPayable = 0;
			invoice.amountPaid = 0;
			invoice.paymentMode = 'Cash';
			invoice.invoiceType = EnumInvoiceType.Invoice;
			invoice.details = [];
			invoice.createdAt = appointmentDate;
			invoice.fileUrl = { invoiceFileKey: invoiceData.s3Key };
			invoice.metadata = invoiceData.metadata || {};

			const savedInvoice = await queryRunner.manager.save(invoice);

			// console.log('savedInvoice', savedInvoice);

			// Create payment detail
			const paymentDetail = new PaymentDetailsEntity();
			paymentDetail.patientId = invoiceData.patientId;
			paymentDetail.ownerId = invoiceData.ownerId;
			paymentDetail.clinicId = invoiceData.clinicId;
			paymentDetail.brandId = invoiceData.brandId;
			paymentDetail.transactionAmount = 0;
			paymentDetail.previousBalance = 0;
			paymentDetail.invoiceId = savedInvoice.id;
			paymentDetail.type = EnumAmountType.Invoice;
			paymentDetail.paymentType = EnumPaymentType.Cash;
			paymentDetail.amountPayable = 0;
			paymentDetail.mainBalance = 0;
			paymentDetail.amount = 0;
			paymentDetail.createdAt = appointmentDate;
			paymentDetail.showInLedger = false;
			paymentDetail.showInInvoice = true;

			const savedPaymentDetail =
				await queryRunner.manager.save(paymentDetail);

			// console.log('savedPaymentDetail', savedPaymentDetail);

			await queryRunner.commitTransaction();

			return { invoice: savedInvoice, paymentDetail: savedPaymentDetail };
		} catch (error) {
			await queryRunner.rollbackTransaction();
			console.error('Error creating invoice and payment detail:', error);
			throw new BadRequestException(
				'Failed to create invoice and payment detail'
			);
		} finally {
			await queryRunner.release();
		}
	}

	async processInvoices(invoiceData: InvoiceDataDto[]): Promise<any[]> {
		const results = [];

		for (const data of invoiceData) {
			try {
				// Find patient
				const patient = await this.findByOldId(
					data.patientId,
					data.clinicId
				);
				if (!patient) {
					this.logger.error(`Patient not found: ${data.patientId}`);
					results.push({
						status: 'error',
						message: `Patient not found: ${data.patientId}`
					});
					continue;
				}

				// Find patient owners
				const patientOwners = await this.patientOwnersRepository.find({
					where: { patientId: patient.id }
				});

				console.log(patientOwners[0].ownerId);

				if (!patientOwners?.length) {
					this.logger.error(
						`No owners found for patient: ${patient.id}`
					);
					results.push({
						status: 'error',
						message: `No owners found for patient ${patient.id}`
					});
					continue;
				}

				// Find appointment
				const appointment = await this.findByPatientAndDate(
					patient.id,
					'31 Dec 2024'
				);
				if (!appointment) {
					this.logger.error(
						`Appointment not found for patient ${patient.id} on ${data.date}`
					);
					results.push({
						status: 'error',
						message: `Appointment not found for patient ${patient.id} on ${data.date}`
					});
					continue;
				}

				// Create cart
				const cart = await this.createCart(appointment.id);

				console.log(cart, appointment);
				// Create invoice
				const invoice = await this.createInvoiceAndPaymentDetail({
					cartId: cart.id,
					patientId: patient.id,
					ownerId: patientOwners[0].ownerId,
					s3Key: data.s3Key,
					date: data.date,
					clinicId: data.clinicId,
					brandId: data.brandId,
					metadata: data.metadata
				});

				results.push({
					status: 'success',
					patientId: patient.id,
					appointmentId: appointment.id,
					ownerId: patientOwners[0].ownerId
				});
			} catch (error) {
				console.log(error);
				this.logger.error('Error processing invoice', {
					error,
					patientId: data.patientId,
					date: data.date
				});
				results.push({
					status: 'error',
					message: error,
					data: data
				});
			}
		}

		return results;
	}

	// async processDiagnostics(data: {
	//   patientId: string;
	//   date: string;
	//   labReportTypeId: string;
	//   fileData: any;
	// }): Promise<any> {
	//   const queryRunner = this.dataSource.createQueryRunner();
	//   await queryRunner.connect();
	//   await queryRunner.startTransaction();

	//   try {
	//     // Find patient using old ID
	//     const patient = await this.findByOldId(data.patientId);
	//     if (!patient) {
	//       throw new NotFoundException(`Patient not found: ${data.patientId}`);
	//     }

	//     console.log(patient)
	//     // Find appointment
	//     const appointment = await this.findByPatientAndDate(patient.id, data.date);
	//     if (!appointment) {
	//       throw new NotFoundException(
	//         `Appointment not found for patient ${patient.id} on ${data.date}`
	//       );
	//     }

	//     console.log(appointment.id)
	//     // Create lab report entry
	//     const labReport = await this.createLabReport({
	//       appointmentId: appointment.id,
	//       clinicLabReportId: data.labReportTypeId,
	//       clinicId: appointment.clinicId,
	//       patientId: patient.id,
	//       files: [data.fileData],
	//       status: 'COMPLETED',
	//       date:data.date
	//     });

	//     // Update appointment details
	//     const apdet = await this.updateAppointmentDetailsWithLabReport(
	//       appointment.id,
	//       data.labReportTypeId,
	//       data.fileData
	//     );

	//     console.log(apdet)
	//     await queryRunner.commitTransaction();

	//     return {
	//       status: 'success',
	//       patientId: patient.id,
	//       appointmentId: appointment.id,
	//       labReportId: labReport.id
	//     };

	//   } catch (error) {
	//     await queryRunner.rollbackTransaction();
	//     throw error;
	//   } finally {
	//     await queryRunner.release();
	//   }
	// }

	private async createLabReport(data: {
		appointmentId: string;
		clinicLabReportId: string;
		clinicId: string;
		patientId: string;
		files: any[];
		status: string;
		date: string;
	}): Promise<any> {
		const appointmentDate = new Date(data.date);
		const labReport = this.labReportRepository.create({
			appointmentId: data.appointmentId,
			clinicLabReportId: data.clinicLabReportId,
			clinicId: data.clinicId,
			patientId: data.patientId,
			files: data.files,
			status: 'COMPLETED',
			createdAt: appointmentDate
		});

		return this.labReportRepository.save(labReport);
	}

	private async updateAppointmentDetailsWithLabReport(
		appointmentId: string,
		labReportTypeId: string,
		fileData: LabReportFile
	): Promise<void> {
		const appointmentDetails =
			(await this.appointmentDetailsRepository.findOne({
				where: { appointmentId }
			})) as AppointmentDetails | null;

		if (!appointmentDetails) {
			// Create new appointment details if doesn't exist
			const newDetails = {
				objective: {
					labReports: [
						{
							files: [fileData],
							label: 'IDEXX',
							value: labReportTypeId
						}
					]
				}
			};

			await this.appointmentDetailsRepository.save({
				appointmentId,
				details: newDetails
			});
		} else {
			const details = appointmentDetails.details;
			const labReports = details.objective.labReports;

			// Find existing report with the same value
			const existingReport = labReports.find(
				report => report.value === labReportTypeId
			);

			if (existingReport) {
				// Add new file to existing report
				existingReport.files = existingReport.files || [];
				existingReport.files.push(fileData);
			} else {
				// Add new report
				labReports.push({
					files: [fileData],
					label: 'IDEXX',
					value: labReportTypeId
				});
			}

			await this.appointmentDetailsRepository.save(appointmentDetails);
		}
	}

	// async createMigratedAppointment(
	//   appointmentData: CreateAppointmentDto
	// ): Promise<AppointmentEntity> {
	//   const queryRunner = this.dataSource.createQueryRunner();
	//   await queryRunner.connect();
	//   await queryRunner.startTransaction();

	//   try {
	//     this.logger.log('Creating migrated appointment', {
	//       patientId: appointmentData.patientId,
	//       date: appointmentData.date
	//     });

	//     // Check for existing appointment
	//     const existingAppointment = await this.findExistingAppointment(
	//       appointmentData.patientId,
	//       new Date(appointmentData.date)
	//     );

	//     if (existingAppointment) {
	//       this.logger.log('Appointment already exists', {
	//         appointmentId: existingAppointment.id,
	//         patientId: appointmentData.patientId,
	//         date: appointmentData.date
	//       });
	//       return existingAppointment;
	//     }

	//     // Create new appointment
	//     const appointment = this.appointmentRepository.create({
	//       clinicId: appointmentData.clinicId,
	//       patientId: appointmentData.patientId,
	//       date: new Date(appointmentData.date),
	//       startTime: appointmentData.startTime,
	//       endTime: appointmentData.endTime,
	//       reason: appointmentData.reason,
	//       type: appointmentData.type,
	//       status: appointmentData.status,
	//       isBlocked: appointmentData.isBlocked,
	//       weight: appointmentData.weight
	//     });

	//     const savedAppointment = await queryRunner.manager.save(appointment);

	//     // Create doctor relationships
	//     const doctorPromises = appointmentData.doctorIds.map(async (doctorId) => {
	//       const appointmentDoctor = this.appointmentDoctorsRepository.create({
	//         appointmentId: savedAppointment.id,
	//         doctorId,
	//         primary: true
	//       });
	//       return queryRunner.manager.save(appointmentDoctor);
	//     });

	//     await Promise.all(doctorPromises);
	//     await queryRunner.commitTransaction();

	//     this.logger.log('Migrated appointment created successfully', {
	//       appointmentId: savedAppointment.id,
	//       patientId: appointmentData.patientId,
	//       date: savedAppointment.date
	//     });

	//     return savedAppointment;

	//   } catch (error) {
	//     await queryRunner.rollbackTransaction();
	//     this.logger.error('Error creating migrated appointment', {
	//       error: error,
	//       patientId: appointmentData.patientId
	//     });
	//     throw error;
	//   } finally {
	//     await queryRunner.release();
	//   }
	// }

	async bulkUpdateOpeningBalance(
		updates: UpdateOwnerOpeningBalanceDto[]
	): Promise<
		Array<{
			clientId: string;
			status: 'success' | 'error';
			message?: string;
			ownerId?: string;
		}>
	> {
		const queryRunner = this.dataSource.createQueryRunner();
		await queryRunner.connect();
		await queryRunner.startTransaction();

		const results: Array<{
			clientId: string;
			status: 'success' | 'error';
			message?: string;
			ownerId?: string;
		}> = [];

		try {
			for (const update of updates) {
				try {
					// Find owner brand using client ID
					const ownerBrand = await queryRunner.manager
						.createQueryBuilder(OwnerBrand, 'ownerBrand')
						.where("dummy_data->>'clientId' = :clientId", {
							clientId: update.clientId
						})
						.getOne();

					if (!ownerBrand) {
						results.push({
							clientId: update.clientId,
							status: 'error',
							message: 'Owner not found'
						});
						continue;
					}

					// Update opening balance only
					const updateData: Partial<OwnerBrand> = {
						openingBalance: update.openingBalance
					};

					// Update owner brand
					await queryRunner.manager.update(
						OwnerBrand,
						{ id: ownerBrand.id },
						updateData
					);

					results.push({
						clientId: update.clientId,
						status: 'success',
						ownerId: ownerBrand.id
					});

					this.logger.log('Successfully updated owner balance', {
						clientId: update.clientId,
						ownerId: ownerBrand.id,
						openingBalance: update.openingBalance
					});
				} catch (error) {
					this.logger.error(
						'Error updating individual owner balance',
						{
							error,
							clientId: update.clientId
						}
					);
					results.push({
						clientId: update.clientId,
						status: 'error',
						message:
							error instanceof Error
								? error.message
								: 'Unknown error'
					});
				}
			}

			await queryRunner.commitTransaction();
			return results;
		} catch (error) {
			await queryRunner.rollbackTransaction();
			throw error;
		} finally {
			await queryRunner.release();
		}
	}
	async findByOldPatientId(
		oldPatientId: string,
		clinicId: string
	): Promise<Patient> {
		try {
			const patient = await this.patientsRepository
				.createQueryBuilder('patient')
				.where("patient.dummy_data->>'patientId' = :oldPatientId", {
					oldPatientId
				})
				.andWhere('patient.clinic_id = :clinicId', { clinicId })
				.getOne();

			if (!patient) {
				this.logger.error('Patient not found with old ID', {
					oldPatientId,
					clinicId
				});
				throw new NotFoundException(
					`Patient with old ID "${oldPatientId}" not found`
				);
			}

			this.logger.log('Patient found successfully', {
				oldPatientId,
				newPatientId: patient.id,
				clinicId
			});

			return patient;
		} catch (error) {
			this.logger.error('Error finding patient by old ID', {
				error,
				oldPatientId,
				clinicId
			});
			throw error;
		}
	}

	async updateMicrochipId(id: string, microchipId: string): Promise<Patient> {
        try {
            const patient = await this.patientsRepository.findOne({ 
                where: { id } 
            });

            if (!patient) {
                this.logger.error('Patient not found', { patientId: id });
                throw new NotFoundException(`Patient with ID "${id}" not found`);
            }

            // Update microchip ID
            patient.microchipId = microchipId;
            
            // Save the updated patient
            const updatedPatient = await this.patientsRepository.save(patient);
            
            this.logger.log('Microchip ID updated successfully', { 
                patientId: id,
                microchipId 
            });
            
            return updatedPatient;
        } catch (error) {
            this.logger.error('Error updating microchip ID', { 
                error, 
                patientId: id 
            });
            throw error;
        }
    }
}
