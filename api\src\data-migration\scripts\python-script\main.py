import os
import csv
import pdfplumber
import pandas as pd
import re
import logging
from typing import List, Dict,Tuple,Optional
from datetime import datetime
from collections import defaultdict

# Set up logging
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'pdf_processing_{timestamp}.log'),
        logging.StreamHandler()
    ]
)

def parse_appointment_dates_from_links(text: str) -> Dict[str, Tuple[str, ...]]:
    """Extract dates associated with attached images/links."""
    date_pattern = r'(?:Subjective|Objective) \(Attached\).*?(?=\n\w{3},?\s+\d{2}\s+\w{3}\s+\d{4}|$)'
    appointment_dates = {}
    
    # Find all dates in the text
    dates = re.finditer(r'(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s+\d{2}\s+\w{3}\s+\d{4}', text)
    current_date = None
    
    for match in dates:
        date = match.group()
        # Normalize date format
        date = re.sub(r'^(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s+', '', date)
        current_date = date
        
        # Look for attachments in the text following this date
        text_after_date = text[match.end():].split('\n\n')[0]
        if '(Attached)' in text_after_date:
            appointment_dates[current_date] = {
                'Subjective (Attached)': [],
                'Objective (Attached)': []
            }
            
    return appointment_dates

def parse_date_from_header(text: str) -> str:
    """Extract date from the appointment header format."""
    date_pattern = r'(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s+(\d{2}\s+[A-Za-z]{3}\s+\d{4})'
    match = re.search(date_pattern, text)
    if match:
        return match.group(1)
    return None


def normalize_date(date_str: str) -> str:
    """Normalize date format for consistent comparison."""
    if not date_str:
        return None
        
    # Skip if this is an overdue entry
    if 'OverDue' in date_str:
        return None
        
    match = re.search(r'(\d{2}\s+[A-Za-z]{3}\s+\d{4})', date_str)
    if match:
        # Additional check to ensure it's not part of an overdue line
        if not re.search(r'OverDue.*?'+match.group(1), date_str, re.IGNORECASE):
            return match.group(1)
    return None

def enhanced_normalize_date(date_str: str) -> str:
    """Enhanced date normalization that handles multiple formats"""
    if not date_str:
        return None
        
    # Skip patterns
    skip_patterns = [
        'OverDue',
        'Visit List',
        'Common Patient Medical Record'
        # ... other skip patterns
    ]
    
    for pattern in skip_patterns:
        if pattern in date_str:
            return None
            
    # Try different date formats
    date_patterns = [
        # Format: "Fri 29 Mar 2024"
        r'(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+(\d{2}\s+[A-Za-z]{3}\s+\d{4})',
        
        # Format: "May 02, 2019"
        r'((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{2},?\s+\d{4})'
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, date_str)
        if match:
            date_str = match.group(1)
            # Remove any comma
            date_str = date_str.replace(',', '')
            try:
                # Convert to standard format
                if re.match(r'\d{2}\s+[A-Za-z]{3}\s+\d{4}', date_str):
                    return date_str
                else:
                    # Convert from "May 02 2019" to "02 May 2019"
                    date_obj = datetime.strptime(date_str, '%b %d %Y')
                    return date_obj.strftime('%d %b %Y')
            except ValueError:
                continue
                
    return None

def process_link(link: str) -> str:
    """Add prefix to links if needed."""
    link = link.strip()
    if not link.startswith('https'):
        return f'https://topdogpets.thevetbuddy.com/{link}'
    return link


def extract_text_and_links_from_pdf(pdf_path: str):
    """Extract text and links while preserving original link format."""
    text = ''
    date_specific_links = {}
    current_date = None
    page_content = []
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text() or ''
                page_hyperlinks = []
                
                # Preserve original hyperlinks exactly as they appear
                if page.hyperlinks:
                    for link in page.hyperlinks:
                        if 'uri' in link:
                            processed_link = process_link(link['uri'])
                            page_hyperlinks.append(link['uri'])
                
                page_content.append({
                    'text': page_text,
                    'hyperlinks': page_hyperlinks
                })
                text += page_text

        # Process all content after collecting it
        for content in page_content:
            sections = re.split(r'(?=(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s+\d{2}\s+[A-Za-z]{3}\s+\d{4})', content['text'])
            
            for section in sections:
                if not section.strip():
                    continue
                    
                date = normalize_date(section)
                if date:
                    current_date = date
                    if current_date not in date_specific_links:
                        date_specific_links[current_date] = {
                            'Subjective (Attached)': set(),
                            'Objective (Attached)': set()
                        }
                
                # Process attachments with original links
                if date and '(Attached)' in section:
                    for link in content['hyperlinks']:
                        if 'Subjective (Attached)' in section:
                            date_specific_links[current_date]['Subjective (Attached)'].add(link)
                        elif 'Objective (Attached)' in section:
                            date_specific_links[current_date]['Objective (Attached)'].add(link)

        # Convert sets to lists while preserving original link format
        for date in date_specific_links:
            date_specific_links[date] = {
                'Subjective (Attached)': list(date_specific_links[date]['Subjective (Attached)']),
                'Objective (Attached)': list(date_specific_links[date]['Objective (Attached)'])
            }

        return text, date_specific_links
    
    except Exception as e:
        logging.error(f"Error extracting text and links from PDF {pdf_path}: {str(e)}")
        return None, {}

def extract_text_from_pdf(pdf_path: str) -> Optional[str]:
    """Safely extract text from PDF with error handling."""
    try:
        with pdfplumber.open(pdf_path) as pdf:
            text = ''
            for page in pdf.pages:
                text += page.extract_text() or ''
            return text
    except Exception as e:
        logging.error(f"Error processing PDF {pdf_path}: {str(e)}")
        return None

def extract_client_patient_details(lines: List[str]) -> Dict[str, str]:
    """Extract client and patient details with error handling."""
    client_patient_details = {
        'Owner Name': 'NA', 'Mobile Number': 'NA', 'Scheme': 'NA',
        'City': 'NA', 'Address': 'NA', 'Patient ID': 'NA',
        'Patient Name': 'NA', 'Gender': 'NA', 'Breed1': 'NA',
        'Breed2': 'NA', 'Breed': 'NA'
    }
    
    try:
        client_patient_details_extracted = False
        client_patient_details_extracted1 = False

        for line in lines:
            if 'Client' in line and not client_patient_details_extracted:
                owner_info = line.split('|')
                if len(owner_info) > 1:
                    client_patient_details.update({
                        'Owner Name': owner_info[0].replace('Client ', '').strip(),
                        'Mobile Number': owner_info[1].strip() if len(owner_info) > 1 else 'NA',
                        'Scheme': owner_info[2].strip() if len(owner_info) > 2 else 'NA',
                        'City': owner_info[3].strip() if len(owner_info) > 3 else 'NA',
                        'Address': owner_info[4].strip() if len(owner_info) > 4 else 'NA'
                    })
                    client_patient_details_extracted = True

            if 'Patient #' in line and not client_patient_details_extracted1:
                patient_info = line.split('|')
                if len(patient_info) > 1:
                    client_patient_details.update({
                        'Patient ID': patient_info[0].replace('Patient #', '').strip(),
                        'Patient Name': patient_info[1].strip() if len(patient_info) > 1 else 'NA',
                        'Gender': patient_info[4].strip() if len(patient_info) > 4 else 'NA',
                        'Breed1': patient_info[5].strip() if len(patient_info) > 5 else 'NA',
                        'Breed2': patient_info[6].strip() if len(patient_info) > 6 else 'NA',
                        'Breed': patient_info[7].strip() if len(patient_info) > 7 else 'NA'
                    })
                    client_patient_details_extracted1 = True

    except Exception as e:
        logging.error(f"Error extracting client/patient details: {str(e)}")

    return client_patient_details

def extract_data(appointment_text):
    patterns = {
        'Appointment Date': r'(\d{2} \w{3} \d{4})',
        'Subjective': r'Subjective(.*?)(?=Objective|$)',
        'Subjective (Attached)': r'Subjective \(Attached\):(.*?)(?=Objective|$)',
        'Objective': r'Objective(.*?)(?=Assessment|$)',
        'Objective (Attached)': r'Objective \(Attached\)(.*?)(?=Assessment|$)',
        'Assessment': r'Assessment(.*?)(?=Plan:|$)',
        'Plan': r'Plan(.*?)(?=Weight:|$)',
        'Weight': r'Weight:\s*([\d.]+\s*Kg)',
        'Vitals': r'Vitals:\s*([\d.]+\s*Kg)',
        'Temperature': r'Temperature:\s*([\d.]+)',
        'Diagnostic': r'Diagnostic(.*?)(?=Treatment|$)',
        'Treatment': r'Treatment(.*?)(?=Internal Memo|$)',
        'Internal Memo': r'Internal Memo(.*?)(?=Message Board|$)',
        'Message Board': r'Message Board(.*?)(?=Idexx:|$)',
        'Idexx': r'Idexx CBC(.*?)(?=Subjective \(Attached\) Images|$)',
        'Subjective (Attached) Images': r'Subjective \(Attached\) Images(.*?)(?=Objective \(Attached\) Images|$)',
        'Objective (Attached) Images': r'Objective \(Attached\) Images(.*?)(?=Abdominal Ultrasound|$)',
        'Abdominal Ultrasound': r'Abdominal Ultrasound(.*?)(?=$)'
    }

    data = {}
    for key, pattern in patterns.items():
        match = re.search(pattern, appointment_text, re.DOTALL | re.IGNORECASE)
        if match:
            value = match.group(1).strip()
            if key in ['Subjective (Attached) Images', 'Objective (Attached) Images']:
                print(data[key],key)
                data[key] = 'Image present' if value else 'NA'
            else:
                data[key] = value.replace('\n', ' | ') if value else 'NA'
        else:
            data[key] = 'NA'
    
    return data

def process_pdf(pdf_path: str) -> List[Dict[str, str]]:
    """Process PDF preserving original link format."""
    try:
        text, date_specific_links = extract_text_and_links_from_pdf(pdf_path)
        if text is None:
            logging.warning(f"Skipping PDF {pdf_path} due to extraction error")
            return []

        lines = text.split('\n')
        client_patient_details = extract_client_patient_details(lines)
        
        appointments = re.split(
            r'(?=(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+\d{2}\s+[A-Za-z]{3}\s+\d{4}|' + 
            r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{2},?\s+\d{4})',
            text
        )
        appointments = [app.strip() for app in appointments if app.strip()]
        
        processed_appointments = {}
        
        print(processed_appointments)
        for appointment in appointments:
            try:
                data = extract_data(appointment)
                date = normalize_date(appointment)
                print(date)
                
                if not date:
                    continue
                
                if date not in processed_appointments:
                    processed_appointments[date] = data
                else:
                    for key, value in data.items():
                        if value != 'NA' and processed_appointments[date][key] == 'NA':
                            processed_appointments[date][key] = value
                
                # Add original format links if they exist for this date
                if date in date_specific_links:
                    subj_links = date_specific_links[date]['Subjective (Attached)']
                    obj_links = date_specific_links[date]['Objective (Attached)']
                    
                    processed_appointments[date]['Subjective Links'] = ' | '.join(subj_links) if subj_links else 'NA'
                    processed_appointments[date]['Objective Links'] = ' | '.join(obj_links) if obj_links else 'NA'
                    processed_appointments[date]['Subjective (Attached)'] = 'Images present with links' if subj_links else 'NA'
                    processed_appointments[date]['Objective (Attached)'] = 'Images present with links' if obj_links else 'NA'
                    
                processed_appointments[date].update(client_patient_details)
                
            except Exception as e:
                logging.error(f"Error processing appointment in {pdf_path}: {str(e)}")
                continue
        
        # Convert processed appointments back to list
        appointment_data = list(processed_appointments.values())
        
        return appointment_data

    except Exception as e:
        logging.error(f"Error processing PDF {pdf_path}: {str(e)}")
        return []

def main_single_folder(input_folder: str, output_csv: str) -> None:
    """Main function to process folder of PDFs with detailed success/failure tracking."""
    all_appointments = []
    stats = {
        'processed_files': 0,
        'failed_files': 0,
        'total_files': 0,
        'successful_pdfs': [],
        'failed_pdfs': defaultdict(list)  # Will store filename -> error message
    }
    
    try:
        # Ensure input folder exists
        if not os.path.exists(input_folder):
            raise FileNotFoundError(f"Input folder {input_folder} does not exist")

        # Get total PDF count
        pdf_files = [f for f in os.listdir(input_folder) if f.endswith('.pdf')]
        stats['total_files'] = len(pdf_files)
        
        logging.info(f"Starting processing of {stats['total_files']} PDF files")
        
        # Process each PDF
        for filename in pdf_files:
            pdf_path = os.path.join(input_folder, filename)
            logging.info(f"Processing {filename}...")

            try:
                appointments = process_pdf(pdf_path)
                if appointments:  # Check if any appointments were extracted
                    all_appointments.extend(appointments)
                    stats['processed_files'] += 1
                    stats['successful_pdfs'].append(filename)
                    logging.info(f"Successfully processed {filename} - extracted {len(appointments)} appointments")
                else:
                    stats['failed_files'] += 1
                    stats['failed_pdfs'][filename].append("No appointments extracted")
                    logging.error(f"Failed to extract appointments from {filename}")
                    
            except Exception as e:
                stats['failed_files'] += 1
                stats['failed_pdfs'][filename].append(str(e))
                logging.error(f"Failed to process {filename}: {str(e)}")
                continue

        # Write detailed processing report
        write_processing_report(stats, timestamp)

        # Create DataFrame and save to CSV if we have any appointments
        if all_appointments:
            columns = [
                'Appointment Date', 'Owner Name', 'Mobile Number', 'Scheme', 'City', 
                'Address', 'Patient ID', 'Patient Name', 'Weight', 'Vitals', 'Gender', 
                'Breed1', 'Breed2', 'Breed', 'Subjective', 'Subjective (Attached)','Subjective Links' 
                'Objective', 'Objective (Attached)','Objective Links', 'Assessment', 'Plan', 'Temperature', 
                'Diagnostic', 'Treatment', 'Internal Memo', 'Message Board', 'Idexx',
                'Subjective (Attached) Images', 'Objective (Attached) Images', 
                'Abdominal Ultrasound'
            ]

            df = pd.DataFrame(all_appointments, columns=columns)
            df.to_csv(output_csv, index=False, quoting=csv.QUOTE_ALL)
            logging.info(f"CSV file has been saved to {output_csv}")
        else:
            logging.error("No appointments were extracted from any PDF files")

    except Exception as e:
        logging.error(f"Critical error in main processing: {str(e)}")
        raise

def write_processing_report(stats: Dict, timestamp: str) -> None:
    """Write detailed processing report to a separate log file."""
    report_filename = f'processing_report_{timestamp}.log'
    
    with open(report_filename, 'w') as f:
        f.write("=== PDF Processing Report ===\n\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total PDFs Found: {stats['total_files']}\n")
        f.write(f"Successfully Processed: {stats['processed_files']}\n")
        f.write(f"Failed to Process: {stats['failed_files']}\n")
        f.write(f"Success Rate: {(stats['processed_files']/stats['total_files']*100 if stats['total_files'] else 0):.2f}%\n\n")
        
        f.write("=== Successfully Processed PDFs ===\n")
        for pdf in sorted(stats['successful_pdfs']):
            f.write(f"✓ {pdf}\n")
        
        f.write("\n=== Failed PDFs ===\n")
        for pdf, errors in sorted(stats['failed_pdfs'].items()):
            f.write(f"✗ {pdf}\n")
            for error in errors:
                f.write(f"  - {error}\n")

        # Write summary to main log as well
        logging.info(f"Detailed processing report written to {report_filename}")
        logging.info(f"Summary: Processed {stats['processed_files']}/{stats['total_files']} files successfully "
                    f"({(stats['processed_files']/stats['total_files']*100 if stats['total_files'] else 0):.2f}% success rate)")

if __name__ == "__main__":
    input_folder = "/Users/<USER>/Downloads/pdf_extractor 2/EMR_PDF2s"
    output_csv = "/Users/<USER>/Downloads/pdf_extractor 2/output/output.csv"
    
    try:
        main_single_folder(input_folder, output_csv)
    except Exception as e:
        logging.error(f"Application failed: {str(e)}")