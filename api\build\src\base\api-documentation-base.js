"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiDocumentationBase = void 0;
const swagger_1 = require("@nestjs/swagger");
const package_json_1 = require("../../package.json");
let ApiDocumentationBase = class ApiDocumentationBase {
    static initApiDocumentation(app) {
        const config = new swagger_1.DocumentBuilder()
            .setTitle(package_json_1.name)
            .setDescription(package_json_1.description)
            .setVersion(package_json_1.version)
            .addTag(package_json_1.tag)
            // .addSecurity()
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api', app, document);
    }
};
exports.ApiDocumentationBase = ApiDocumentationBase;
exports.ApiDocumentationBase = ApiDocumentationBase = __decorate([
    (0, swagger_1.ApiNotFoundResponse)({
        description: 'Not Found'
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Bad Request'
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Unauthorized'
    })
    // We use Swagger for api documentation- The Swagger framework allows developers to create interactive, machine and human-readable API documentation.
], ApiDocumentationBase);
//# sourceMappingURL=api-documentation-base.js.map