import { Test, TestingModule } from '@nestjs/testing';
import { ClinicConsumblesController } from './clinic-consumbles.controller';
import { ClinicConsumblesService } from './clinic-consumbles.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicConsumableEntity } from './entities/clinic-consumable.entity';
import { RoleService } from '../roles/role.service';

describe('ClinicConsumblesController', () => {
	let controller: ClinicConsumblesController;
	let consumblesService: jest.Mocked<ClinicConsumblesService>;
	let logger: jest.Mocked<WinstonLogger>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicConsumblesController],
			providers: [
				{
					provide: ClinicConsumblesService,
					useValue: {
						getConsumables: jest.fn(),
						create: jest.fn(),
						findOne: jest.fn(),
						update: jest.fn(),
						remove: jest.fn()
					}
				},
				{
					provide: <PERSON>Logger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicConsumblesController>(
			ClinicConsumblesController
		);
		consumblesService = module.get(ClinicConsumblesService);
		logger = module.get(WinstonLogger);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('Get consumables for a clinic - GET - /clinic-consumables', () => {
		const consumablesDataList: ClinicConsumableEntity[] = [
			{
				id: 'consumable_uuid_1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				productName: 'product_name_A',
				currentStock: 12,
				minimumQuantity: 2
			},
			{
				id: 'consumable_uuid_2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_2',
				productName: 'product_name_B',
				currentStock: 12,
				minimumQuantity: 2
			}
		];

		it('should have a getAllConsumables function', () => {
			expect(controller.getAllConsumables).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the getAllConsumables call', async () => {
			const error = new HttpException(
				'Error fetching all the consumables',
				HttpStatus.BAD_REQUEST
			);

			consumblesService.getConsumables.mockRejectedValue(error);

			await expect(
				controller.getAllConsumables('clinicId')
			).rejects.toThrow('Error fetching all the consumables');
			expect(consumblesService.getConsumables).toHaveBeenCalled();
		});

		it('should return a list of consumables without a search keyword', async () => {
			consumblesService.getConsumables.mockResolvedValue(
				consumablesDataList
			);
			const result = await controller.getAllConsumables('clinicId');
			expect(result).toEqual(consumablesDataList);
			expect(consumblesService.getConsumables).toHaveBeenCalledWith(
				'clinicId',
				undefined
			);
		});

		it('should return a list of consumables for a clinic id with search keyword', async () => {
			const searchKeyword = 'product_name_A';
			const clinicId = 'clinic_id';

			const filteredData = consumablesDataList.filter(consumable =>
				consumable.productName.includes(searchKeyword)
			);

			consumblesService.getConsumables.mockResolvedValue(filteredData);
			const result = await controller.getAllConsumables(
				clinicId,
				searchKeyword
			);
			expect(result).toEqual(filteredData);
			expect(consumblesService.getConsumables).toHaveBeenCalledWith(
				clinicId,
				searchKeyword
			);
		});
	});

	describe('create', () => {
		it('should create a new consumable', async () => {
			const createDto = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'unique_id',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};
			const createdEntity = { ...createDto, id: 'new_uuid' };
			consumblesService.create.mockResolvedValue(createdEntity as any);

			const result = await controller.create(createDto);

			expect(consumblesService.create).toHaveBeenCalledWith(createDto);
			expect(result).toEqual(createdEntity);
		});

		it('should handle errors during creation', async () => {
			const createDto = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'unique_id',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};
			const error = new HttpException(
				'Error creating consumable',
				HttpStatus.BAD_REQUEST
			);
			consumblesService.create.mockRejectedValue(error);

			await expect(controller.create(createDto)).rejects.toThrow(
				'Error creating consumable'
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating consumable',
				{ error }
			);
		});
	});

	describe('findOne', () => {
		it('should return a consumable by id', async () => {
			const consumable = {
				id: 'consumable_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'unique_id',
				productName: 'Product A',
				currentStock: 100,
				minimumQuantity: 10
			};
			consumblesService.findOne.mockResolvedValue(consumable as any);

			const result = await controller.findOne('consumable_uuid');

			expect(consumblesService.findOne).toHaveBeenCalledWith(
				'consumable_uuid'
			);
			expect(result).toEqual(consumable);
		});

		it('should handle errors when consumable is not found', async () => {
			const error = new HttpException(
				'Error fetching consumable',
				HttpStatus.NOT_FOUND
			);
			consumblesService.findOne.mockRejectedValue(error);

			await expect(
				controller.findOne('nonexistent_uuid')
			).rejects.toThrow('Error fetching consumable');
			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching consumable',
				{ error }
			);
		});
	});

	describe('update', () => {
		it('should update and return the consumable', async () => {
			const updateDto = {
				productName: 'Updated Product',
				currentStock: 150
			};
			const updatedEntity = {
				id: 'consumable_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'unique_id',
				productName: 'Updated Product',
				currentStock: 150,
				minimumQuantity: 10
			};
			consumblesService.update.mockResolvedValue(updatedEntity as any);

			const result = await controller.update(
				'consumable_uuid',
				updateDto
			);

			expect(consumblesService.update).toHaveBeenCalledWith(
				'consumable_uuid',
				updateDto
			);
			expect(result).toEqual(updatedEntity);
		});

		it('should handle errors during update', async () => {
			const updateDto = {
				productName: 'Updated Product',
				currentStock: 150
			};
			const error = new HttpException(
				'Error updating consumable',
				HttpStatus.BAD_REQUEST
			);
			consumblesService.update.mockRejectedValue(error);

			await expect(
				controller.update('consumable_uuid', updateDto)
			).rejects.toThrow('Error updating consumable');
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating consumable',
				{ error }
			);
		});
	});

	describe('remove', () => {
		it('should delete the consumable and return a success message', async () => {
			consumblesService.remove.mockResolvedValue(undefined);

			const result = await controller.remove('consumable_uuid');

			expect(consumblesService.remove).toHaveBeenCalledWith(
				'consumable_uuid'
			);
			expect(result).toEqual({
				message: 'Consumable deleted successfully'
			});
		});

		it('should handle errors during deletion', async () => {
			const error = new HttpException(
				'Error deleting consumable',
				HttpStatus.BAD_REQUEST
			);
			consumblesService.remove.mockRejectedValue(error);

			await expect(controller.remove('consumable_uuid')).rejects.toThrow(
				'Error deleting consumable'
			);
			expect(logger.error).toHaveBeenCalledWith(
				'Error deleting consumable',
				{ error }
			);
		});
	});
});
