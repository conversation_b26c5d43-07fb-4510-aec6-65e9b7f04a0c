import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { DataSource } from 'typeorm';
import { ClientDashboardService } from './client-dashboard.service';
import { OwnersService } from '../owners/owners.service';
import { AppointmentsService } from '../appointments/appointments.service';
import { PatientsService } from '../patients/patients.service';
import { BrandService } from '../brands/brands.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { NotFoundException } from '@nestjs/common';
import { DirectLoginDto } from './dto/direct-login.dto';
import { ClientDashboardResponseDto } from './dto/client-dashboard-response.dto';
import { ClientAppointmentsResponseDto } from './dto/client-appointments-response.dto';
import { Brand } from '../brands/entities/brand.entity';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { Patient } from '../patients/entities/patient.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { Role } from '../roles/role.enum';
import * as moment from 'moment-timezone';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { EnumAppointmentType } from '../appointments/enums/enum-appointment-type';

// Mock Services
const mockOwnersService = {
	findGlobalOwnerByPhoneNumber: jest.fn(),
	findOwnerBrandByGlobalOwnerAndBrand: jest.fn(),
	findOrCreateOwnerBrand: jest.fn(),
	getAllOwnerPatients: jest.fn()
};
const mockAppointmentsService = {
	getAppointmentsForPatient: jest.fn()
};
const mockPatientsService = {
	computeOwnerBalance: jest.fn()
};
const mockBrandService = {
	getBrandById: jest.fn()
};
const mockJwtService = {
	sign: jest.fn()
};
const mockLogger = {
	log: jest.fn(),
	error: jest.fn(),
	warn: jest.fn()
};

// Mock DataSource and QueryBuilder
const mockQueryBuilder = {
	select: jest.fn().mockReturnThis(),
	from: jest.fn().mockReturnThis(),
	innerJoin: jest.fn().mockReturnThis(),
	where: jest.fn().mockReturnThis(),
	andWhere: jest.fn().mockReturnThis(),
	orderBy: jest.fn().mockReturnThis(),
	getRawMany: jest.fn()
};
const mockDataSource = {
	createQueryBuilder: jest.fn(() => mockQueryBuilder)
};

describe('ClientDashboardService', () => {
	let service: ClientDashboardService;
	let ownersService: OwnersService;
	let appointmentsService: AppointmentsService;
	let patientsService: PatientsService;
	let brandService: BrandService;
	let jwtService: JwtService;
	let dataSource: DataSource;
	let logger: WinstonLogger;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClientDashboardService,
				{ provide: OwnersService, useValue: mockOwnersService },
				{
					provide: AppointmentsService,
					useValue: mockAppointmentsService
				},
				{ provide: PatientsService, useValue: mockPatientsService },
				{ provide: BrandService, useValue: mockBrandService },
				{ provide: JwtService, useValue: mockJwtService },
				{ provide: DataSource, useValue: mockDataSource },
				{ provide: WinstonLogger, useValue: mockLogger }
			]
		}).compile();

		service = module.get<ClientDashboardService>(ClientDashboardService);
		ownersService = module.get<OwnersService>(OwnersService);
		appointmentsService =
			module.get<AppointmentsService>(AppointmentsService);
		patientsService = module.get<PatientsService>(PatientsService);
		brandService = module.get<BrandService>(BrandService);
		jwtService = module.get<JwtService>(JwtService);
		dataSource = module.get<DataSource>(DataSource);
		logger = module.get<WinstonLogger>(WinstonLogger);

		// Reset mocks
		jest.clearAllMocks();
		// Reset query builder mocks specifically if needed between tests
		Object.values(mockQueryBuilder).forEach(mockFn =>
			mockFn.mockClear().mockReturnThis()
		);
		mockQueryBuilder.getRawMany.mockClear();
		mockDataSource.createQueryBuilder.mockClear();
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	// --- Test cases for directLogin ---
	describe('directLogin', () => {
		const mockPhoneNumber = '**********';
		const mockCountryCode = '91';
		const mockBrandId = 'brand-uuid-123';
		const mockGlobalOwnerId = 'global-owner-uuid-123';
		const mockOwnerBrandId = 'owner-brand-uuid-123';
		const mockToken = 'jwt-token-123';

		const mockBrand = {
			id: mockBrandId,
			name: 'Test Brand'
		} as Brand;

		const mockGlobalOwner = {
			id: mockGlobalOwnerId,
			phoneNumber: mockPhoneNumber,
			countryCode: mockCountryCode
		} as GlobalOwner;

		const mockOwnerBrand = {
			id: mockOwnerBrandId,
			firstName: 'Test',
			lastName: 'Owner',
			email: '<EMAIL>',
			globalOwnerId: mockGlobalOwnerId,
			brandId: mockBrandId,
			version: 1,
			ownerBalance: 0,
			ownerCredits: 0,
			openingBalance: 0,
			createdAt: new Date(),
			updatedAt: new Date(),
			dummyData: null
		} as unknown as OwnerBrand;

		it('should successfully login and return token and owner info', async () => {
			// Setup mocks
			mockBrandService.getBrandById.mockResolvedValue(mockBrand);
			mockOwnersService.findGlobalOwnerByPhoneNumber.mockResolvedValue(
				mockGlobalOwner
			);
			mockOwnersService.findOwnerBrandByGlobalOwnerAndBrand.mockResolvedValue(
				mockOwnerBrand
			);
			mockJwtService.sign.mockReturnValue(mockToken);

			// Call the method
			const result = await service.directLogin({
				phoneNumber: mockPhoneNumber,
				countryCode: mockCountryCode,
				brandId: mockBrandId
			});

			// Verify results
			expect(result).toEqual({
				token: mockToken,
				owner: {
					id: mockOwnerBrandId,
					globalOwnerId: mockGlobalOwnerId,
					firstName: mockOwnerBrand.firstName,
					lastName: mockOwnerBrand.lastName,
					phoneNumber: mockGlobalOwner.phoneNumber,
					countryCode: mockGlobalOwner.countryCode,
					email: mockOwnerBrand.email,
					brandId: mockBrand.id,
					brandName: mockBrand.name
				}
			});

			// Verify service calls
			expect(mockBrandService.getBrandById).toHaveBeenCalledWith(
				mockBrandId
			);
			expect(
				mockOwnersService.findGlobalOwnerByPhoneNumber
			).toHaveBeenCalledWith(mockPhoneNumber);
			expect(
				mockOwnersService.findOwnerBrandByGlobalOwnerAndBrand
			).toHaveBeenCalledWith(mockGlobalOwnerId, mockBrandId);
			expect(mockJwtService.sign).toHaveBeenCalledWith({
				sub: mockOwnerBrandId,
				phoneNumber: mockGlobalOwner.phoneNumber,
				role: Role.OWNER,
				brandId: mockBrand.id,
				globalOwnerId: mockGlobalOwnerId
			});
		});

		it('should create a new OwnerBrand if it does not exist', async () => {
			// Setup mocks
			mockBrandService.getBrandById.mockResolvedValue(mockBrand);
			mockOwnersService.findGlobalOwnerByPhoneNumber.mockResolvedValue(
				mockGlobalOwner
			);
			mockOwnersService.findOwnerBrandByGlobalOwnerAndBrand.mockResolvedValue(
				null
			); // No existing OwnerBrand
			mockOwnersService.findOrCreateOwnerBrand.mockResolvedValue(
				mockOwnerBrand
			);
			mockJwtService.sign.mockReturnValue(mockToken);

			// Call the method
			const result = await service.directLogin({
				phoneNumber: mockPhoneNumber,
				countryCode: mockCountryCode,
				brandId: mockBrandId
			});

			// Verify results
			expect(result).toEqual({
				token: mockToken,
				owner: expect.objectContaining({
					id: mockOwnerBrandId,
					globalOwnerId: mockGlobalOwnerId
				})
			});

			// Verify service calls
			expect(
				mockOwnersService.findOrCreateOwnerBrand
			).toHaveBeenCalledWith(
				expect.objectContaining({
					phoneNumber: mockGlobalOwner.phoneNumber,
					countryCode: mockGlobalOwner.countryCode
				}),
				mockBrandId,
				mockGlobalOwnerId
			);
		});

		it('should throw NotFoundException if brand is not found', async () => {
			// Setup mocks
			mockBrandService.getBrandById.mockResolvedValue(null);

			// Call the method and expect exception
			await expect(
				service.directLogin({
					phoneNumber: mockPhoneNumber,
					countryCode: mockCountryCode,
					brandId: mockBrandId
				})
			).rejects.toThrow(NotFoundException);
			await expect(
				service.directLogin({
					phoneNumber: mockPhoneNumber,
					countryCode: mockCountryCode,
					brandId: mockBrandId
				})
			).rejects.toThrow(`Brand with ID ${mockBrandId} not found`);
		});

		it('should throw NotFoundException if global owner is not found', async () => {
			// Setup mocks
			mockBrandService.getBrandById.mockResolvedValue(mockBrand);
			mockOwnersService.findGlobalOwnerByPhoneNumber.mockResolvedValue(
				null
			);

			// Call the method and expect exception
			await expect(
				service.directLogin({
					phoneNumber: mockPhoneNumber,
					countryCode: mockCountryCode,
					brandId: mockBrandId
				})
			).rejects.toThrow(NotFoundException);
			await expect(
				service.directLogin({
					phoneNumber: mockPhoneNumber,
					countryCode: mockCountryCode,
					brandId: mockBrandId
				})
			).rejects.toThrow(
				'Mobile number not recognized. Please check the number or contact the clinic.'
			);
		});
	});

	// --- Test cases for getClientDashboard ---
	describe('getClientDashboard', () => {
		const mockOwnerId = 'owner-uuid-123';
		const mockBrandId = 'brand-uuid-123';

		const mockOwnerWithPets = {
			owner: {
				id: mockOwnerId,
				firstName: 'Test',
				lastName: 'Owner',
				phoneNumber: '**********',
				email: '<EMAIL>',
				address: '123 Main St',
				ownerCredits: 500
			},
			patients: [
				{
					id: 'pet-1',
					patientName: 'Max',
					breed: 'Labrador',
					species: 'Dog'
				},
				{
					id: 'pet-2',
					patientName: 'Whiskers',
					breed: 'Persian',
					species: 'Cat'
				}
			]
		};

		it('should return dashboard data successfully', async () => {
			// Setup mocks
			mockOwnersService.getAllOwnerPatients.mockResolvedValue(
				mockOwnerWithPets
			);
			mockPatientsService.computeOwnerBalance.mockResolvedValue(1000);

			// Call the method
			const result = await service.getClientDashboard(
				mockOwnerId,
				mockBrandId
			);

			// Verify results
			expect(result).toEqual({
				owner: {
					id: mockOwnerId,
					firstName: 'Test',
					lastName: 'Owner',
					fullName: 'Test Owner',
					phoneNumber: '**********',
					email: '<EMAIL>',
					address: '123 Main St',
					ownerBalance: 1000,
					ownerCredits: 500
				},
				pets: [
					{
						id: 'pet-1',
						name: 'Max',
						breed: 'Labrador',
						species: 'Dog'
					},
					{
						id: 'pet-2',
						name: 'Whiskers',
						breed: 'Persian',
						species: 'Cat'
					}
				]
			});

			// Verify service calls
			expect(mockOwnersService.getAllOwnerPatients).toHaveBeenCalledWith(
				mockOwnerId,
				mockBrandId
			);
			expect(
				mockPatientsService.computeOwnerBalance
			).toHaveBeenCalledWith(mockOwnerId);
		});

		it('should handle missing optional fields', async () => {
			// Setup mocks with missing optional fields
			const mockOwnerWithMissingFields = {
				owner: {
					id: mockOwnerId,
					firstName: 'Test',
					lastName: 'Owner',
					phoneNumber: '**********',
					ownerCredits: 500
					// email and address are missing
				},
				patients: [
					{
						id: 'pet-1',
						patientName: 'Max',
						breed: 'Labrador'
						// species is missing
					}
				]
			};

			mockOwnersService.getAllOwnerPatients.mockResolvedValue(
				mockOwnerWithMissingFields
			);
			mockPatientsService.computeOwnerBalance.mockResolvedValue(1000);

			// Call the method
			const result = await service.getClientDashboard(
				mockOwnerId,
				mockBrandId
			);

			// Verify results
			expect(result).toEqual({
				owner: {
					id: mockOwnerId,
					firstName: 'Test',
					lastName: 'Owner',
					fullName: 'Test Owner',
					phoneNumber: '**********',
					email: '',
					address: '',
					ownerBalance: 1000,
					ownerCredits: 500
				},
				pets: [
					{
						id: 'pet-1',
						name: 'Max',
						breed: 'Labrador',
						species: ''
					}
				]
			});
		});

		it('should throw NotFoundException if owner is not found', async () => {
			// Setup mocks
			mockOwnersService.getAllOwnerPatients.mockResolvedValue(null);

			// Call the method and expect exception
			await expect(
				service.getClientDashboard(mockOwnerId, mockBrandId)
			).rejects.toThrow(NotFoundException);
			await expect(
				service.getClientDashboard(mockOwnerId, mockBrandId)
			).rejects.toThrow(`Owner with ID ${mockOwnerId} not found`);
		});

		it('should propagate errors from services', async () => {
			// Setup mocks
			const mockError = new Error('Service error');
			mockOwnersService.getAllOwnerPatients.mockRejectedValue(mockError);

			// Call the method and expect exception
			await expect(
				service.getClientDashboard(mockOwnerId, mockBrandId)
			).rejects.toThrow(mockError);

			// Verify logger was called
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting client dashboard',
				expect.objectContaining({
					error: mockError,
					ownerId: mockOwnerId
				})
			);
		});
	});

	// --- Test cases for getClientAppointments ---
	describe('getClientAppointments', () => {
		const mockOwnerId = 'owner-uuid-123';
		const mockBrandId = 'brand-uuid-123';

		const mockOwnerWithPets = {
			owner: {
				id: mockOwnerId,
				firstName: 'Test',
				lastName: 'Owner'
			},
			patients: [
				{
					id: 'pet-1',
					patientName: 'Max'
				},
				{
					id: 'pet-2',
					patientName: 'Whiskers'
				}
			]
		};

		const mockAppointment1 = {
			id: 'appt-1',
			date: new Date('2024-09-20'),
			startTime: new Date('2024-09-20T10:00:00'),
			endTime: new Date('2024-09-20T10:30:00'),
			status: EnumAppointmentStatus.Scheduled,
			mode: 'Online',
			type: EnumAppointmentType.Consultation,
			patient: {
				id: 'pet-1',
				patientName: 'Max'
			},
			clinicId: 'clinic-1',
			brandId: mockBrandId,
			patientId: 'pet-1',
			reason: 'Regular checkup',
			appointmentDoctors: [
				{
					doctorId: 'doc-1',
					clinicUser: {
						user: {
							firstName: 'John',
							lastName: 'Smith'
						}
					}
				}
			]
		} as unknown as AppointmentEntity;

		const mockAppointment2 = {
			id: 'appt-2',
			date: new Date('2024-09-10'),
			startTime: new Date('2024-09-10T14:30:00'),
			endTime: new Date('2024-09-10T15:00:00'),
			status: EnumAppointmentStatus.Completed,
			mode: 'Clinic',
			type: EnumAppointmentType.Vaccination,
			patient: {
				id: 'pet-2',
				patientName: 'Whiskers'
			},
			clinicId: 'clinic-1',
			brandId: mockBrandId,
			patientId: 'pet-2',
			reason: 'Vaccination',
			appointmentDoctors: [
				{
					doctorId: 'doc-1',
					clinicUser: {
						user: {
							firstName: 'Jane',
							lastName: 'Doe'
						}
					}
				}
			]
		} as unknown as AppointmentEntity;

		beforeEach(() => {
			// Mock the moment.now() to return a fixed date for testing
			jest.spyOn(moment, 'now').mockReturnValue(
				moment('2024-09-15').valueOf()
			);
		});

		afterEach(() => {
			jest.restoreAllMocks();
		});

		it('should return appointments successfully', async () => {
			// Setup mocks
			mockOwnersService.getAllOwnerPatients.mockResolvedValue(
				mockOwnerWithPets
			);
			mockAppointmentsService.getAppointmentsForPatient
				.mockResolvedValueOnce([mockAppointment1]) // For pet-1
				.mockResolvedValueOnce([mockAppointment2]); // For pet-2

			// Call the method
			const result = await service.getClientAppointments(
				mockOwnerId,
				mockBrandId
			);

			// Verify results
			expect(result).toEqual({
				upcoming: [
					{
						id: 'appt-1',
						date: '2024-09-20',
						startTime: '10:00:00',
						endTime: '10:30:00',
						time: '10:00 AM - 10:30 AM',
						patientName: 'Max',
						clinicId: 'clinic-1',
						doctorId: 'doc-1',
						petId: 'pet-1',
						doctorName: 'Dr. John Smith',
						mode: 'Online',
						status: 'Completed'
					}
				],
				previous: [
					{
						id: 'appt-2',
						date: '2024-09-10',
						startTime: '14:30:00',
						endTime: '15:00:00',
						time: '2:30 PM - 3:00 PM',
						patientName: 'Whiskers',
						clinicId: 'clinic-1',
						doctorId: 'doc-1',
						petId: 'pet-2',
						doctorName: 'Dr. Jane Doe',
						mode: 'Clinic',
						status: 'Completed',
						visitType: 'Vaccination'
					}
				]
			});

			// Verify service calls
			expect(mockOwnersService.getAllOwnerPatients).toHaveBeenCalledWith(
				mockOwnerId,
				mockBrandId
			);
			expect(
				mockAppointmentsService.getAppointmentsForPatient
			).toHaveBeenCalledTimes(2);
			expect(
				mockAppointmentsService.getAppointmentsForPatient
			).toHaveBeenCalledWith('pet-1', true);
			expect(
				mockAppointmentsService.getAppointmentsForPatient
			).toHaveBeenCalledWith('pet-2', true);
		});

		it('should return empty arrays if owner has no pets', async () => {
			// Setup mocks
			mockOwnersService.getAllOwnerPatients.mockResolvedValue({
				owner: { id: mockOwnerId },
				patients: [] // No pets
			});

			// Call the method
			const result = await service.getClientAppointments(
				mockOwnerId,
				mockBrandId
			);

			// Verify results
			expect(result).toEqual({
				upcoming: [],
				previous: []
			});

			// Verify service calls
			expect(
				mockAppointmentsService.getAppointmentsForPatient
			).not.toHaveBeenCalled();
		});

		it('should return empty arrays if owner is not found', async () => {
			// Setup mocks
			mockOwnersService.getAllOwnerPatients.mockResolvedValue(null);

			// Call the method
			const result = await service.getClientAppointments(
				mockOwnerId,
				mockBrandId
			);

			// Verify results
			expect(result).toEqual({
				upcoming: [],
				previous: []
			});
		});

		it('should handle appointments with missing doctor information', async () => {
			// Setup mocks
			const appointmentWithoutDoctor = {
				...mockAppointment1,
				appointmentDoctors: [] // No doctors
			} as unknown as AppointmentEntity;

			mockOwnersService.getAllOwnerPatients.mockResolvedValue(
				mockOwnerWithPets
			);
			mockAppointmentsService.getAppointmentsForPatient
				.mockResolvedValueOnce([appointmentWithoutDoctor])
				.mockResolvedValueOnce([]);

			// Call the method
			const result = await service.getClientAppointments(
				mockOwnerId,
				mockBrandId
			);

			// Verify results
			expect(result.upcoming[0].doctorName).toBe('Unknown Doctor');
		});

		it('should propagate errors from services', async () => {
			// Setup mocks
			const mockError = new Error('Service error');
			mockOwnersService.getAllOwnerPatients.mockRejectedValue(mockError);

			// Call the method and expect exception
			await expect(
				service.getClientAppointments(mockOwnerId, mockBrandId)
			).rejects.toThrow(mockError);

			// Verify logger was called
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting client appointments',
				expect.objectContaining({
					error: mockError,
					ownerId: mockOwnerId
				})
			);
		});
	});

	// --- Test cases for getClientClinics ---
	describe('getClientClinics', () => {
		const mockBrandId = 'brand-uuid-123';
		const mockClinics = [
			{ id: 'clinic-1', name: 'Clinic A' },
			{ id: 'clinic-2', name: 'Clinic B' }
		];

		it('should return clinics successfully', async () => {
			// Setup mocks
			mockQueryBuilder.getRawMany.mockResolvedValue(mockClinics);

			// Call the method
			const result = await service.getClientClinics(mockBrandId);

			// Verify results
			expect(result).toEqual(mockClinics);

			// Verify query builder calls
			expect(mockDataSource.createQueryBuilder).toHaveBeenCalled();
			expect(mockQueryBuilder.select).toHaveBeenCalledWith([
				'c.id as id',
				'c.name as name'
			]);
			expect(mockQueryBuilder.from).toHaveBeenCalledWith('clinics', 'c');
			expect(mockQueryBuilder.where).toHaveBeenCalledWith(
				'c.brand_id = :brandId',
				{ brandId: mockBrandId }
			);
			expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
				'c.name',
				'ASC'
			);
			expect(mockQueryBuilder.getRawMany).toHaveBeenCalled();
		});

		it('should propagate errors from database', async () => {
			// Setup mocks
			const mockError = new Error('Database error');
			mockQueryBuilder.getRawMany.mockRejectedValue(mockError);

			// Call the method and expect exception
			await expect(service.getClientClinics(mockBrandId)).rejects.toThrow(
				mockError
			);

			// Verify logger was called
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting client clinics',
				expect.objectContaining({
					error: mockError,
					brandId: mockBrandId
				})
			);
		});
	});

	// --- Test cases for getClientDoctors ---
	describe('getClientDoctors', () => {
		const mockBrandId = 'brand-uuid-123';
		const mockClinicId = 'clinic-uuid-456';
		const mockDoctors = [
			{ id: 'doctor-1', name: 'Dr. John Smith' },
			{ id: 'doctor-2', name: 'Dr. Jane Doe' }
		];

		it('should return doctors successfully without clinic filter', async () => {
			// Setup mocks
			mockQueryBuilder.getRawMany.mockResolvedValue(mockDoctors);

			// Call the method
			const result = await service.getClientDoctors(mockBrandId);

			// Verify results
			expect(result).toEqual(mockDoctors);

			// Verify query builder calls
			expect(mockDataSource.createQueryBuilder).toHaveBeenCalled();
			expect(mockQueryBuilder.select).toHaveBeenCalledWith([
				'cu.id as id',
				"CONCAT(u.first_name, ' ', u.last_name) as name"
			]);
			expect(mockQueryBuilder.from).toHaveBeenCalledWith(
				'clinic_users',
				'cu'
			);
			expect(mockQueryBuilder.innerJoin).toHaveBeenCalledTimes(3);
			expect(mockQueryBuilder.where).toHaveBeenCalledWith(
				'c.brand_id = :brandId',
				{ brandId: mockBrandId }
			);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'r.name != :superAdminRole',
				{ superAdminRole: Role.SUPER_ADMIN }
			);
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'cu.id IS NOT NULL'
			);
			expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
				'name',
				'ASC'
			);
			expect(mockQueryBuilder.getRawMany).toHaveBeenCalled();
		});

		it('should apply clinic filter when clinicId is provided', async () => {
			// Setup mocks
			mockQueryBuilder.getRawMany.mockResolvedValue(mockDoctors);

			// Call the method
			const result = await service.getClientDoctors(
				mockBrandId,
				mockClinicId
			);

			// Verify results
			expect(result).toEqual(mockDoctors);

			// Verify clinic filter was applied
			expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
				'cu.clinic_id = :clinicId',
				{ clinicId: mockClinicId }
			);
		});

		it('should propagate errors from database', async () => {
			// Setup mocks
			const mockError = new Error('Database error');
			mockQueryBuilder.getRawMany.mockRejectedValue(mockError);

			// Call the method and expect exception
			await expect(service.getClientDoctors(mockBrandId)).rejects.toThrow(
				mockError
			);

			// Verify logger was called
			expect(mockLogger.error).toHaveBeenCalledWith(
				'Error getting client doctors',
				expect.objectContaining({
					error: mockError,
					brandId: mockBrandId
				})
			);
		});
	});

	// --- Test cases for mapAppointmentStatus (private method) ---
	describe('mapAppointmentStatus', () => {
		it('should map appointment statuses correctly', () => {
			// Access the private method using type casting
			const mapAppointmentStatus = (
				service as any
			).mapAppointmentStatus.bind(service);

			// Test each status mapping
			expect(mapAppointmentStatus(EnumAppointmentStatus.Completed)).toBe(
				'Completed'
			);
			expect(mapAppointmentStatus(EnumAppointmentStatus.Missed)).toBe(
				'Missed'
			);
			expect(mapAppointmentStatus(EnumAppointmentStatus.Cancelled)).toBe(
				'Cancelled'
			);

			// Test default case
			expect(mapAppointmentStatus('Unknown')).toBe('Completed');
			expect(mapAppointmentStatus('')).toBe('Completed');
			expect(mapAppointmentStatus(null)).toBe('Completed');
		});
	});
});
