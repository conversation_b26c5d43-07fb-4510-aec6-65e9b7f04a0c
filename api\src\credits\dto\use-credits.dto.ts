import { ApiProperty } from '@nestjs/swagger';
import {
	IsNotEmpty,
	IsNumber,
	IsObject,
	IsOptional,
	IsString,
	IsUUID
} from 'class-validator';

export class UseCreditsDto {
	@ApiProperty({
		description: 'The owner ID',
		example: 'uuid'
	})
	@IsUUID()
	@IsNotEmpty()
	ownerId!: string;

	@ApiProperty({
		description: 'Amount of credits to use',
		example: 100
	})
	@IsNumber()
	@IsNotEmpty()
	amount!: number;

	@ApiProperty({
		description: 'Invoice ID for which credits are being used',
		example: 'uuid'
	})
	@IsUUID()
	@IsNotEmpty()
	invoiceId!: string;

	@ApiProperty({
		description: 'User context information',
		example: { clinicId: 'uuid', brandId: 'uuid', userId: 'uuid' }
	})
	@IsObject()
	@IsOptional()
	user?: { clinicId: string; brandId: string; userId: string };
}

export class AddCreditsDto {
	@ApiProperty({
		description: 'The owner ID',
		example: 'uuid'
	})
	@IsUUID()
	@IsNotEmpty()
	ownerId!: string;

	@ApiProperty({
		description: 'Amount of credits to add',
		example: 100
	})
	@IsNumber()
	@IsNotEmpty()
	amount!: number;

	@ApiProperty({
		description: 'Description of why credits are being added',
		example: 'Refund for cancelled appointment'
	})
	@IsString()
	@IsNotEmpty()
	description!: string;

	@ApiProperty({
		description: 'Additional metadata related to the credit transaction',
		example: { reason: 'customer satisfaction', approvedBy: 'Jane Doe' },
		required: false
	})
	@IsObject()
	@IsOptional()
	metadata?: Record<string, any>;
}

export class DuplicateCheckDto {
	@ApiProperty({
		description: 'Invoice ID to check for duplicate payments',
		example: 'uuid'
	})
	@IsUUID()
	@IsNotEmpty()
	invoiceId!: string;

	@ApiProperty({
		description: 'Amount of the payment to check for duplicates',
		example: 100
	})
	@IsNumber()
	@IsNotEmpty()
	amount!: number;
}
