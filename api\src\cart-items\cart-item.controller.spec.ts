import { Test, TestingModule } from '@nestjs/testing';
import { CartItemController } from './cart-item.controller';
import { CartItemService } from './cart-item.service';
import { CreateCartItemDto } from './dto/create-cart-item.dto';
import { CartItemEntity } from './entities/cart-item.entity';
import { <PERSON>Logger } from '../utils/logger/winston-logger.service';
import { BadRequestException, HttpException, HttpStatus } from '@nestjs/common';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
import { RoleService } from '../roles/role.service';
import { BulkInsertIntoCartItemDto } from './dto/bulkInsert-cartItem.dto';
import { uuidv4 } from 'uuidv7';

describe('CartItemController', () => {
	let controller: CartItemController;
	let cartService: jest.Mocked<CartItemService>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [CartItemController],
			providers: [
				{
					provide: CartItemService,
					useValue: {
						createCartItem: jest.fn(),
						deleteCartItem: jest.fn(),
						getCartItemDetailsByAppointmentId: jest.fn(),
						getCartItemDetailsByCartId: jest.fn(),
						updateCartItemDetails: jest.fn(),
						createCartAndBulkInsert: jest.fn()
					}
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<CartItemController>(CartItemController);
		cartService = module.get(CartItemService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('Create a cart item - POST /cart', () => {
		const mockInputCarDto: CreateCartItemDto = {
			appointmentId: 'appointment_uuid'
		};

		const mockOutputEntity: CartItemEntity & {
			lineItemId: string | undefined;
		} = {
			id: 'cart_item_uuid',
			appointmentId: 'appointment_uuid',
			productId: 'product_uuid',
			type: '',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: 'cart_uuid',
			lineItemId: 'line_item_uuid'
		};

		it('should have a createCartItem function', () => {
			expect(controller.createCartItem).toBeDefined();
		});

		it('should create a cart and return a cart id', async () => {
			const mockReq = {
				user: {
					id: 'user_uuid',
					clinicId: 'clinic_uuid',
					brandId: 'brand_uuid'
				}
			};

			cartService.createCartItem.mockResolvedValue(mockOutputEntity);
			const result = await controller.createCartItem(
				mockInputCarDto,
				mockReq
			);
			expect(result).toEqual(mockOutputEntity);
			expect(cartService.createCartItem).toHaveBeenCalledWith(
				mockInputCarDto,
				{
					clinicId: mockReq.user.clinicId,
					brandId: mockReq.user.brandId,
					userId: mockReq.user.id
				}
			);
		});

		it('should handle errors and throw HttpException', async () => {
			const mockReq = {
				user: {
					id: 'user_uuid',
					clinicId: 'clinic_uuid',
					brandId: 'brand_uuid'
				}
			};

			const errorMessage = 'Service error';
			cartService.createCartItem.mockRejectedValue(
				new Error(errorMessage)
			);

			await expect(
				controller.createCartItem(mockInputCarDto, mockReq)
			).rejects.toThrow(HttpException);

			// Verify the error was logged
			expect(cartService.createCartItem).toHaveBeenCalledWith(
				mockInputCarDto,
				{
					clinicId: mockReq.user.clinicId,
					brandId: mockReq.user.brandId,
					userId: mockReq.user.id
				}
			);
		});
	});

	describe('Delete a cart item - DELETE /cart-items/:cartId', () => {
		it('should have deleteCartItem function', () => {
			expect(controller.deleteCartItem).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the deleteCart call', async () => {
			const cartId = 'cart_uuid';
			const error = new HttpException(
				`This cart with ${cartId} doesn't exist`,
				HttpStatus.NOT_FOUND
			);
			cartService.deleteCartItem.mockRejectedValue(error);
			await expect(controller.deleteCartItem(cartId)).rejects.toThrow(
				`This cart with ${cartId} doesn't exist`
			);
			expect(cartService.deleteCartItem).toHaveBeenCalledWith(
				cartId,
				undefined
			);
		});

		it('should delete an appointment for the given appointment id', async () => {
			const cartId = 'cart_uuid';
			const mockDeletedCart = { status: true };
			cartService.deleteCartItem.mockResolvedValue(mockDeletedCart);
			const result = await controller.deleteCartItem(cartId);
			expect(result).toEqual(mockDeletedCart);
			expect(cartService.deleteCartItem).toHaveBeenCalledWith(
				cartId,
				undefined
			);
		});

		it('should handle delete with source parameter', async () => {
			const cartId = 'cart_uuid';
			const source = 'api';
			const mockDeletedCart = { status: true };
			cartService.deleteCartItem.mockResolvedValue(mockDeletedCart);
			const result = await controller.deleteCartItem(cartId, source);
			expect(result).toEqual(mockDeletedCart);
			expect(cartService.deleteCartItem).toHaveBeenCalledWith(
				cartId,
				source
			);
		});

		it('should handle errors and throw HttpException', async () => {
			const cartId = 'cart_uuid';
			const errorMessage = 'Service error';
			cartService.deleteCartItem.mockRejectedValue(
				new Error(errorMessage)
			);

			await expect(controller.deleteCartItem(cartId)).rejects.toThrow(
				HttpException
			);
			expect(cartService.deleteCartItem).toHaveBeenCalledWith(
				cartId,
				undefined
			);
		});
	});

	describe('Get cart item details - GET /cart-items', () => {
		const appointmentId = 'appointment_id';
		const cartId = 'cart_id';

		const mockOutputEntity: CartItemEntity = {
			id: 'cart_item_uuid',
			appointmentId: 'appointment_uuid',
			productId: 'product_uuid',
			type: '',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			cartId: 'cart_uuid'
		};

		it('should have a getCartItemDetails function', () => {
			expect(controller.getCartItemDetails).toBeDefined();
		});

		it('should get cart items by appointmentId', async () => {
			const mockInputCartList: CartItemEntity[] = [mockOutputEntity];

			cartService.getCartItemDetailsByAppointmentId.mockResolvedValue(
				mockInputCartList
			);
			const result = await controller.getCartItemDetails(appointmentId);
			expect(result).toEqual(mockInputCartList);
			expect(
				cartService.getCartItemDetailsByAppointmentId
			).toHaveBeenCalledWith(appointmentId);
		});

		it('should get cart items by cartId', async () => {
			const mockInputCartList: CartItemEntity[] = [mockOutputEntity];

			cartService.getCartItemDetailsByCartId.mockResolvedValue(
				mockInputCartList
			);
			const result = await controller.getCartItemDetails(
				undefined,
				cartId
			);
			expect(result).toEqual(mockInputCartList);
			expect(cartService.getCartItemDetailsByCartId).toHaveBeenCalledWith(
				cartId
			);
		});

		it('should throw BadRequestException when neither appointmentId nor cartId provided', async () => {
			await expect(controller.getCartItemDetails()).rejects.toThrow(
				BadRequestException
			);
		});

		it('should throw an exception for any kind of failure in the getCartItemDetails call', async () => {
			const error = new HttpException(
				'Error fetching cart list by appointment id',
				HttpStatus.NOT_FOUND
			);

			cartService.getCartItemDetailsByAppointmentId.mockRejectedValue(
				error
			);

			await expect(
				controller.getCartItemDetails(appointmentId)
			).rejects.toThrow('Error fetching cart list by appointment id');
			expect(
				cartService.getCartItemDetailsByAppointmentId
			).toHaveBeenCalledWith(appointmentId);
		});

		it('should return the list ofites in a cart for a given appointment id', async () => {
			const mockInputCartList: CartItemEntity[] = [mockOutputEntity];

			cartService.getCartItemDetailsByAppointmentId.mockResolvedValue(
				mockInputCartList
			);
			const result = await controller.getCartItemDetails(appointmentId);
			expect(result).toEqual(mockInputCartList);
			expect(
				cartService.getCartItemDetailsByAppointmentId
			).toHaveBeenCalledWith(appointmentId);
		});
	});

	describe('Update cart item details for a cart id - PUT /cart-items/:cartId/details', () => {
		const mockCartId = 'cart_uuid';
		const mockUpdateCartDto: UpdateCartItemDto = {
			quantity: 10
		};

		it('should have updateCartItemDetails function', () => {
			expect(controller.updateCartItemDetails).toBeDefined();
		});

		it('should throw an exception for any kind of failure in the updateCartItemDetails call', async () => {
			const error = new HttpException(
				`This cart with ${mockCartId} doesn't exist`,
				HttpStatus.NOT_FOUND
			);
			cartService.updateCartItemDetails.mockRejectedValue(error);
			await expect(
				controller.updateCartItemDetails(mockCartId, mockUpdateCartDto)
			).rejects.toThrow(`This cart with ${mockCartId} doesn't exist`);
			expect(cartService.updateCartItemDetails).toHaveBeenCalledWith(
				mockCartId,
				mockUpdateCartDto,
				undefined
			);
		});

		it('should update the cart for the given details', async () => {
			const mockUpdatedCart = { status: true };

			cartService.updateCartItemDetails.mockResolvedValue(
				mockUpdatedCart
			);

			const result = await controller.updateCartItemDetails(
				mockCartId,
				mockUpdateCartDto
			);

			expect(result).toEqual(mockUpdatedCart);
			expect(cartService.updateCartItemDetails).toHaveBeenCalledWith(
				mockCartId,
				mockUpdateCartDto,
				undefined
			);
		});

		it('should handle update with source parameter', async () => {
			const source = 'api';
			const mockUpdatedCart = { status: true };

			cartService.updateCartItemDetails.mockResolvedValue(
				mockUpdatedCart
			);

			const result = await controller.updateCartItemDetails(
				mockCartId,
				mockUpdateCartDto,
				source
			);

			expect(result).toEqual(mockUpdatedCart);
			expect(cartService.updateCartItemDetails).toHaveBeenCalledWith(
				mockCartId,
				mockUpdateCartDto,
				source
			);
		});

		it('should handle errors and throw HttpException', async () => {
			const errorMessage = 'Service error';
			cartService.updateCartItemDetails.mockRejectedValue(
				new Error(errorMessage)
			);

			await expect(
				controller.updateCartItemDetails(mockCartId, mockUpdateCartDto)
			).rejects.toThrow(HttpException);
		});
	});

	describe('createCartAndBulkInsert - POST /cart-items/bulkInsert', () => {
		const mockDto: BulkInsertIntoCartItemDto = {
			appointmentId: 'appointment_uuid',
			prescriptionIds: ['prescription1', 'prescription2'],
			addedFrom: 'api'
		};

		it('should be defined', () => {
			expect(controller.createCartAndBulkInsert).toBeDefined();
		});

		it('should create cart and bulk insert items successfully', async () => {
			const mockOutputEntities = [
				{
					id: 'cart_item_uuid_1',
					appointmentId: 'appointment_uuid',
					cartId: 'cart_uuid',
					prescriptionId: 'prescription1',
					type: 'Medication',
					isAddedToCart: false,
					addedFrom: 'api',
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: '',
					updatedBy: ''
				},
				{
					id: 'cart_item_uuid_2',
					appointmentId: 'appointment_uuid',
					cartId: 'cart_uuid',
					prescriptionId: 'prescription2',
					type: 'Medication',
					isAddedToCart: false,
					addedFrom: 'api',
					createdAt: new Date(),
					updatedAt: new Date(),
					createdBy: '',
					updatedBy: ''
				}
			];

			cartService.createCartAndBulkInsert.mockResolvedValue(
				mockOutputEntities
			);

			const result = await controller.createCartAndBulkInsert(mockDto);
			expect(result).toEqual(mockOutputEntities);
			expect(cartService.createCartAndBulkInsert).toHaveBeenCalledWith(
				mockDto
			);
		});

		it('should throw BadRequestException if something goes wrong', async () => {
			cartService.createCartAndBulkInsert.mockRejectedValue(
				new Error('Service error')
			);

			await expect(
				controller.createCartAndBulkInsert(mockDto)
			).rejects.toThrow(BadRequestException);
			expect(cartService.createCartAndBulkInsert).toHaveBeenCalledWith(
				mockDto
			);
		});

		it('should handle empty prescription ids', async () => {
			const emptyDto: BulkInsertIntoCartItemDto = {
				appointmentId: 'appointment_uuid',
				prescriptionIds: [],
				addedFrom: 'api'
			};

			cartService.createCartAndBulkInsert.mockResolvedValue([]);

			const result = await controller.createCartAndBulkInsert(emptyDto);
			expect(result).toEqual([]);
			expect(cartService.createCartAndBulkInsert).toHaveBeenCalledWith(
				emptyDto
			);
		});
	});
});
