import {
	Controller,
	Get,
	Post,
	Body,
	Param,
	Req,
	UseGuards,
	Query
} from '@nestjs/common';
import {
	ApiTags,
	ApiOkResponse,
	ApiBearerAuth,
	ApiOperation,
	ApiResponse,
	ApiParam,
	ApiQuery
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { CreditsService } from './credits.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { AddCreditsDto, DuplicateCheckDto } from './dto/use-credits.dto';
import { CreditTransactionEntity } from './entities/credit-transaction.entity';
import { OwnerCreditTransactionsFiltersDto } from './dto/owner-credit-transactions-filters.dto';
import { PaginatedCreditTransactionsResponseDto } from './dto/credit-transaction-response.dto';
import { CreditTransactionType } from './enums/enum-credit-transaction-type';

@ApiTags('credits')
@ApiBearerAuth()
@Controller('credits')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CreditsController extends ApiDocumentationBase {
	constructor(
		private readonly creditsService: CreditsService,
		private readonly logger: WinstonLogger
	) {
		super();
	}

	@ApiOkResponse({
		description: 'Get owner credit balance'
	})
	@Get('owner/:ownerId/balance')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get owner credit balance' })
	@ApiResponse({
		status: 200,
		description: 'Owner credit balance retrieved successfully'
	})
	async getOwnerCreditBalance(
		@Param('ownerId') ownerId: string,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Getting owner credit balance', {
				ownerId,
				clinicId: req.user.clinicId,
				brandId: req.user.brandId
			});

			const balance =
				await this.creditsService.getOwnerCreditBalance(ownerId);
			return { ownerId, balance };
		} catch (error) {
			this.logger.error('Error getting owner credit balance', {
				error,
				ownerId
			});
			throw error;
		}
	}

	@ApiOkResponse({
		description: 'Get owner credit transactions'
	})
	@Get('owner/:ownerId/transactions')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get owner credit transactions' })
	@ApiResponse({
		status: 200,
		description: 'Owner credit transactions retrieved successfully',
		type: [CreditTransactionEntity]
	})
	async getOwnerCreditTransactions(
		@Param('ownerId') ownerId: string,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Getting owner credit transactions', {
				ownerId,
				clinicId: req.user.clinicId,
				brandId: req.user.brandId
			});

			const transactions =
				await this.creditsService.getOwnerCreditTransactions(ownerId);
			return transactions;
		} catch (error) {
			this.logger.error('Error getting owner credit transactions', {
				error,
				ownerId
			});
			throw error;
		}
	}

	@ApiOkResponse({
		description: 'Get paginated owner credit transactions',
		type: PaginatedCreditTransactionsResponseDto
	})
	@Get('owner/:ownerId/transactions-paginated')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get paginated credit transactions for an owner' })
	@ApiParam({
		name: 'ownerId',
		type: 'string',
		required: true,
		description: 'Owner ID'
	})
	@ApiQuery({
		name: 'startDate',
		required: false,
		type: String,
		description: 'Start date for filtering (ISO format)'
	})
	@ApiQuery({
		name: 'endDate',
		required: false,
		type: String,
		description: 'End date for filtering (ISO format)'
	})
	@ApiQuery({
		name: 'transactionType',
		required: false,
		enum: CreditTransactionType,
		description: 'Transaction type (ADD or USE)'
	})
	@ApiQuery({
		name: 'userId',
		required: false,
		type: String,
		description:
			'Filter by user who created the transaction (can be comma-separated)'
	})
	@ApiQuery({
		name: 'searchTerm',
		required: false,
		type: String,
		description: 'Search term for filtering by description or references'
	})
	@ApiQuery({
		name: 'page',
		required: false,
		type: Number,
		description: 'Page number for pagination',
		example: 1
	})
	@ApiQuery({
		name: 'limit',
		required: false,
		type: Number,
		description: 'Number of items per page',
		example: 20
	})
	@ApiResponse({
		status: 200,
		description:
			'Paginated owner credit transactions retrieved successfully',
		type: PaginatedCreditTransactionsResponseDto
	})
	async getOwnerCreditTransactionsPaginated(
		@Param('ownerId') ownerId: string,
		@Query() filtersDto: OwnerCreditTransactionsFiltersDto,
		@Req()
		req: { user: { userId: string; clinicId: string; brandId: string } }
	): Promise<PaginatedCreditTransactionsResponseDto> {
		try {
			this.logger.log('Getting paginated owner credit transactions', {
				ownerId,
				filters: filtersDto,
				clinicId: req.user.clinicId,
				brandId: req.user.brandId,
				userId: req.user.userId
			});

			const result =
				await this.creditsService.getOwnerCreditTransactionsPaginated(
					ownerId,
					filtersDto
				);

			return result;
		} catch (error) {
			this.logger.error(
				'Error getting paginated owner credit transactions',
				{
					error,
					ownerId,
					filters: filtersDto
				}
			);
			throw error;
		}
	}

	@Post('add')
	@Roles(Role.ADMIN, Role.RECEPTIONIST, Role.DOCTOR)
	@ApiOperation({ summary: 'Add credits to owner account' })
	@ApiResponse({
		status: 200,
		description: 'Credits added successfully'
	})
	async addOwnerCredit(
		@Body() addCreditsDto: AddCreditsDto,
		@Req()
		req: { user: { userId: string; clinicId: string; brandId: string } }
	) {
		try {
			// Log user object to debug
			this.logger.log('User object for debugging', {
				userId: req.user.userId,
				exists: !!req.user.userId,
				userObj: JSON.stringify(req.user)
			});

			this.logger.log('Adding credits to owner', {
				ownerId: addCreditsDto.ownerId,
				amount: addCreditsDto.amount,
				description: addCreditsDto.description,
				metadata: addCreditsDto.metadata,
				user: req.user
			});

			const result = await this.creditsService.addOwnerCredit(
				addCreditsDto.ownerId,
				addCreditsDto.amount,
				addCreditsDto.description,
				req.user.userId,
				req.user.clinicId,
				req.user.brandId,
				addCreditsDto.metadata
			);

			return result;
		} catch (error) {
			this.logger.error('Error adding credits to owner', {
				error,
				dto: addCreditsDto
			});
			throw error;
		}
	}

	@Post('check-duplicate')
	@Roles(Role.ADMIN, Role.RECEPTIONIST, Role.DOCTOR)
	@ApiOperation({ summary: 'Check for duplicate payments' })
	@ApiResponse({
		status: 200,
		description: 'Duplicate check result'
	})
	async checkDuplicatePayment(
		@Body() duplicateCheckDto: DuplicateCheckDto,
		@Req()
		req: { user: { userId: string; clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Checking for duplicate payment', {
				invoiceId: duplicateCheckDto.invoiceId,
				amount: duplicateCheckDto.amount,
				user: req.user
			});

			const result = await this.creditsService.checkForDuplicatePayment({
				invoiceId: duplicateCheckDto.invoiceId,
				amount: duplicateCheckDto.amount,
				clinicId: req.user.clinicId,
				brandId: req.user.brandId,
				userId: req.user.userId
			});

			return { hasDuplicate: result };
		} catch (error) {
			this.logger.error('Error checking for duplicate payment', {
				error,
				dto: duplicateCheckDto
			});
			throw error;
		}
	}
}
