import { Test, TestingModule } from '@nestjs/testing';
import { CartsService } from './carts.service';
import { Repository } from 'typeorm';
import { CartEntity } from './entites/cart.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { CartItemService } from '../cart-items/cart-item.service';
import { CartItemEntity } from '../cart-items/entities/cart-item.entity';
import { AppointmentsService } from '../appointments/appointments.service';
import { EnumAppointmentType } from '../appointments/enums/enum-appointment-type';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { EnumAppointmentTriage } from '../appointments/enums/enum-appointment-triage';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';

describe('CartsService', () => {
	let service: CartsService;
	let cartRepository: jest.Mocked<Repository<CartEntity>>;
	let cartItemRepository: jest.Mocked<Repository<CartItemEntity>>;
	let appointmentsService: jest.Mocked<AppointmentsService>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				CartsService,
				{
					provide: getRepositoryToken(CartEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						softDelete: jest.fn(),
						manager: {
							softDelete: jest.fn()
						}
					}
				},
				{
					provide: getRepositoryToken(CartItemEntity),
					useValue: {
						save: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						find: jest.fn(),
						update: jest.fn()
					}
				},
				{
					provide: AppointmentsService,
					useValue: {
						findOne: jest.fn(),
						deleteAppointment: jest.fn(),
						createAppointment: jest.fn(),
						createImpromptuAppointment: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<CartsService>(CartsService);
		cartRepository = module.get(getRepositoryToken(CartEntity));
		cartItemRepository = module.get(getRepositoryToken(CartItemEntity));
		appointmentsService = module.get(AppointmentsService);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('findCartById', () => {
		const mockCartId = 'cart_uuid';
		const mockCart: CartEntity = {
			id: mockCartId,
			appointmentId: 'appointment_uuid',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'user_uuid',
			updatedBy: 'user_uuid',
			appointment: {} as AppointmentEntity,
			invoice: [] as InvoiceEntity[]
		};

		it('should find cart by id successfully', async () => {
			cartRepository.findOne.mockResolvedValue(mockCart);

			const result = await service.findCartById(mockCartId);

			expect(result).toEqual(mockCart);
			expect(cartRepository.findOne).toHaveBeenCalledWith({
				where: { id: mockCartId }
			});
		});

		it('should return null when cart is not found', async () => {
			cartRepository.findOne.mockResolvedValue(null);

			const result = await service.findCartById(mockCartId);

			expect(result).toBeNull();
			expect(cartRepository.findOne).toHaveBeenCalledWith({
				where: { id: mockCartId }
			});
		});
	});

	describe('Create a cart', () => {
		const mockAppointmentId = 'appointment_uuid';
		const mockCartId = 'cart_uuid';
		const mockOutputEntity: CartEntity = {
			id: mockCartId,
			appointmentId: mockAppointmentId,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			updatedBy: '',
			appointment: {} as AppointmentEntity,
			invoice: [] as InvoiceEntity[]
		};

		it('should have createCart function', () => {
			expect(service.createCart).toBeDefined();
		});

		it('should return existing cart when cartId is provided and exists', async () => {
			cartRepository.findOne.mockResolvedValue(mockOutputEntity);

			const result = await service.createCart(
				mockAppointmentId,
				mockCartId
			);

			expect(result).toEqual(mockOutputEntity);
			expect(cartRepository.findOne).toHaveBeenCalledWith({
				where: { id: mockCartId }
			});
		});

		it('should check for cart and return the cart if present', async () => {
			// When appointmentId is provided directly, service only makes one call to findOne
			cartRepository.findOne.mockResolvedValue(mockOutputEntity);

			const result = await service.createCart(mockAppointmentId);

			expect(result).toEqual(mockOutputEntity);
			expect(cartRepository.findOne).toHaveBeenCalledWith({
				where: { appointmentId: mockAppointmentId }
			});
		});

		it('should create a new cart', async () => {
			cartRepository.findOne.mockResolvedValue(null);
			cartRepository.save.mockResolvedValue(mockOutputEntity);

			const result = await service.createCart(mockAppointmentId);

			expect(result).toEqual(mockOutputEntity);
			expect(cartRepository.findOne).toHaveBeenCalledWith({
				where: { appointmentId: mockAppointmentId }
			});
			expect(cartRepository.save).toHaveBeenCalledWith({
				appointmentId: mockAppointmentId
			});
		});

		it('should create impromptu appointment when no appointmentId but impromptu data provided', async () => {
			const impromptuData = {
				impromptu: true,
				patientId: 'patient_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				userId: 'user_uuid'
			};

			const mockImpromptuAppointment = {
				id: 'impromptu_appointment_uuid',
				type: EnumAppointmentType.Impromptu,
				status: EnumAppointmentStatus.Completed
			} as Partial<AppointmentEntity> as AppointmentEntity;

			const expectedCartWithImpromptuId = {
				...mockOutputEntity,
				appointmentId: mockImpromptuAppointment.id
			};

			cartRepository.findOne.mockResolvedValue(null); // No existing cart
			appointmentsService.createImpromptuAppointment.mockResolvedValue(
				mockImpromptuAppointment
			);
			cartRepository.save.mockResolvedValue(expectedCartWithImpromptuId);

			const result = await service.createCart(
				undefined,
				undefined,
				impromptuData
			);

			expect(result).toEqual(expectedCartWithImpromptuId);
			expect(
				appointmentsService.createImpromptuAppointment
			).toHaveBeenCalledWith({
				clinicId: impromptuData.clinicId,
				patientId: impromptuData.patientId,
				brandId: impromptuData.brandId,
				date: expect.any(Date),
				startTime: expect.any(Date),
				endTime: expect.any(Date),
				reason: 'Impromptu',
				type: EnumAppointmentType.Impromptu,
				status: EnumAppointmentStatus.Completed,
				createdBy: impromptuData.userId
			});
			expect(cartRepository.save).toHaveBeenCalledWith({
				appointmentId: mockImpromptuAppointment.id
			});
		});

		it('should not create impromptu appointment when patientId is missing', async () => {
			const impromptuData = {
				impromptu: true,
				patientId: '', // Empty patientId
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				userId: 'user_uuid'
			};

			cartRepository.findOne.mockResolvedValue(null);
			cartRepository.save.mockResolvedValue(mockOutputEntity);

			const result = await service.createCart(
				undefined,
				undefined,
				impromptuData
			);

			expect(
				appointmentsService.createImpromptuAppointment
			).not.toHaveBeenCalled();
			expect(cartRepository.save).toHaveBeenCalledWith({
				appointmentId: undefined
			});
		});

		it('should not create impromptu appointment when impromptu flag is false', async () => {
			const impromptuData = {
				impromptu: false,
				patientId: 'patient_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				userId: 'user_uuid'
			};

			cartRepository.findOne.mockResolvedValue(null);
			cartRepository.save.mockResolvedValue(mockOutputEntity);

			const result = await service.createCart(
				undefined,
				undefined,
				impromptuData
			);

			expect(
				appointmentsService.createImpromptuAppointment
			).not.toHaveBeenCalled();
			expect(cartRepository.save).toHaveBeenCalledWith({
				appointmentId: undefined
			});
		});
	});

	describe('deleteCart', () => {
		const mockCartId = 'cart_uuid';
		const mockAppointmentId = 'appointment_uuid';
		const mockCart: CartEntity = {
			id: mockCartId,
			appointmentId: mockAppointmentId,
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: 'user_uuid',
			updatedBy: 'user_uuid',
			appointment: {} as AppointmentEntity,
			invoice: [] as InvoiceEntity[]
		};

		it('should have deleteCart function', () => {
			expect(service.deleteCart).toBeDefined();
		});

		it('should delete cart with associated impromptu appointment', async () => {
			const mockImpromptuAppointment = {
				id: mockAppointmentId,
				type: EnumAppointmentType.Impromptu
			};

			cartRepository.findOne.mockResolvedValue(mockCart);
			// Mock the private repository access using bracket notation
			const mockAppointmentRepository = {
				findOne: jest.fn().mockResolvedValue(mockImpromptuAppointment),
				delete: jest.fn().mockResolvedValue(undefined)
			} as any;
			const mockAppointmentDetailsRepository = {
				delete: jest.fn().mockResolvedValue(undefined)
			} as any;
			(appointmentsService as any)['appointmentRepository'] =
				mockAppointmentRepository;
			(appointmentsService as any)['appointmentDetailsRepository'] =
				mockAppointmentDetailsRepository;
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: {}
			});
			cartRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

			const result = await service.deleteCart(mockCartId);

			expect(result).toEqual({
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) deleted successfully'
			});
			expect(
				appointmentsService['appointmentDetailsRepository'].delete
			).toHaveBeenCalledWith({
				appointmentId: mockAppointmentId
			});
			expect(
				appointmentsService['appointmentRepository'].delete
			).toHaveBeenCalledWith(mockAppointmentId);
			expect(cartItemRepository.delete).toHaveBeenCalledWith({
				cartId: mockCartId
			});
			expect(cartRepository.delete).toHaveBeenCalledWith(mockCartId);
		});

		it('should delete cart without affecting non-impromptu appointment', async () => {
			const mockRegularAppointment = {
				id: mockAppointmentId,
				type: EnumAppointmentType.Consultation // Not impromptu
			};

			cartRepository.findOne.mockResolvedValue(mockCart);
			// Mock the private repository access using bracket notation
			const mockAppointmentRepository2 = {
				findOne: jest.fn().mockResolvedValue(mockRegularAppointment),
				delete: jest.fn()
			} as any;
			const mockAppointmentDetailsRepository2 = {
				delete: jest.fn()
			} as any;
			(appointmentsService as any)['appointmentRepository'] =
				mockAppointmentRepository2;
			(appointmentsService as any)['appointmentDetailsRepository'] =
				mockAppointmentDetailsRepository2;
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: {}
			});
			cartRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

			const result = await service.deleteCart(mockCartId);

			expect(result).toEqual({
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) deleted successfully'
			});
			expect(
				appointmentsService['appointmentDetailsRepository'].delete
			).not.toHaveBeenCalled();
			expect(
				appointmentsService['appointmentRepository'].delete
			).not.toHaveBeenCalled();
			expect(cartItemRepository.delete).toHaveBeenCalledWith({
				cartId: mockCartId
			});
			expect(cartRepository.delete).toHaveBeenCalledWith(mockCartId);
		});

		it('should soft delete cart when softDelete flag is true', async () => {
			const mockEntityManager = {
				softDelete: jest.fn()
			};

			cartRepository.findOne.mockResolvedValue(mockCart);
			// Mock the private repository access using bracket notation
			const mockAppointmentRepository4 = {
				findOne: jest.fn().mockResolvedValue(null)
			} as any;
			(appointmentsService as any)['appointmentRepository'] =
				mockAppointmentRepository4;
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: {}
			});

			const result = await service.deleteCart(
				mockCartId,
				true,
				mockEntityManager
			);

			expect(result).toEqual({
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) soft deleted successfully'
			});
			expect(mockEntityManager.softDelete).toHaveBeenCalledWith(
				CartEntity,
				mockCartId
			);
			expect(cartItemRepository.delete).toHaveBeenCalledWith({
				cartId: mockCartId
			});
		});

		it('should delete cart without appointment', async () => {
			const mockCartWithoutAppointment: CartEntity = {
				...mockCart,
				appointmentId: undefined
			};

			// Set up mock repository for this test
			const mockAppointmentRepository5 = {
				findOne: jest.fn()
			} as any;
			(appointmentsService as any)['appointmentRepository'] =
				mockAppointmentRepository5;

			cartRepository.findOne.mockResolvedValue(
				mockCartWithoutAppointment
			);
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: {}
			});
			cartRepository.delete.mockResolvedValue({ affected: 1, raw: {} });

			const result = await service.deleteCart(mockCartId);

			expect(result).toEqual({
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) deleted successfully'
			});
			expect(
				appointmentsService['appointmentRepository'].findOne
			).not.toHaveBeenCalled();
			expect(cartItemRepository.delete).toHaveBeenCalledWith({
				cartId: mockCartId
			});
			expect(cartRepository.delete).toHaveBeenCalledWith(mockCartId);
		});

		it('should handle cart not found gracefully', async () => {
			cartRepository.findOne.mockResolvedValue(null);
			cartItemRepository.delete.mockResolvedValue({
				affected: 0,
				raw: {}
			});
			cartRepository.delete.mockResolvedValue({ affected: 0, raw: {} });

			const result = await service.deleteCart(mockCartId);

			expect(result).toEqual({
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) deleted successfully'
			});
			expect(cartItemRepository.delete).toHaveBeenCalledWith({
				cartId: mockCartId
			});
			expect(cartRepository.delete).toHaveBeenCalledWith(mockCartId);
		});

		it('should use repository manager when no entity manager is provided', async () => {
			cartRepository.findOne.mockResolvedValue(mockCart);
			// Mock the private repository access using bracket notation
			const mockAppointmentRepository3 = {
				findOne: jest.fn().mockResolvedValue(null)
			} as any;
			(appointmentsService as any)['appointmentRepository'] =
				mockAppointmentRepository3;
			cartItemRepository.delete.mockResolvedValue({
				affected: 1,
				raw: {}
			});
			(cartRepository.manager.softDelete as any) = jest
				.fn()
				.mockResolvedValue(undefined);

			const result = await service.deleteCart(mockCartId, true); // No entity manager provided

			expect(result).toEqual({
				success: true,
				message:
					'Cart, associated items, and impromptu appointment (if any) soft deleted successfully'
			});
			expect(cartRepository.manager.softDelete).toHaveBeenCalledWith(
				CartEntity,
				mockCartId
			);
		});
	});
});
