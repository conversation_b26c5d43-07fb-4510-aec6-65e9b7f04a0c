import {
	<PERSON><PERSON><PERSON>,
	<PERSON>umn,
	PrimaryGenerated<PERSON><PERSON>umn,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn
} from 'typeorm';
import { ChatRoom } from './chat-room.entity';
import { User } from '../users/entities/user.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';

@Entity('chat_room_users')
export class ChatRoomUser {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column('uuid', { name: 'user_id' })
	userId!: string;

	@Column('uuid', { name: 'chat_room_id' })
	chatRoomId!: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@ManyToOne(() => ChatRoom, chatRoom => chatRoom.users)
	@JoinColumn({ name: 'chat_room_id' })
	chatRoom!: ChatRoom;

	@ManyToOne(() => ClinicUser, user => user.chatRoomUser)
	@JoinColumn({ name: 'user_id' })
	clinicUser!: ClinicUser;
}
