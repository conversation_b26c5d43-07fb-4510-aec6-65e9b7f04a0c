import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChatRoomService } from './chat-room.service';
import { ChatRoomController } from './chat-room.controller';
import { ChatRoom } from './chat-room.entity';
import { User } from '../users/entities/user.entity';
import { ChatRoomUser } from './chat-room-users.entity';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { ChatUserSessionsService } from '../socket/chat-user-sessions.service';
import { ChatUserSessions } from '../socket/chat-user-sessions.entity';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			Chat<PERSON><PERSON>,
			User,
			ChatRoomUser,
			ChatRoomMessage
			// ChatUserSessions
		])
	],
	providers: [ChatRoomService],
	controllers: [ChatRoomController],
	exports: []
})
export class ChatRoomModule {}
