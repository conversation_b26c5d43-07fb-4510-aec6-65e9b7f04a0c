import { Test, TestingModule } from '@nestjs/testing';
import { ClinicAlertsService } from './clinic-alerts.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ClinicAlerts } from './entities/clinicAlerts.entity';
import { DeleteResult, Repository } from 'typeorm';
import { CreateClinicAlertDto } from './dto/create-clinicAlerts.dto';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { NotFoundException } from '@nestjs/common';
import { UpdateClinicAlertsDto } from './dto/update-clinicAlerts.dto';

describe('ClinicAlertsService', () => {
	let service: ClinicAlertsService;
	let clinicAlertsRepository: jest.Mocked<Repository<ClinicAlerts>>;
	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicAlertsService,
				{
					provide: getRepositoryToken(ClinicAlerts),
					useValue: {
						create: jest.fn(),
						save: jest.fn(),
						find: jest.fn(),
						findOne: jest.fn(),
						delete: jest.fn(),
						update: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicAlertsService>(ClinicAlertsService);
		clinicAlertsRepository = module.get(getRepositoryToken(ClinicAlerts));
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('create clinic-alerts', () => {
		it('should be the defined the functions', () => {
			expect(service.createClinicAlerts).toBeDefined();
		});

		it('should create clinic-alerts with proper data', async () => {
			const mockClinicAlertsDto: CreateClinicAlertDto = {
				severity: 'High',
				alertName: 'Fever',
				clinicId: 'c_1'
			};
			const mockClinicAlert: ClinicAlerts = {
				id: 'U_1',
				severity: 'High',
				alertName: 'Fever',
				clinicId: 'c_1',
				brandId: 'b_1',
				clinic: {} as ClinicEntity
			};
			clinicAlertsRepository.create(mockClinicAlertsDto);
			clinicAlertsRepository.save.mockResolvedValue(mockClinicAlert);

			const result = await service.createClinicAlerts(
				mockClinicAlertsDto,
				'b_1'
			);

			expect(clinicAlertsRepository.save).toHaveBeenCalledWith({
				...mockClinicAlertsDto,
				brandId: 'b_1'
			});
			expect(result).toEqual(mockClinicAlert);
		});

		it('should not create clinicAlerts, dto is not proper', async () => {
			const mockClinicAlertsDto: CreateClinicAlertDto = {
				severity: 'High',
				alertName: '',
				clinicId: 'c_1'
			};

			const errorMessage = new Error('something went wrong');
			clinicAlertsRepository.save.mockRejectedValue(errorMessage);

			try {
				await service.createClinicAlerts(mockClinicAlertsDto, 'b_1');
			} catch (error) {
				expect(error).toBe(errorMessage);
			}
		});
	});

	describe('get clinic-alerts', () => {
		it('should be defined the funciton', () => {
			expect(service.getClinicAlerts).toBeDefined();
		});

		it('should return the all clinic-alerts from given clinicId', async () => {
			const clinicId = 'u_1';
			const search: string = 'para';
			const mockClinicAlert: ClinicAlerts[] = [
				{
					id: 'u_1',
					severity: 'High',
					alertName: 'Fever',
					clinicId: 'c_1',
					brandId: 'b_1',
					clinic: {} as ClinicEntity
				}
			];
			clinicAlertsRepository.find.mockResolvedValue(mockClinicAlert);
			await service.getClinicAlerts(clinicId, search);
			expect(clinicAlertsRepository.find).toHaveBeenCalled();
			expect(clinicAlertsRepository.find).toHaveBeenLastCalledWith({
				where: { clinicId, alertName: search }
			});
		});

		it('should not return clinicAlerts, clinicId does not exist', async () => {
			const mockClinicId = 'x1';

			const errorMessage = new Error('something went wrong');
			clinicAlertsRepository.find.mockRejectedValue(errorMessage);

			try {
				await service.getClinicAlerts(mockClinicId);
			} catch (error) {
				expect(error).toBe(errorMessage);
			}
		});
	});

	describe('delete cliic-alerts', () => {
		it('should be defined the function', () => {
			expect(service.deleteClinicAlert).toBeDefined();
		});

		it('should delete the clinic-alerts with requested clinic-alert_id', async () => {
			const id = 'u1';
			const deleteResult: DeleteResult = {
				affected: 1,
				raw: {} as any
			};
			clinicAlertsRepository.delete.mockResolvedValue(deleteResult);

			await service.deleteClinicAlert(id);

			expect(clinicAlertsRepository.delete).toHaveBeenCalledWith(id);
			expect(clinicAlertsRepository.delete).toHaveBeenCalledTimes(1);
		});
		it('should throw a NotFoundException if the clinic-alert does not exist', async () => {
			const id = 'non_existing_id';
			const deleteResult: DeleteResult = {
				affected: 0,
				raw: {} as any
			};
			clinicAlertsRepository.delete.mockResolvedValue(deleteResult);

			await expect(service.deleteClinicAlert(id)).rejects.toThrow(
				new NotFoundException(
					`This clinic-alerts with id: ${id} doesn't exist`
				)
			);

			expect(clinicAlertsRepository.delete).toHaveBeenCalledWith(id);
			expect(clinicAlertsRepository.delete).toHaveBeenCalledTimes(1);
		});
	});

	describe('update clinic-alerts', () => {
		it('should defined the service', () => {
			expect(service.updateClinicAlerts).toBeDefined();
		});
		it('should update the clinic-alert ', async () => {
			const id = 'u-uuid';
			const updateClinicAlertDto: UpdateClinicAlertsDto = {
				alertName: 'Aggressive',
				severity: 'low'
			};
			const existingClinicAlert: ClinicAlerts = {
				id,
				alertName: 'abc',
				severity: 'medium',
				clinicId: 'c_1',
				brandId: 'b_1',
				clinic: {} as ClinicEntity
			};
			const updatedClinicAlert: ClinicAlerts = {
				...existingClinicAlert,
				...updateClinicAlertDto
			};

			clinicAlertsRepository.findOne.mockResolvedValue(
				existingClinicAlert
			);
			clinicAlertsRepository.save.mockResolvedValue(updatedClinicAlert);

			const result = await service.updateClinicAlerts(
				id,
				updateClinicAlertDto
			);

			expect(result).toEqual(updatedClinicAlert);
			expect(clinicAlertsRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
			expect(clinicAlertsRepository.save).toHaveBeenCalledWith(
				expect.objectContaining(updateClinicAlertDto)
			);
		});
		it('should throw NotFoundException if clinic-alert by requested id is not found', async () => {
			const clinicId = 'non-existent-uuid';
			const updateClinicAlertDto: UpdateClinicAlertsDto = {
				alertName: 'Aggressive',
				severity: 'low'
			};

			clinicAlertsRepository.findOne.mockResolvedValue(null);

			await expect(
				service.updateClinicAlerts(clinicId, updateClinicAlertDto)
			).rejects.toThrow(NotFoundException);
		});
	});
});
