import { Injectable, NotFoundException } from '@nestjs/common';
import { OwnersService } from '../owners/owners.service';
import { AppointmentsService } from '../appointments/appointments.service';
import { PatientsService } from '../patients/patients.service';
import {
	ClientDashboardResponseDto,
	OwnerInfoDto
} from './dto/client-dashboard-response.dto';
import {
	ClientAppointmentsResponseDto,
	UpcomingAppointmentDto,
	PreviousAppointmentDto
} from './dto/client-appointments-response.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import * as moment from 'moment-timezone';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { DataSource } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { BrandService } from '../brands/brands.service';
import { Role } from '../roles/role.enum';
import { DirectLoginDto } from './dto/direct-login.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { Repository } from 'typeorm';
import { ClinicService } from '../clinics/clinic.service';
import { v4 as uuidv4 } from 'uuid';
import { SessionService } from '../session/session.service';

// Add TimeDuration interface definition
interface TimeDuration {
	days?: number;
	hours?: number;
	minutes?: number;
}

@Injectable()
export class ClientDashboardService {
	constructor(
		private readonly ownersService: OwnersService,
		private readonly appointmentsService: AppointmentsService,
		private readonly patientsService: PatientsService,
		private readonly logger: WinstonLogger,
		private readonly dataSource: DataSource,
		private readonly jwtService: JwtService,
		private readonly sessionService: SessionService,
		private readonly brandService: BrandService,
		@InjectRepository(ClinicEntity)
		private readonly clinicRepository: Repository<ClinicEntity>,
		private readonly clinicService: ClinicService
	) {}

	/**
	 * Direct login for pet owners in the booking portal
	 * @param directLoginDto The login data (phone number, country code, brand ID)
	 * @returns Object with JWT token and owner information
	 */
	async directLogin(
		directLoginDto: DirectLoginDto
	): Promise<{ token: string; owner: any }> {
		const { phoneNumber, countryCode = '91', brandId } = directLoginDto;

		// Verify brand exists
		const brand = await this.brandService.getBrandById(brandId);
		if (!brand) {
			throw new NotFoundException(`Brand with ID ${brandId} not found`);
		}

		// Find owner by phone number
		const globalOwner =
			await this.ownersService.findGlobalOwnerByPhoneNumber(phoneNumber);

		// If owner doesn't exist globally, throw NotFoundException
		if (!globalOwner) {
			this.logger.warn(
				'Direct login attempt failed: Phone number not found globally',
				{
					phoneNumber: `+${countryCode}${phoneNumber}`,
					brandId
				}
			);
			throw new NotFoundException(
				'Mobile number not recognized. Please check the number or contact the clinic.'
			);
		}

		// Owner exists globally, find or create the brand-specific owner record
		let ownerBrand =
			await this.ownersService.findOwnerBrandByGlobalOwnerAndBrand(
				globalOwner.id,
				brand.id
			);

		// If owner exists globally but not for this specific brand, throw an error
		if (!ownerBrand) {
			this.logger.warn(
				'Direct login attempt failed: OwnerBrand record not found for this brand',
				{
					globalOwnerId: globalOwner.id,
					brandId
				}
			);
			throw new NotFoundException(
				'Mobile number not recognized. Please check the number or contact the clinic.'
			);
		}

		// Generate JWT token
		const payload = {
			sub: ownerBrand.id, // Use OwnerBrand ID as subject
			phoneNumber: globalOwner.phoneNumber, // Use global phone number
			role: Role.OWNER,
			brandId: brand.id,
			globalOwnerId: globalOwner.id
		};

		const sessionId = uuidv4();
		const token = this.jwtService.sign({ ...payload, sid: sessionId });

		// Persist session for ownerBrand user
		await this.sessionService.setUserSession(ownerBrand.id, sessionId, 60 * 60 * 24);

		// Log successful login
		this.logger.log(
			`Direct login successful for phone: +${globalOwner.countryCode || countryCode}${globalOwner.phoneNumber}, brand: ${brand.name}`
		);

		// Return token and owner information
		return {
			token,
			owner: {
				id: ownerBrand.id,
				globalOwnerId: globalOwner.id,
				firstName: ownerBrand.firstName,
				lastName: ownerBrand.lastName,
				phoneNumber: globalOwner.phoneNumber,
				countryCode: globalOwner.countryCode,
				email: ownerBrand.email,
				brandId: brand.id,
				brandName: brand.name
			}
		};
	}

	/**
	 * Get client dashboard information including owner details and pets
	 * @param ownerId The owner brand ID
	 * @param brandId The brand ID
	 * @returns ClientDashboardResponseDto
	 */
	async getClientDashboard(
		ownerId: string,
		brandId: string
	): Promise<ClientDashboardResponseDto> {
		try {
			this.logger.log('Getting client dashboard information', {
				ownerId,
				brandId
			});

			// Get owner with patients from owners service
			const ownerWithPets = await this.ownersService.getAllOwnerPatients(
				ownerId,
				brandId
			);

			if (!ownerWithPets) {
				throw new NotFoundException(
					`Owner with ID ${ownerId} not found`
				);
			}

			// Compute the owner balance dynamically
			const calculatedOwnerBalance =
				await this.patientsService.computeOwnerBalance(ownerId);

			// Format the response according to the DTO
			const ownerInfo: OwnerInfoDto = {
				id: ownerWithPets.owner.id,
				firstName: ownerWithPets.owner.firstName,
				lastName: ownerWithPets.owner.lastName,
				fullName:
					`${ownerWithPets.owner.firstName} ${ownerWithPets.owner.lastName}`.trim(),
				phoneNumber: ownerWithPets.owner.phoneNumber,
				email: ownerWithPets.owner.email || '',
				address: ownerWithPets.owner.address || '',
				ownerBalance: calculatedOwnerBalance,
				ownerCredits: ownerWithPets.owner.ownerCredits
			};

			return {
				owner: ownerInfo,
				pets: ownerWithPets.patients.map(patient => ({
					id: patient.id,
					name: patient.patientName,
					breed: patient.breed,
					species: patient.species || ''
				}))
			};
		} catch (error) {
			this.logger.error('Error getting client dashboard', {
				error,
				ownerId
			});
			throw error;
		}
	}

	/**
	 * Get all appointments for a client's pets
	 * @param ownerId The owner brand ID
	 * @param brandId The brand ID
	 * @param filters Optional filters for date, status, etc.
	 * @returns ClientAppointmentsResponseDto
	 */
	async getClientAppointments(
		ownerId: string,
		brandId: string,
		filters?: { date?: string; status?: string[] }
	): Promise<ClientAppointmentsResponseDto> {
		try {
			this.logger.log('Getting client appointments', {
				ownerId,
				brandId,
				filters
			});

			// 1. Get owner with patients from owners service
			const ownerWithPets = await this.ownersService.getAllOwnerPatients(
				ownerId,
				brandId
			);

			if (!ownerWithPets || !ownerWithPets.patients.length) {
				return { upcoming: [], previous: [] };
			}

			// 2. Get all pet IDs
			const petIds = ownerWithPets.patients.map(pet => pet.id);
			this.logger.log('Found pets for owner', {
				petCount: petIds.length,
				petIds
			});

			// 3. Get appointments for each pet and combine them
			const allAppointments = [];

			for (const petId of petIds) {
				const petAppointments =
					await this.appointmentsService.getAppointmentsForPatient(
						petId,
						true
					);
				allAppointments.push(...petAppointments);
			}

			this.logger.log('Retrieved appointments', {
				count: allAppointments.length
			});

			// 4. Format and separate into upcoming and previous
			const now = moment();
			const upcoming: UpcomingAppointmentDto[] = [];
			const previous: PreviousAppointmentDto[] = [];

			// Create a cache for clinic settings to avoid repeated database calls
			const clinicSettingsCache: Map<string, any> = new Map();

			// Helper to fetch and cache settings
			const getCachedSettings = async (clinicId: string) => {
				if (!clinicSettingsCache.has(clinicId)) {
					try {
						// Fetch the raw settings which include the TimeDuration object
						const settings =
							await this.clinicService.getClientBookingSettings(
								clinicId
							);
						clinicSettingsCache.set(clinicId, settings); // Store the raw settings
					} catch (error) {
						this.logger.error(
							'Error fetching clinic settings for modification check',
							{
								error,
								clinicId
							}
						);
						// Use default (null deadline) if fetch fails
						clinicSettingsCache.set(clinicId, {
							modificationDeadlineTime: null
						});
					}
				}
				return clinicSettingsCache.get(clinicId);
			};

			for (const appointment of allAppointments) {
				const appointmentDate = moment(appointment.date).startOf('day');
				const appointmentStartTime = moment(appointment.startTime); // Use the Date object directly
				const appointmentEndTime = moment(appointment.endTime); // Use the Date object directly

				// Get doctor name
				const doctor =
					appointment.appointmentDoctors?.length > 0
						? appointment.appointmentDoctors[0]?.clinicUser?.user
						: null;
				const doctorName = doctor
					? `Dr. ${doctor.firstName || ''} ${doctor.lastName || ''}`.trim()
					: 'Unknown Doctor';

				// Determine if the appointment can be modified or cancelled
				let canModifyOrCancel = false;
				let modificationDeadlineTime: TimeDuration | null = null; // Store the full object or null

				// Only scheduled appointments can be modified/cancelled by client
				if (appointment.status === EnumAppointmentStatus.Scheduled) {
					const clinicSettings = await getCachedSettings(
						appointment.clinicId
					);
					modificationDeadlineTime =
						clinicSettings?.modificationDeadlineTime || null;
					const modificationDeadlineMinutes = timeDurationToMinutes(
						modificationDeadlineTime
					); // Calculate minutes for check

					if (
						modificationDeadlineMinutes !== null &&
						modificationDeadlineMinutes >= 0
					) {
						const cancellationDeadline = appointmentStartTime
							.clone()
							.subtract(modificationDeadlineMinutes, 'minutes');

						// Can modify/cancel if current time is before the cancellation deadline
						canModifyOrCancel = now.isBefore(cancellationDeadline);
					} else {
						// If no deadline set (null or negative), default to allowing modification
						canModifyOrCancel = true;
					}
				}

				// Common appointment properties
				const baseAppointment = {
					id: appointment.id,
					date: appointmentDate.format('YYYY-MM-DD'),
					startTime: appointmentStartTime.utc().toISOString(), // Return as UTC timestamp string
					endTime: appointmentEndTime.utc().toISOString(), // Return as UTC timestamp string
					patientName:
						appointment.patient?.patientName || 'Unknown Patient',
					clinicId: appointment.clinicId,
					doctorId: appointment.appointmentDoctors[0]?.doctorId,
					petId: appointment.patient?.id,
					doctorName,
					mode: (appointment.mode === 'Online'
						? 'Online'
						: 'Clinic') as 'Clinic' | 'Online',
					status: this.mapAppointmentStatus(
						appointment.status || EnumAppointmentStatus.Completed
					),
					canModifyOrCancel,
					modificationDeadline: modificationDeadlineTime // Return the full TimeDuration object or null
				};

				// Categorize as upcoming or previous
				const appointmentStartMoment = moment(
					baseAppointment.startTime
				); // Use the startTime ISO string directly
				if (
					appointmentStartMoment.isAfter(now) &&
					appointment.status !== EnumAppointmentStatus.Cancelled
				) {
					upcoming.push({ ...baseAppointment });
				} else {
					previous.push({
						...baseAppointment,
						visitType: appointment.type || ''
					});
				}
			}

			// Sort appointments
			upcoming.sort((a, b) =>
				moment(a.startTime).diff(moment(b.startTime))
			);
			previous.sort((a, b) =>
				moment(b.startTime).diff(moment(a.startTime))
			);

			return {
				upcoming,
				previous
			};
		} catch (error) {
			this.logger.error('Error getting client appointments', {
				error,
				ownerId
			});
			throw error;
		}
	}

	/**
	 * Map appointment status from database enum to frontend enum
	 */
	private mapAppointmentStatus(
		status: string
	): 'Completed' | 'Missed' | 'Cancelled' | 'Scheduled' {
		switch (status) {
			case EnumAppointmentStatus.Scheduled:
				return 'Scheduled';
			case EnumAppointmentStatus.Completed:
				return 'Completed';
			case EnumAppointmentStatus.Missed:
				return 'Missed';
			case EnumAppointmentStatus.Cancelled:
				return 'Cancelled';
			default:
				return 'Completed';
		}
	}

	/**
	 * Get the list of clinics for the client dashboard
	 * @param brandId Brand ID to filter clinics
	 * @returns Array of clinic objects with id and name that have client booking enabled
	 */
	async getClientClinics(brandId: string): Promise<any[]> {
		try {
			this.logger.log('Getting client clinics with booking enabled', {
				brandId
			});

			// Execute a query to get clinics from the database that have client booking enabled
			const clinics = await this.dataSource
				.createQueryBuilder()
				.select(['c.id as id', 'c.name as name'])
				.from('clinics', 'c')
				.where('c.brand_id = :brandId', { brandId })
				// Filter for only clinics with client booking enabled
				.andWhere(`c.custom_rule->'clientBookingSettings' IS NOT NULL`)
				.andWhere(
					`(c.custom_rule->'clientBookingSettings'->>'isEnabled')::boolean = true`
				)
				.orderBy('c.name', 'ASC')
				.getRawMany();

			this.logger.log('Found clinics with booking enabled', {
				brandId,
				clinicCount: clinics.length
			});

			return clinics;
		} catch (error) {
			this.logger.error(
				'Error getting client clinics with booking enabled',
				{
					error,
					brandId
				}
			);
			throw error;
		}
	}

	/**
	 * Get the list of doctors/providers for the client dashboard, potentially filtered by clinic's allowed list.
	 * @param brandId Brand ID to filter doctors
	 * @param clinicId Optional clinic ID to filter doctors by clinic and its booking settings
	 * @returns Array of doctor objects with id and name
	 */
	async getClientDoctors(brandId: string, clinicId?: string): Promise<any[]> {
		try {
			this.logger.log('Getting client doctors', { brandId, clinicId });

			let allowedDoctorIds: string[] | null | undefined = undefined;
			let allowAllDoctorsFlag: boolean = false; // Initialize flag

			// Fetch clinic settings if clinicId is provided
			if (clinicId) {
				// Fetch settings using ClinicService to ensure defaults are applied
				const settings =
					await this.clinicService.getClientBookingSettings(clinicId);

				if (settings) {
					allowAllDoctorsFlag = settings.allowAllDoctors ?? false; // Use retrieved flag or default
					allowedDoctorIds = settings.allowedDoctorIds ?? null; // Use retrieved IDs or null

					if (allowAllDoctorsFlag) {
						this.logger.log(
							'allowAllDoctors is true, skipping allowedDoctorIds filter',
							{ clinicId }
						);
					} else if (
						Array.isArray(allowedDoctorIds) &&
						allowedDoctorIds.length === 0
					) {
						// Explicitly no doctors allowed if allowAll=false and list is empty
						this.logger.log(
							'Settings indicate no specific doctors allowed for booking.',
							{ clinicId }
						);
						return []; // Return empty immediately
					}
				} else {
					// This case should ideally not happen if clinicId is valid, but handle defensively
					this.logger.warn(
						'Could not retrieve client booking settings',
						{ clinicId }
					);
					// Default behavior: Assume allowAllDoctors is false, allowedDoctorIds is null (no filtering)
				}
			}

			// Create query builder to get doctors
			const queryBuilder = this.dataSource
				.createQueryBuilder()
				.select([
					'cu.id as id', // Select the ClinicUser ID as 'id'. This ID represents the provider in the context of a specific clinic.
					"CONCAT(u.first_name, ' ', u.last_name) as name" // Concatenate first and last names for the provider's full name.
				])
				.from('clinic_users', 'cu') // Start querying from the clinic_users table (aliased as 'cu').
				.innerJoin('users', 'u', 'cu.user_id = u.id') // Join with the users table to get user details like name.
				.innerJoin('roles', 'r', 'u.role_id = r.id') // Join with the roles table to filter by user role.
				.innerJoin('clinics', 'c', 'cu.clinic_id = c.id') // Join with the clinics table to filter by brand.
				.where('c.brand_id = :brandId', { brandId }) // Filter results to include only users associated with the specified brand.
				// Filter results to include only users with the 'DOCTOR' or 'ADMIN' role.
				// This ensures only relevant personnel are listed as potential providers for booking.
				.andWhere('r.name IN (:...allowedRoles)', {
					allowedRoles: [Role.DOCTOR, Role.ADMIN]
				})
				.andWhere('cu.id IS NOT NULL') // Ensure the ClinicUser ID exists (sanity check).
				.orderBy('name', 'ASC'); // Order the results alphabetically by name.

			// Add clinic filter if provided
			if (clinicId) {
				queryBuilder.andWhere('cu.clinic_id = :clinicId', { clinicId });

				// Apply allowedDoctorIds filter only if allowAllDoctors is false and the list is non-empty
				if (
					allowAllDoctorsFlag === false &&
					Array.isArray(allowedDoctorIds) &&
					allowedDoctorIds.length > 0
				) {
					this.logger.log(
						'Applying allowed doctor filter based on settings',
						{
							clinicId,
							allowedDoctorIds
						}
					);
					// Filter by ClinicUser IDs
					queryBuilder.andWhere('cu.id IN (:...allowedDoctorIds)', {
						allowedDoctorIds
					});
				}
			}

			const doctors = await queryBuilder.getRawMany();

			this.logger.log(`Returning ${doctors.length} doctors`, {
				brandId,
				clinicId,
				filterApplied:
					!allowAllDoctorsFlag &&
					Array.isArray(allowedDoctorIds) &&
					allowedDoctorIds.length > 0
			});

			return doctors;
		} catch (error) {
			this.logger.error('Error getting client doctors', {
				error,
				brandId,
				clinicId
			});
			throw error;
		}
	}
}

// Helper function (can be moved to a utils file if shared)
function timeDurationToMinutes(
	duration: TimeDuration | null | undefined
): number | null {
	if (!duration) return null;
	const days = duration.days || 0;
	const hours = duration.hours || 0;
	const minutes = duration.minutes || 0;
	const totalMinutes = days * 24 * 60 + hours * 60 + minutes;
	return totalMinutes > 0 ? totalMinutes : null;
}
