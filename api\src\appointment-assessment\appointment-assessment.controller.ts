import {
	Body,
	Controller,
	Get,
	HttpException,
	HttpStatus,
	Post,
	Query,
	Req,
	UseGuards,
	UsePipes,
	ValidationPipe
} from '@nestjs/common';
import { AppointmentAssessmentService } from './appointment-assessment.service';
import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { CreateAppointmentAssessmentDto } from './dto/create-appointment-assessment.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Appointments')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('appointment-assessment')
export class AppointmentAssessmentController {
	constructor(
		private readonly appointmentAssessmentService: AppointmentAssessmentService,
		private readonly logger: WinstonLogger
	) {}

	@ApiOkResponse({
		description: 'Returns the list of assessments',
		isArray: true,
		type: AppointmentAssessmentEntity
	})
	@ApiQuery({ name: 'search', type: 'string', required: false })
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getAppointmentAssessments-appointment-assessment')
	async getAppointmentAssessments(
		@Req() req: { user: { clinicId: string } },
		@Query('search') search?: string
	): Promise<AppointmentAssessmentEntity[]> {
		return this.appointmentAssessmentService.findAll(
			search,
			req.user.clinicId
		);
	}

	@ApiOkResponse({
		description: 'Creates a new assessment',
		type: AppointmentAssessmentEntity
	})
	@Post()
	@UsePipes(ValidationPipe)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createNewAssessment-appointment-assessment')
	createNewAssessment(
		@Body() createAppointmentAssessmentDto: CreateAppointmentAssessmentDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			this.logger.log('Creating new assessment', {
				dto: createAppointmentAssessmentDto
			});

			return this.appointmentAssessmentService.createNewAssessment(
				createAppointmentAssessmentDto,
				req.user.clinicId,
				req.user.brandId
			);
		} catch (error) {
			this.logger.error('Error creating new assessment', {
				error,
				createAppointmentAssessmentDto
			});

			throw new HttpException(
				(error as Error).message,
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
