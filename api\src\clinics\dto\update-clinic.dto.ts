import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
	IsString,
	IsOptional,
	IsEmail,
	IsUrl,
	IsArray,
	ArrayMaxSize,
	Matches,
	IsObject,
	IsBoolean,
	ValidateNested
} from 'class-validator';

interface ClinicCustomRule {
	patientLastNameAsOwnerLastName: boolean;
}

export class UpdateClinicDto {
	@IsOptional()
	@IsString()
	addressLine1?: string;

	@IsOptional()
	@IsString()
	addressLine2?: string;

	@IsOptional()
	@IsString()
	city?: string;

	@IsOptional()
	@IsString()
	@Matches(/^\d{6}$/, { message: 'PIN must be a 6-digit number' })
	addressPincode?: string;

	@IsOptional()
	@IsString()
	state?: string;

	@IsOptional()
	@IsString()
	country?: string;

	@IsOptional()
	@IsEmail()
	email?: string;

	@IsOptional()
	@IsUrl()
	website?: string;

	@IsOptional()
	@IsString()
	logoUrl?: string;

	@IsOptional()
	@IsString()
	drugLicenseNumber?: string;

	@IsOptional()
	@IsString()
	clinicLogo?: string;

	@IsOptional()
	@IsArray()
	@ArrayMaxSize(4)
	@ValidateNested({ each: true })
	@Type(() => PhoneNumberDto)
	phoneNumbers?: PhoneNumberDto[];

	@IsObject()
	@IsOptional()
  	workingHours?: {
    [key: string]: DaySchedule;
  };

	@ApiProperty({
		description: 'Custom rules for the clinic',
		example: { patientLastNameAsOwnerLastName: false }
	})
	@IsOptional()
	@IsObject()
	customRule?: ClinicCustomRule;
}

class DaySchedule {
	@ApiProperty({ example: '09:00', required: false })
	@IsString()
	@IsOptional()
	startTime!: string | null;
  
	@ApiProperty({ example: '17:00', required: false })
	@IsString()
	@IsOptional()
	endTime!: string | null;
  
	@ApiProperty({ example: true })
	@IsBoolean()
	isWorkingDay!: boolean;
  }

  class PhoneNumberDto {
	@IsString()
	country_code?: string;
  
	@IsString()
	number?: string;
  }