import {
	Body,
	Controller,
	Delete,
	Get,
	Param,
	Patch,
	Post,
	Query,
	UseGuards,
	Put,
	Req,
	BadRequestException,
	HttpException,
	HttpStatus
} from '@nestjs/common';
import { ClinicLabReportService } from './clinic-lab-report.service';
import { ClinicLabReport } from './entities/clinic-lab-report.entity';
import { CreateLabReportDto, UpdateLabReportDto } from './dto/lab-report.dto';
import { UpdateStatusDto } from './dto/update-clinic-lab-report-status.dto';
import { LabReport } from './entities/lab-report.entity';
import * as moment from 'moment';
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiQuery,
	ApiParam,
	ApiCreatedResponse,
	ApiOkResponse
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { CreateClinicLabReportDto } from './dto/create-clinic-lab-report.dto';
import { UpdateClinicLabReportDto } from './dto/update-clinic-lab-report.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	CreateDiagnosticNoteDto,
	DeleteDiagnosticNoteDto,
	EditDiagnosticNoteDto
} from './dto/create-update-diagnostic-notes.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Clinic Lab Reports')
@Controller('clinic-lab-reports')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicLabReportController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly clinicLabReportService: ClinicLabReportService
	) {}

	@ApiOperation({ summary: 'Get clinic lab reports' })
	@ApiResponse({ status: 200, description: 'Return all clinic lab reports' })
	@ApiQuery({ name: 'search', required: false, type: String })
	@Get('types')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getClinicLabReports-clinic-lab-reports')
	async getClinicLabReports(
		@Query('clinicId') clinicId: string,
		@Query('search') search?: string,
		@Query('integrationType') integrationType?: string
	): Promise<ClinicLabReport[]> {
		return this.clinicLabReportService.getLabReports(
			clinicId,
			search,
			integrationType
		);
	}

	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create a clinic lab report' })
	@ApiCreatedResponse({ type: ClinicLabReport })
	@TrackMethod('create-clinic-lab-reports')
	async create(
		@Body() createLabReportDto: CreateClinicLabReportDto,
		@Req() req: { user: { clinicId: string; brandId: string } }
	) {
		try {
			return await this.clinicLabReportService.create(
				createLabReportDto,
				req.user.brandId
			);
		} catch (error) {
			this.logger.error('Error creating clinic lab report', { error });
			throw new HttpException(
				'Error creating clinic lab report',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get a clinic lab report by id' })
	@ApiOkResponse({ type: ClinicLabReport })
	@TrackMethod('findOne-clinic-lab-reports')
	async findOne(@Param('id') id: string) {
		try {
			return await this.clinicLabReportService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching clinic lab report', { error });
			throw new HttpException(
				'Error fetching clinic lab report',
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Get('lab-report/:id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Get a lab report by id',
		description:
			'Fetch minimal lab report data for deletion flow including IDEXX integration information'
	})
	@ApiParam({
		name: 'id',
		description: 'Lab report ID',
		type: 'string'
	})
	@ApiOkResponse({
		description: 'Lab report with integration details',
		schema: {
			type: 'object',
			properties: {
				id: { type: 'string' },
				integrationOrderId: { type: 'string' },
				integrationDetails: { type: 'object' },
				status: { type: 'string' },
				lineItemId: { type: 'string' },
				appointmentId: { type: 'string' },
				patientId: { type: 'string' },
				clinicLabReportId: { type: 'string' }
			}
		}
	})
	@TrackMethod('findLabReportById-clinic-lab-reports')
	async findLabReportById(@Param('id') id: string) {
		try {
			const labReport =
				await this.clinicLabReportService.findLabReportById(id);
			if (!labReport) {
				throw new HttpException(
					`Lab report with ID ${id} not found`,
					HttpStatus.NOT_FOUND
				);
			}
			return labReport;
		} catch (error) {
			this.logger.error('Error fetching lab report by ID', {
				error,
				labReportId: id
			});
			if (error instanceof HttpException) {
				throw error;
			}
			throw new HttpException(
				'Error fetching lab report',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@Patch(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update a clinic lab report' })
	@ApiOkResponse({ type: ClinicLabReport })
	@TrackMethod('update-clinic-lab-reports')
	async update(
		@Param('id') id: string,
		@Body() updateLabReportDto: UpdateClinicLabReportDto
	) {
		try {
			return await this.clinicLabReportService.update(
				id,
				updateLabReportDto
			);
		} catch (error) {
			this.logger.error('Error updating clinic lab report', { error });
			throw new HttpException(
				'Error updating clinic lab report',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete a clinic lab report' })
	@ApiOkResponse({ description: 'Clinic lab report deleted successfully' })
	@TrackMethod('deleteItem-clinic-lab-reports')
	async deleteItem(@Param('id') id: string) {
		try {
			await this.clinicLabReportService.deleteItem(id);
			return { message: 'Clinic lab report deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting clinic lab report', { error });
			throw new HttpException(
				'Error deleting clinic lab report',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	//---------------------------------

	@ApiOperation({ summary: 'Create a lab report' })
	@ApiResponse({
		status: 201,
		description: 'The lab report has been successfully created.'
	})
	@ApiQuery({
		name: 'isCreate',
		required: false,
		type: Boolean,
		description:
			'Flag to indicate if this is a create operation (default: false)'
	})
	@Post('lab-report')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createLabReport-clinic-lab-reports')
	async createLabReport(
		@Body() createLabReportDto: CreateLabReportDto,
		@Query('isCreate') isCreate: boolean = false,
		@Req() req: { user: { clinicId: string; brandId: string } }
	): Promise<LabReport> {
		return this.clinicLabReportService.createOrUpdateLabReport(
			createLabReportDto,
			req.user.brandId,
			isCreate
		);
	}

	@Delete(':labReportId/files/:fileId')
	@ApiOperation({ summary: 'Delete a file from a lab report' })
	@ApiParam({ name: 'labReportId', description: 'ID of the lab report' })
	@ApiParam({ name: 'fileId', description: 'ID of the file to delete' })
	@ApiQuery({
		name: 'lineItemId',
		description: 'Line item ID of the lab report',
		required: true
	})
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteFile-clinic-lab-reports')
	async deleteFile(
		@Param('labReportId') labReportId: string,
		@Param('fileId') fileId: string,
		@Query('lineItemId') lineItemId: string
	): Promise<LabReport> {
		return this.clinicLabReportService.deleteFile(
			labReportId,
			fileId,
			lineItemId
		);
	}

	@Delete('/lab-report/:id') // Added forward slash
	@ApiOperation({ summary: 'Delete a lab report and its associated files' })
	@ApiResponse({
		status: 200,
		description: 'The lab report has been successfully deleted.'
	})
	@ApiParam({ name: 'id', type: 'string' })
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteLabReport-clinic-lab-reports')
	async deleteLabReport(
		@Param('id') id: string,
		@Query('appointmentId') appointmentId: string,
		@Query('lineItemId') lineItemId: string
	) {
		return this.clinicLabReportService.deleteLabReport(
			id,
			appointmentId,
			lineItemId
		);
	}

	@ApiOperation({ summary: 'Get lab reports for a clinic' })
	@ApiResponse({
		status: 200,
		description: 'Return lab reports for a clinic'
	})
	@ApiQuery({ name: 'clinicId', required: true, type: String })
	@ApiQuery({ name: 'page', required: false, type: Number })
	@ApiQuery({ name: 'limit', required: false, type: Number })
	@ApiQuery({ name: 'startDate', required: false, type: String })
	@ApiQuery({ name: 'endDate', required: false, type: String })
	@Get('')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getLabReportsForClinic-clinic-lab-reports')
	async getLabReportsForClinic(
		@Query('clinicId') clinicId: string,
		@Query('page') page: number = 1,
		@Query('limit') limit: number = 10,
		@Query('searchTerm') searchTerm?: string,
		@Query('status') status?: string
	) {
		const startDate = moment().startOf('day').toDate();
		const endDate = moment().endOf('day').toDate();

		return this.clinicLabReportService.getLabReportsForClinic(
			clinicId,
			page,
			limit,
			startDate,
			endDate,
			searchTerm,
			status
		);
	}

	@ApiResponse({
		status: 200,
		description: 'Lab report status updated successfully'
	})
	@Put('status')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('updateStatus-clinic-lab-reports')
	async updateStatus(@Body() updateStatusDto: UpdateStatusDto) {
		return this.clinicLabReportService.updateLabReportStatus(
			updateStatusDto
		);
	}

	@Get('/diagnostic/:patientId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getLabReportsForPatient-clinic-lab-reports')
	async getLabReportsForPatient(@Param('patientId') patientId: string) {
		return this.clinicLabReportService.getLabReportForPatient(patientId);
	}

	@ApiParam({ name: 'appointmentId', type: 'string' })
	@Delete('appointment/:appointmentId')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('deleteLabReportsByAppointmentId-clinic-lab-reports')
	async deleteLabReportsByAppointmentId(
		@Param('appointmentId') appointmentId: string
	): Promise<any> {
		try {
			console.log('in the delete lap repo controller');
			return await this.clinicLabReportService.deleteLabReportsByAppointmentId(
				appointmentId
			);
		} catch (error) {
			console.log('error :>> ', error);
			throw new BadRequestException(error);
		}
	}

	@ApiOperation({ summary: 'Update clinic lab report' })
	@ApiResponse({ status: 200, description: 'Return updated lab report' })
	@ApiParam({ name: 'id', required: true, type: String })
	@Put(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('updateLabReport-clinic-lab-reports')
	async updateLabReport(
		@Param('id') id: string,
		@Body() updateLabportDto: UpdateLabReportDto
	): Promise<ClinicLabReport> {
		return this.clinicLabReportService.updateClinicLabReport(
			id,
			updateLabportDto
		);
	}

	@Put(':id/diagnostic-notes/create')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('createDiagnosticNote-clinic-lab-reports')
	async createDiagnosticNote(
		@Param('id') appointmentId: string,
		@Query('operation') operation: 'insert' | 'edit' | 'delete',
		@Body()
		note:
			| CreateDiagnosticNoteDto
			| EditDiagnosticNoteDto
			| DeleteDiagnosticNoteDto
	) {
		return this.clinicLabReportService.handleCreateDiagnosticNotes(
			appointmentId,
			operation,
			note
		);
	}
}
