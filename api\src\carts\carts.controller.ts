import { Controller, Delete, Param, UseGuards } from '@nestjs/common';
import { CartsService } from './carts.service';
import { ApiTags, ApiOkResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Carts')
@Controller('carts')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CartsController {
	constructor(private readonly cartsService: CartsService) {}
	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST)
	@ApiOkResponse({ description: 'Cart deleted successfully' })
	@TrackMethod('deleteCart-carts')
	async deleteCart(@Param('id') id: string) {
		return this.cartsService.deleteCart(id);
	}
}
