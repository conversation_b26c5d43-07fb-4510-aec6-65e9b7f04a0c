import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { ClinicProductEntity } from './entities/clinic-product.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateProductDto } from './dto/create-products.dto';
import { UpdateProductDto } from './dto/update-products.dto';

@Injectable()
export class ClinicProductsService {
	constructor(
		@InjectRepository(ClinicProductEntity)
		private readonly productRepository: Repository<ClinicProductEntity>,
		private readonly logger: <PERSON>Logger
	) {}

	// Make generateUniqueId asynchronous and accept clinicId
	private async generateUniqueId(
		prefix: string,
		clinicId: string
	): Promise<string> {
		const count = await this.productRepository.count({
			where: { clinicId }
		});
		const nextNumber = count + 1;
		return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
	}

	async getProducts(clinicId: string, searchKeyword?: string) {
		if (searchKeyword) {
			if (clinicId) {
				return this.productRepository.find({
					where: {
						productName: ILike(`%${searchKeyword}%`),
						clinicId
					}
				});
			}

			return this.productRepository.find({
				where: { productName: ILike(`%${searchKeyword}%`) }
			});
		}

		return this.productRepository.find({
			where: { clinicId }
		});
	}

	async bulkInsert(products: CreateProductDto[]): Promise<string> {
		try {
			for (const product of products) {
				// Check if item exists for the clinic
				const existingProduct = await this.productRepository.findOne({
					where: {
						clinicId: product.clinicId,
						productName: product.productName
					}
				});

				if (existingProduct) {
					// Update existing item
					Object.assign(existingProduct, product);
					await this.productRepository.save(existingProduct);
				} else {
					// Create new item with unique ID
					const uniqueId = await this.generateUniqueId(
						'P_',
						product.clinicId
					);
					await this.productRepository.save({
						...product,
						uniqueId
					});
				}
			}

			const message = `Bulk insert of ${products.length} products completed successfully`;
			this.logger.log(
				`Bulk insert completed. Inserted ${products.length} products.`
			);
			return message;
		} catch (error) {
			this.logger.error('Error during bulk insert of products', {
				error
			});
			throw new Error('Failed to insert products');
		}
	}
	async create(
		createProductDto: CreateProductDto
	): Promise<ClinicProductEntity> {
		try {
			const uniqueId = await this.generateUniqueId(
				'P_',
				createProductDto.clinicId
			);
			const product = this.productRepository.create({
				...createProductDto,
				uniqueId
			});
			return await this.productRepository.save(product);
		} catch (error) {
			this.logger.error('Error creating product', { error });
			throw error;
		}
	}

	async update(
		id: string,
		updateProductDto: UpdateProductDto
	): Promise<ClinicProductEntity> {
		const product = await this.productRepository.findOneBy({ id });
		if (!product) {
			const error = new NotFoundException(
				`Product with ID ${id} not found`
			);
			this.logger.error('Error updating product', { error });
			throw error;
		}

		try {
			Object.assign(product, updateProductDto);
			return await this.productRepository.save(product);
		} catch (error) {
			this.logger.error('Error updating product', { error });
			throw error;
		}
	}

	async findOne(id: string): Promise<ClinicProductEntity> {
		const product = await this.productRepository.findOneBy({ id });
		if (!product) {
			throw new NotFoundException(`Product with ID ${id} not found`);
		}
		return product;
	}

	async remove(id: string): Promise<void> {
		const result = await this.productRepository.delete(id);
		if (result.affected === 0) {
			throw new NotFoundException(`Product with ID ${id} not found`);
		}
	}

	async findOneEntry(criteria: { productName: string; clinicId: string }) {
		return this.productRepository.findOne({ where: criteria });
	}

	async deleteItem(itemId: string) {
		return this.productRepository.delete(itemId);
	}
}
