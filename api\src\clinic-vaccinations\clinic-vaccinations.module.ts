import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicVaccinationsService } from './clinic-vaccinations.service';
import { ClinicVaccinationEntity } from './entities/clinic-vaccination.entity';
import { ClinicVaccinationsController } from './clinic-vaccinations.controller';
import { RoleModule } from '../roles/role.module';

@Module({
	imports: [TypeOrmModule.forFeature([ClinicVaccinationEntity]), RoleModule],
	controllers: [ClinicVaccinationsController],
	providers: [ClinicVaccinationsService],
	exports: [ClinicVaccinationsService]
})
export class ClinicVaccinationsModule {}
