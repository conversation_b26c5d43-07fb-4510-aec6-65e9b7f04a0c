import { ApiProperty } from '@nestjs/swagger';
import { CreditTransactionType } from '../enums/enum-credit-transaction-type';
import { DerivedCreditTransactionType } from '../enums/enum-derived-credit-transaction-type';

export class CreditTransactionResponseDto {
	@ApiProperty({
		description: 'Transaction ID',
		example: 'uuid'
	})
	id!: string;

	@ApiProperty({
		description: 'Owner ID',
		example: 'uuid'
	})
	ownerId!: string;

	@ApiProperty({
		description: 'Transaction amount',
		example: 1500
	})
	amount!: number;

	@ApiProperty({
		description: 'Transaction type (ADD or USE)',
		enum: CreditTransactionType,
		example: CreditTransactionType.ADD
	})
	transactionType!: CreditTransactionType;

	@ApiProperty({
		description:
			'Derived transaction type (CREDITS_RETURNED, CREDITS_USED, EXCESS_PAYMENT, CREDITS_ADDED)',
		enum: DerivedCreditTransactionType,
		example: DerivedCreditTransactionType.CREDITS_ADDED
	})
	derivedTransactionType!: DerivedCreditTransactionType;

	@ApiProperty({
		description: 'Transaction description',
		example: 'Credit added for future use'
	})
	description!: string;

	@ApiProperty({
		description: 'Related invoice ID if applicable',
		example: 'uuid',
		required: false
	})
	invoiceId?: string;

	@ApiProperty({
		description: 'Related invoice reference number if applicable',
		example: 'INV-001',
		required: false
	})
	invoiceReferenceAlphaId?: string;

	@ApiProperty({
		description: 'Related payment detail ID if applicable',
		example: 'uuid',
		required: false
	})
	paymentDetailId?: string;

	@ApiProperty({
		description: 'Related payment reference number if applicable',
		example: 'PAY-001',
		required: false
	})
	paymentReferenceAlphaId?: string;

	@ApiProperty({
		description: 'Payment notes if applicable',
		example: 'Payment for invoice #123',
		required: false
	})
	paymentNotes?: string;

	@ApiProperty({
		description: 'Payment details object if applicable',
		required: false,
		type: 'object'
	})
	paymentDetails?: {
		id: string;
		amount: number;
		type: string;
		paymentType: string;
		isCreditUsed: boolean;
		creditAmountUsed: number;
		isCreditsAdded: boolean;
		creditAmountAdded: number;
		amountPayable: number;
		mainBalance: number;
		transactionAmount: number;
		previousBalance: number;
		createdAt: Date;
		createdBy: string;
		referenceAlphaId: string;
		[key: string]: any;
	};

	@ApiProperty({
		description: 'Clinic ID',
		example: 'uuid'
	})
	clinicId!: string;

	@ApiProperty({
		description: 'Brand ID',
		example: 'uuid'
	})
	brandId!: string;

	@ApiProperty({
		description: 'User ID who created the transaction',
		example: 'uuid'
	})
	createdBy!: string;

	@ApiProperty({
		description: 'Name of user who created the transaction',
		example: 'John Doe'
	})
	createdByName?: string;

	@ApiProperty({
		description: 'Transaction creation timestamp',
		example: '2023-01-01T12:00:00Z'
	})
	createdAt!: Date;

	@ApiProperty({
		description: 'Transaction update timestamp',
		example: '2023-01-01T12:00:00Z'
	})
	updatedAt!: Date;

	@ApiProperty({
		description: 'Additional metadata for the transaction',
		example: { source: 'manual_addition', notes: 'Customer requested' },
		required: false
	})
	metadata?: Record<string, any>;
}

export class PaginatedCreditTransactionsResponseDto {
	@ApiProperty({
		description: 'Owner details',
		example: {
			id: 'uuid',
			name: 'John Doe',
			creditBalance: 1500
		}
	})
	ownerDetails!: {
		id: string;
		name: string;
		creditBalance: number;
	};

	@ApiProperty({
		description: 'List of credit transactions',
		type: [CreditTransactionResponseDto]
	})
	transactions!: CreditTransactionResponseDto[];

	@ApiProperty({
		description: 'List of unique users who created transactions',
		example: [{ id: 'uuid', name: 'John Doe' }]
	})
	uniqueUsers!: { id: string; name: string }[];

	@ApiProperty({
		description: 'Pagination information',
		example: {
			total: 50,
			page: 1,
			limit: 20,
			hasMore: true
		}
	})
	pagination!: {
		total: number;
		page: number;
		limit: number;
		hasMore: boolean;
	};
}
