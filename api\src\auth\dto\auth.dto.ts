import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../../roles/role.enum';

export class EmailLoginDto {
	@ApiProperty({ description: 'The email address of the user' })
	@IsEmail()
	email!: string;
	@IsString()
	otp!: string;
}

export class VerifyOtpDto {
	@ApiProperty({ description: 'The email address of the user' })
	@IsEmail()
	email!: string;

	@ApiProperty({ description: 'The OTP received via email', minLength: 6 })
	@IsString()
	@MinLength(6)
	otp!: string;
}

export class PinLoginDto {
	@ApiProperty({ description: 'The PIN of the user', minLength: 4 })
	@IsString()
	@MinLength(4)
	@MaxLength(4)
	pin!: string;
	@ApiProperty({ description: 'The brandId of the user' })
	@IsString()
	@IsUUID()
	brandId!: string;
}
