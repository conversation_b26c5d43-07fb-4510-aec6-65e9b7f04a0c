import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { CartsService } from './carts.service';
import { CartsController } from './carts.controller';
import { CartEntity } from './entites/cart.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CartItemService } from '../cart-items/cart-item.service';
import { CartItemEntity } from '../cart-items/entities/cart-item.entity';
import { RoleModule } from '../roles/role.module';
import { AppointmentsModule } from '../appointments/appointments.module';
import { CartItemModule } from '../cart-items/cart-item.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([CartEntity, CartItemEntity]),
		RoleModule,
		forwardRef(() => AppointmentsModule),
		forwardRef(() => CartItemModule)
	],
	controllers: [CartsController],
	providers: [CartsService, CartItemService],
	exports: [CartsService]
})
export class CartsModule {}
