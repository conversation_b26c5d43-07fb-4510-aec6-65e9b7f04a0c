import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>UUI<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateProductDto {
	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	clinicId!: string;

	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	brandId!: string;

	// @ApiProperty()
	// @IsString()
	// @IsNotEmpty()
	// uniqueId!: string;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	productName!: string;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	chargeablePrice!: number;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	tax!: number;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	currentStock!: number;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	minimumQuantity!: number;
}
