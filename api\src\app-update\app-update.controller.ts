import {
	Body,
	Controller,
	Post,
	UsePipes,
	ValidationPipe
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { AppUpdateDto } from './app-update.dto';

@ApiTags('App Update')
@Controller('app-update')
export class AppUpdateController extends ApiDocumentationBase {
	@ApiOkResponse({
		description: 'Returns if force update is true or false'
	})
	@Post()
	@UsePipes(ValidationPipe)
	async isAppUpdateAvailable(@Body() bodyParams: AppUpdateDto) {
		console.log(bodyParams);
		const updateInfo = {
			forceUpdate: false
		};

		return updateInfo;
	}
}
