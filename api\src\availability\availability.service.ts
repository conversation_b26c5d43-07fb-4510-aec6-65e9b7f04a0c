import { Injectable, Optional } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
	Between,
	DataSource,
	In, // Import In operator
	IsNull,
	LessThanOrEqual,
	MoreThanOrEqual,
	Repository,
	Raw
} from 'typeorm';
// Import date-fns-tz for timezone handling
import { formatInTimeZone } from 'date-fns-tz';
import * as moment from 'moment-timezone';

// Import related entities and services
import { ClinicAvailabilitySlot } from './entities/clinic-availability-slot.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity'; // Import ClinicEntity
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { AvailabilityExceptionEntity } from '../users/entities/availability-exception.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { SqsService } from '../utils/aws/sqs/sqs.service';

// Import the exception type from the entity to ensure type compatibility
import { ExceptionType } from '../users/entities/availability-exception.entity';

// Define return type for time slots
interface TimeSlotResult {
	startTime: string;
	endTime: string;
	isAvailable: boolean;
}

/**
 * Service to handle clinic user availability
 * Uses a hybrid approach with precomputed availability slots
 */
@Injectable()
export class AvailabilityService {
	/**
	 * Validates if a string is in YYYY-MM-DD format or full ISO-8601 format (with T and Z)
	 * @param dateStr - The date string to validate
	 * @returns Boolean indicating if the format is valid
	 */
	private isValidDateFormat(dateStr: string): boolean {
		if (!dateStr) return false;

		// Handle full ISO-8601 timestamp format (e.g., 2025-05-01T04:30:00.000Z)
		if (dateStr.includes('T')) {
			// Extract date part from ISO string
			dateStr = dateStr.split('T')[0];
		}

		// Check format using regex: YYYY-MM-DD
		const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
		if (!dateRegex.test(dateStr)) return false;

		// Validate as actual date (e.g., not 2023-02-31)
		const date = new Date(dateStr);
		return !isNaN(date.getTime());
	}

	/**
	 * Validates if a string is in HH:MM:SS, HH:MM format, or full ISO-8601 format
	 * @param timeStr - The time string to validate
	 * @returns Boolean indicating if the format is valid
	 */
	private isValidTimeFormat(timeStr: string): boolean {
		if (!timeStr) return false;

		// Handle full ISO-8601 timestamp format (e.g., 2025-05-01T04:30:00.000Z)
		if (timeStr.includes('T')) {
			try {
				// If it's a full ISO string, validate as a Date object
				const date = new Date(timeStr);
				return !isNaN(date.getTime());
			} catch {
				// Ignore error and return false
				return false;
			}
		}

		// Check format using regex for simple time: HH:MM:SS or HH:MM
		const timeRegex =
			/^([01]\d|2[0-3]):([0-5]\d)(?::([0-5]\d))?(?:\.\d+)?$/;
		return timeRegex.test(timeStr);
	}

	constructor(
		@InjectRepository(ClinicAvailabilitySlot)
		private availabilityRepository: Repository<ClinicAvailabilitySlot>,
		@InjectRepository(ClinicUser)
		private clinicUserRepository: Repository<ClinicUser>,
		@InjectRepository(AppointmentEntity)
		private appointmentRepository: Repository<AppointmentEntity>,
		@InjectRepository(AppointmentDoctorsEntity)
		private appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>,
		@InjectRepository(AvailabilityExceptionEntity)
		private exceptionRepository: Repository<AvailabilityExceptionEntity>,
		private logger: WinstonLogger,
		@Optional() public readonly sqsService: SqsService,
		private dataSource: DataSource
	) {
		// If sqsService is not available, log a warning
		if (!this.sqsService) {
			this.logger.warn(
				'SqsService not available in AvailabilityService - some functionality may be limited'
			);
		}
	}

	/**
	 * Helper method to get timezone offset in milliseconds
	 * @param timeZone IANA timezone name (e.g., 'America/New_York')
	 * @param date Optional reference date (defaults to current date)
	 * @returns Offset in milliseconds between the timezone and UTC, or 0 if an error occurs
	 */
	private getTimezoneOffset(
		timeZone: string,
		date: Date = new Date()
	): number {
		try {
			// Format the reference date in the target timezone
			const formattedWithOffset = formatInTimeZone(
				date,
				timeZone,
				"yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
			);

			// Extract the offset string (e.g., "+05:30", "-04:00")
			const offsetMatch = formattedWithOffset.match(/([+-]\d{2}:\d{2})$/);

			if (!offsetMatch) {
				this.logger.warn(
					`Failed to extract timezone offset from: ${formattedWithOffset}`
				);
				return 0;
			}

			// Get the offset string (e.g., "+05:30")
			const offsetString = offsetMatch[1];

			// Parse hours and minutes from offset
			const offsetHours = parseInt(offsetString.substring(1, 3), 10);
			const offsetMinutes = parseInt(offsetString.substring(4, 6), 10);

			// Calculate offset in milliseconds
			const offsetMillis = (offsetHours * 60 + offsetMinutes) * 60 * 1000;

			// Apply the sign (negative if "+" because we're going from local to UTC)
			const finalOffset = offsetString.startsWith('+')
				? -offsetMillis
				: offsetMillis;

			this.logger.log('Calculated timezone offset', {
				timeZone,
				date: date.toISOString(),
				formattedWithOffset,
				offsetString,
				offsetHours,
				offsetMinutes,
				offsetMillis,
				finalOffset
			});

			return finalOffset;
		} catch (error) {
			this.logger.error(
				`Error getting timezone offset for ${timeZone}:`,
				{
					error:
						error instanceof Error ? error.message : String(error)
				}
			);
			// Return 0 offset if an error occurs to avoid breaking calculations
			return 0;
		}
	}

	/**
	 * Converts a local time string (assumed to be in the clinic's timezone) to a UTC Date object.
	 * If the time string already has a 'Z' suffix, it's treated as UTC already.
	 * @param localTimeStr Time string in local timezone format (YYYY-MM-DDTHH:MM:SS) or UTC format with Z suffix
	 * @param clinicTimezone IANA timezone name
	 * @returns Date object representing the time in UTC
	 */
	private localToUtc(localTimeStr: string, clinicTimezone: string): Date {
		// Check if the string already represents a UTC time (has Z suffix)
		if (localTimeStr.endsWith('Z')) {
			this.logger.log(
				'Input string already in UTC format, no conversion needed',
				{
					localTimeStr,
					isUTC: true
				}
			);
			// Simply parse the UTC string into a Date object
			return new Date(localTimeStr);
		}

		try {
			// Use date-fns-tz's formatInTimeZone to get the correct offset representation
			// For a date like "2025-05-01T08:30" in Asia/Kolkata, this should give "+05:30"
			const formattedWithOffset = formatInTimeZone(
				new Date(localTimeStr),
				clinicTimezone,
				"yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
			);

			// Extract the offset string (e.g., "+05:30")
			const offsetMatch = formattedWithOffset.match(/([+-]\d{2}:\d{2})$/);

			if (!offsetMatch) {
				this.logger.warn(
					`Failed to extract timezone offset from: ${formattedWithOffset}`
				);
				return new Date(localTimeStr); // Fallback
			}

			// Get the offset string
			const offsetString = offsetMatch[1];

			// Calculate the correct UTC time by directly including the offset
			// This is the most reliable way to convert from a local time to UTC
			const utcDate = new Date(`${localTimeStr}${offsetString}`);

			this.logger.log('Local to UTC Conversion', {
				localTimeStr,
				clinicTimezone,
				offsetString,
				formattedLocalTime: `${localTimeStr}${offsetString}`,
				utcDate: utcDate.toISOString()
			});

			return utcDate;
		} catch (error) {
			this.logger.error(
				`Error converting local time to UTC: ${localTimeStr}`,
				{
					error:
						error instanceof Error ? error.message : String(error),
					clinicTimezone
				}
			);
			// Fallback to direct parsing (may not handle timezone correctly)
			return new Date(localTimeStr);
		}
	}

	/**
	 * Core method to generate availability slots for a user within a date range
	 * This processes working hours, exceptions, and appointments to create slots
	 */
	async generateSlotsForUser(
		clinicUserId: string,
		startDate: Date,
		endDate: Date
	): Promise<void> {
		this.logger.log('Generating availability slots', {
			clinicUserId,
			startDate: startDate.toISOString(),
			endDate: endDate.toISOString()
		});

		// Using transaction for data consistency
		await this.dataSource.transaction(async entityManager => {
			// Clear existing slots in the date range
			await entityManager.delete(ClinicAvailabilitySlot, {
				clinicUserId,
				date: Between(
					startDate.toISOString().split('T')[0],
					endDate.toISOString().split('T')[0]
				)
			});

			// Fetch clinic user and the related clinic to get timezone
			const clinicUser = await entityManager.findOne(ClinicUser, {
				where: { id: clinicUserId },
				relations: ['clinic'] // Include the clinic relation to access timezone
			});

			if (!clinicUser) {
				this.logger.error(
					`Cannot generate slots: Clinic user ${clinicUserId} not found`
				);
				return;
			}

			// Get the clinic's timezone, default to UTC if missing
			const clinicTimezone = clinicUser.clinic?.timezone || 'UTC';
			this.logger.log('Using clinic timezone', {
				clinicUserId,
				clinicId: clinicUser.clinic?.id,
				timezone: clinicTimezone
			});

			// For each date in range
			const current = new Date(startDate);
			while (current <= endDate) {
				const dateStr = current.toISOString().split('T')[0];
				// Get the day of week in a locale-independent way
				const days = [
					'sunday',
					'monday',
					'tuesday',
					'wednesday',
					'thursday',
					'friday',
					'saturday'
				];
				const dayOfWeek = days[current.getDay()];

				this.logger.log('Calculating day of week', {
					date: dateStr,
					dayOfWeek,
					dayNumber: current.getDay(),
					dateObject: current.toISOString(),
					clinicTimezone
				});

				// Check if working hours are properly structured
				if (
					!clinicUser.workingHours ||
					!clinicUser.workingHours.workingHours
				) {
					this.logger.warn('Missing working hours configuration', {
						clinicUserId,
						date: dateStr,
						workingHours: clinicUser.workingHours
					});
				}

				// Special handling for Saturday (day 6)
				if (current.getDay() === 6) {
					this.logger.log('Processing Saturday', {
						clinicUserId,
						date: dateStr,
						dayOfWeek,
						workingHours:
							clinicUser.workingHours?.workingHours?.saturday
					});
				}

				// Get working hours for this day
				const dayWorkingHours =
					clinicUser.workingHours?.workingHours?.[dayOfWeek] || [];

				this.logger.log('Processing day working hours', {
					clinicUserId,
					date: dateStr,
					dayOfWeek,
					dayWorkingHours
				});

				// Default: no slots
				let daySlots: Array<{ start: Date; end: Date }> = [];

				// Process each working hours block
				for (const workingHour of dayWorkingHours) {
					// Log the working hour being processed
					this.logger.log('Processing working hour', {
						clinicUserId,
						date: dateStr,
						dayOfWeek,
						workingHour
					});

					// Skip if not a working day
					if (!workingHour.isWorkingDay) {
						this.logger.log('Skipping non-working day', {
							clinicUserId,
							date: dateStr,
							dayOfWeek,
							workingHour
						});
						continue;
					}

					// Skip if start/end times are missing or invalid
					if (
						!workingHour.startTime ||
						!workingHour.endTime ||
						workingHour.startTime === 'null' ||
						workingHour.endTime === 'null'
					) {
						this.logger.log(
							'Skipping invalid working hours (missing or null times)',
							{
								clinicUserId,
								date: dateStr,
								dayOfWeek,
								workingHour
							}
						);
						continue;
					}

					try {
						// Create local time strings with the clinic's timezone
						const localStartTimeStr = `${dateStr}T${workingHour.startTime}`;
						const localEndTimeStr = `${dateStr}T${workingHour.endTime}`;

						this.logger.log('Converting working hours to UTC', {
							clinicUserId,
							localStartTime: localStartTimeStr,
							localEndTime: localEndTimeStr,
							clinicTimezone
						});

						// Convert the local times to UTC based on clinic's timezone
						const start = this.localToUtc(
							localStartTimeStr,
							clinicTimezone
						);
						const end = this.localToUtc(
							localEndTimeStr,
							clinicTimezone
						);

						// Ensure end time is after start time
						if (end <= start) {
							this.logger.warn(
								'Invalid working hour range: end time is not after start time',
								{
									clinicUserId,
									date: dateStr,
									startTime: workingHour.startTime,
									endTime: workingHour.endTime,
									utcStart: start.toISOString(),
									utcEnd: end.toISOString()
								}
							);
							continue;
						}

						this.logger.log(
							'Created UTC time slot from working hours',
							{
								clinicUserId,
								localStartTime: localStartTimeStr,
								localEndTime: localEndTimeStr,
								utcStart: start.toISOString(),
								utcEnd: end.toISOString()
							}
						);

						daySlots.push({ start, end });
					} catch (error: any) {
						this.logger.error('Error processing working hours', {
							clinicUserId,
							date: dateStr,
							workingHour,
							error: error.message || String(error)
						});
					}
				}

				// Apply exceptions for this date
				this.logger.log('Fetching exceptions for date', {
					clinicUserId,
					date: dateStr
				});

				// Get exceptions that apply to this date
				const exceptions = await entityManager.find(
					AvailabilityExceptionEntity,
					{
						where: [
							// Case 1: Exception with only start date (no end date) that matches exactly this date
							{
								clinicUserId,
								startDate: Raw(
									alias => `DATE(${alias}) = :date`,
									{
										date: dateStr
									}
								),
								endDate: IsNull()
							},
							// Case 2: Exception with start and end date that includes this date
							{
								clinicUserId,
								startDate: Raw(
									alias => `DATE(${alias}) <= :date`,
									{
										date: dateStr
									}
								),
								endDate: Raw(
									alias => `DATE(${alias}) >= :date`,
									{
										date: dateStr
									}
								)
							}
						]
					}
				);

				this.logger.log('Found exceptions for date', {
					clinicUserId,
					date: dateStr,
					exceptionsCount: exceptions.length,
					exceptions: exceptions.map(e => ({
						id: e.id,
						type: e.type,
						startDate: e.startDate,
						endDate: e.endDate,
						isFullDay: e.isFullDay,
						times: e.times
					}))
				});

				// Process exceptions
				for (const exception of exceptions) {
					this.logger.log('Processing exception', {
						exceptionId: exception.id,
						type: exception.type,
						startDate: exception.startDate,
						endDate: exception.endDate,
						isFullDay: exception.isFullDay,
						times: exception.times,
						currentDate: dateStr,
						slotsBeforeCount: daySlots.length
					});

					if (exception.type === ExceptionType.OUT_OF_OFFICE) {
						// Remove time if OOO
						if (exception.isFullDay) {
							this.logger.log(
								'Applying full-day OUT_OF_OFFICE exception',
								{
									exceptionId: exception.id,
									date: dateStr
								}
							);
							daySlots = []; // Clear all slots for full day OOO
						} else if (
							exception.times &&
							exception.times.length > 0
						) {
							this.logger.log(
								'Applying partial-day OUT_OF_OFFICE exception',
								{
									exceptionId: exception.id,
									date: dateStr,
									times: exception.times
								}
							);
							// Convert exception times to UTC Dates before removing
							const utcDateRanges = exception.times.map(
								timeRange => {
									const localStartTimeStr = `${dateStr}T${timeRange.startTime}`;
									const localEndTimeStr = `${dateStr}T${timeRange.endTime}`;

									const startUtcDate = this.localToUtc(
										localStartTimeStr,
										clinicTimezone
									);
									const endUtcDate = this.localToUtc(
										localEndTimeStr,
										clinicTimezone
									);

									return {
										start: startUtcDate,
										end: endUtcDate
									}; // Return Date objects
								}
							);

							// Remove each UTC range
							for (const range of utcDateRanges) {
								daySlots = this.removeAppointmentTime(
									daySlots,
									range.start,
									range.end
								);
							}
						}
					} else if (
						exception.type === ExceptionType.ADDITIONAL_HOURS
					) {
						// Add time for additional hours
						if (exception.times && exception.times.length > 0) {
							this.logger.log(
								'Applying ADDITIONAL_HOURS exception',
								{
									exceptionId: exception.id,
									date: dateStr,
									times: exception.times,
									currentDate: current.toISOString()
								}
							);

							// Convert exception times to UTC Date objects
							const additionalSlots = exception.times.map(
								timeRange => {
									const localStartTimeStr = `${dateStr}T${timeRange.startTime}`;
									const localEndTimeStr = `${dateStr}T${timeRange.endTime}`;

									const startUtcDate = this.localToUtc(
										localStartTimeStr,
										clinicTimezone
									);
									const endUtcDate = this.localToUtc(
										localEndTimeStr,
										clinicTimezone
									);

									// Log conversion details
									this.logger.log(
										'Exception time conversion',
										{
											exceptionId: exception.id,
											localStartTime: localStartTimeStr,
											localEndTime: localEndTimeStr,
											utcStart:
												startUtcDate.toISOString(),
											utcEnd: endUtcDate.toISOString()
										}
									);

									return {
										start: startUtcDate,
										end: endUtcDate
									}; // Return Date objects
								}
							);

							this.logger.log(
								'Created additional slots from exception',
								{
									exceptionId: exception.id,
									date: dateStr,
									additionalSlotsCount:
										additionalSlots.length,
									additionalSlots: additionalSlots.map(
										slot => ({
											start: slot.start.toISOString(),
											end: slot.end.toISOString()
										})
									)
								}
							);

							// Add the new UTC slots to the day's slots
							daySlots = [...daySlots, ...additionalSlots];
						}
					}

					this.logger.log('Exception processed', {
						exceptionId: exception.id,
						date: dateStr,
						slotsAfterCount: daySlots.length
					});
				}

				// Get appointments for this date
				this.logger.log('Fetching appointments for date', {
					clinicUserId,
					date: dateStr
				});

				const appointments = await entityManager
					.createQueryBuilder(AppointmentDoctorsEntity, 'ad')
					.leftJoinAndSelect('ad.appointment', 'a')
					.where('ad.clinic_user_id = :clinicUserId', {
						clinicUserId
					})
					.andWhere('DATE(a.date) = :date', { date: dateStr })
					.andWhere('a.deleted_at IS NULL')
					.getMany();

				this.logger.log('Found appointments for date', {
					clinicUserId,
					date: dateStr,
					appointmentsCount: appointments.length,
					appointments: appointments.map(ad => ({
						id: ad.appointment.id,
						// Assume startTime/endTime from DB are already UTC
						startTime: ad.appointment.startTime,
						endTime: ad.appointment.endTime
					}))
				});

				// Remove appointment times from available slots
				// IMPORTANT: Assumes appointment times are stored as UTC in the database
				for (const ad of appointments) {
					const appointment = ad.appointment;

					// Verify we have Date objects, if not, parse them (assuming UTC)
					const appointmentStart =
						typeof appointment.startTime === 'string'
							? new Date(appointment.startTime)
							: appointment.startTime;
					const appointmentEnd =
						typeof appointment.endTime === 'string'
							? new Date(appointment.endTime)
							: appointment.endTime;

					// Check if dates are valid
					if (
						isNaN(appointmentStart.getTime()) ||
						isNaN(appointmentEnd.getTime())
					) {
						this.logger.error(
							'Invalid appointment date/time from database',
							{
								appointmentId: appointment.id,
								startTime: appointment.startTime,
								endTime: appointment.endTime
							}
						);
						continue; // Skip this appointment
					}

					this.logger.log(
						'Processing appointment (using UTC from DB)',
						{
							appointmentId: appointment.id,
							date: dateStr,
							utcStartTime: appointmentStart.toISOString(),
							utcEndTime: appointmentEnd.toISOString(),
							slotsBeforeCount: daySlots.length
						}
					);

					daySlots = this.removeAppointmentTime(
						daySlots,
						appointmentStart,
						appointmentEnd
					);

					this.logger.log('Appointment processed', {
						appointmentId: appointment.id,
						date: dateStr,
						slotsAfterCount: daySlots.length
					});
				}

				// Save processed slots (these are now UTC)
				for (const slot of daySlots) {
					await entityManager.save(ClinicAvailabilitySlot, {
						clinicUserId,
						date: dateStr,
						startTime: slot.start, // Already UTC
						endTime: slot.end, // Already UTC
						isAvailable: true
					});
				}

				// Log the final UTC slots generated for this day
				this.logger.log('Saved final UTC slots for day', {
					clinicUserId,
					date: dateStr,
					dayOfWeek,
					slotsCount: daySlots.length,
					slots: daySlots.map(slot => ({
						start: slot.start.toISOString(),
						end: slot.end.toISOString()
					}))
				});

				// Run defragmentation for this day to merge adjacent slots
				if (daySlots.length > 1) {
					this.logger.log('Running defragmentation for day', {
						clinicUserId,
						date: dateStr
					});

					// Get all slots for this user and date, ordered by start time
					const slotsToDefragment = await entityManager.find(
						ClinicAvailabilitySlot,
						{
							where: {
								clinicUserId,
								date: dateStr,
								isAvailable: true
							},
							order: {
								startTime: 'ASC'
							}
						}
					);

					if (slotsToDefragment.length > 1) {
						const mergedSlots: ClinicAvailabilitySlot[] = [];
						let currentSlot = slotsToDefragment[0];

						// Iterate through slots looking for adjacent or overlapping ones
						for (let i = 1; i < slotsToDefragment.length; i++) {
							const nextSlot = slotsToDefragment[i];

							// Check if slots are adjacent or overlapping
							if (currentSlot.endTime >= nextSlot.startTime) {
								// Merge by extending current slot's end time if needed
								if (nextSlot.endTime > currentSlot.endTime) {
									currentSlot.endTime = nextSlot.endTime;
								}
								// Mark the next slot for deletion
								await entityManager.remove(nextSlot);
							} else {
								// Slots aren't adjacent - save current and move to next
								mergedSlots.push(currentSlot);
								currentSlot = nextSlot;
							}
						}

						// Add the last slot we were processing
						mergedSlots.push(currentSlot);

						// Save any modified slots
						for (const slot of mergedSlots) {
							await entityManager.save(slot);
						}

						this.logger.log('Defragmentation complete for day', {
							clinicUserId,
							date: dateStr,
							originalSlotCount: slotsToDefragment.length,
							newSlotCount: mergedSlots.length
						});
					}
				}

				// Move to next day
				current.setDate(current.getDate() + 1);
			}

			// Defragmentation is now done for each day individually
		});
	}

	/**
	 * Check if a user is available for a specific time slot
	 * @returns Boolean indicating if the user is available
	 */
	async checkUserAvailability(
		clinicUserId: string,
		date: string,
		startTime: string,
		endTime: string
	): Promise<boolean> {
		this.logger.log('Checking user availability', {
			clinicUserId,
			date,
			startTime,
			endTime
		});

		// Validate date and time format before creating Date objects
		if (
			!this.isValidDateFormat(date) ||
			!this.isValidTimeFormat(startTime) ||
			!this.isValidTimeFormat(endTime)
		) {
			this.logger.error(
				'Invalid date or time format in checkUserAvailability',
				{
					date,
					startTime,
					endTime
				}
			);
			return false;
		}

		// Fetch the clinic user to get the clinic's timezone
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId },
			relations: ['clinic']
		});

		if (!clinicUser || !clinicUser.clinic) {
			this.logger.error('Clinic user or clinic not found', {
				clinicUserId
			});
			return false;
		}

		const clinicTimezone = clinicUser.clinic.timezone || 'UTC';
		this.logger.log('Using clinic timezone for availability check', {
			clinicUserId,
			timezone: clinicTimezone
		});

		// Check if input times have UTC 'Z' suffix or need conversion
		let startTimeStr = startTime;
		let endTimeStr = endTime;

		// If the times come from ISO strings, they might have the time part extracted
		// Check if they could be from a Z-suffixed time
		if (startTime.includes('Z') || endTime.includes('Z')) {
			// Already in UTC format with Z
			this.logger.log(
				'Input times already contain Z suffix, using as UTC',
				{
					startTime,
					endTime
				}
			);
		} else {
			// For plain time strings (HH:MM:SS), construct full ISO string with date
			startTimeStr = `${date}T${startTime}`;
			endTimeStr = `${date}T${endTime}`;
		}

		// Convert to UTC for database comparison
		const start = this.localToUtc(startTimeStr, clinicTimezone);
		const end = this.localToUtc(endTimeStr, clinicTimezone);

		this.logger.log('Converted input times for availability check', {
			localStartTimeStr: startTimeStr,
			localEndTimeStr: endTimeStr,
			utcStart: start.toISOString(),
			utcEnd: end.toISOString()
		});

		// Verify the Date objects are valid
		if (isNaN(start.getTime()) || isNaN(end.getTime())) {
			this.logger.error(
				'Invalid date objects created from input in checkUserAvailability',
				{
					date,
					startTime,
					endTime,
					start: start.toString(),
					end: end.toString()
				}
			);
			return false;
		}

		// Count overlapping slots that fully contain the requested time
		const count = await this.availabilityRepository.count({
			where: {
				clinicUserId,
				date,
				// FIXED: Use single condition for proper slot availability check
				// A slot is available if its start time is <= requested start AND its end time is >= requested end
				startTime: LessThanOrEqual(start),
				endTime: MoreThanOrEqual(end),
				isAvailable: true
			}
		});

		return count > 0;
	}

	/**
	 * Find all users available during a given slot
	 * Optionally filter by role or other criteria
	 * @returns Array of clinic user IDs
	 */
	async findAvailableUsers(
		clinicId: string,
		date: string,
		startTime: string,
		endTime: string,
		filters?: any
	): Promise<string[]> {
		this.logger.log('Finding available users', {
			clinicId,
			date,
			startTime,
			endTime,
			filters
		});

		// Validate date and time format before proceeding
		if (
			!this.isValidDateFormat(date) ||
			!this.isValidTimeFormat(startTime) ||
			!this.isValidTimeFormat(endTime)
		) {
			this.logger.error(
				'Invalid date or time format in findAvailableUsers',
				{
					date,
					startTime,
					endTime
				}
			);
			return [];
		}

		// Fetch the clinic to get its timezone
		// We need the timezone to convert the input startTime/endTime to UTC
		const clinic = await this.dataSource.manager.findOne(ClinicEntity, {
			where: { id: clinicId }
		});

		if (!clinic) {
			this.logger.error('Clinic not found in findAvailableUsers', {
				clinicId
			});
			return [];
		}

		const clinicTimezone = clinic.timezone || 'UTC'; // Access timezone directly from fetched clinic entity
		this.logger.log('Using clinic timezone for finding available users', {
			clinicId,
			timezone: clinicTimezone
		});

		// Get all users from this clinic matching filters
		const queryBuilder = this.clinicUserRepository
			.createQueryBuilder('cu')
			.where('cu.clinicId = :clinicId', { clinicId });

		// Apply role filter if provided
		if (filters?.role) {
			queryBuilder
				.innerJoin('cu.user', 'u')
				.innerJoin('u.role', 'r')
				.andWhere('r.name = :role', { role: filters.role });
		}

		// Apply specialty filter if provided
		if (filters?.specialty) {
			queryBuilder.andWhere(
				'cu.specialties @> ARRAY[:specialty]::varchar[]',
				{
					specialty: filters.specialty
				}
			);
		}

		// Get clinic users matching criteria
		const clinicUsers = await queryBuilder.getMany();
		const clinicUserIds = clinicUsers.map(cu => cu.id);

		if (clinicUserIds.length === 0) {
			this.logger.log('No clinic users found matching filters', {
				clinicId,
				filters
			});
			return [];
		}

		// Check if input times have UTC 'Z' suffix or need conversion
		let startTimeStr = startTime;
		let endTimeStr = endTime;

		// If the times come from ISO strings, they might have the time part extracted
		// Check if they could be from a Z-suffixed time
		if (startTime.includes('Z') || endTime.includes('Z')) {
			// Already in UTC format with Z
			this.logger.log(
				'Input times already contain Z suffix, using as UTC',
				{
					startTime,
					endTime
				}
			);
		} else {
			// For plain time strings (HH:MM:SS), construct full ISO string with date
			startTimeStr = `${date}T${startTime}`;
			endTimeStr = `${date}T${endTime}`;
		}

		// Convert input times to UTC Date objects
		const startUtc = this.localToUtc(startTimeStr, clinicTimezone);
		const endUtc = this.localToUtc(endTimeStr, clinicTimezone);

		this.logger.log('Converted input times to UTC for query', {
			localStartTimeStr: startTimeStr,
			localEndTimeStr: endTimeStr,
			clinicTimezone,
			utcStart: startUtc.toISOString(),
			utcEnd: endUtc.toISOString()
		});

		// Verify the Date objects are valid
		if (isNaN(startUtc.getTime()) || isNaN(endUtc.getTime())) {
			this.logger.error('Invalid UTC date objects created from input', {
				date,
				startTime,
				endTime,
				start: startUtc.toString(),
				end: endUtc.toString()
			});
			return [];
		}

		// Find users with available slots covering the requested UTC time range
		const availableSlots = await this.availabilityRepository
			.createQueryBuilder('slot')
			.where('slot.clinicUserId IN (:...clinicUserIds)', {
				clinicUserIds
			})
			.andWhere('slot.date = :date', { date })
			// FIXED: Correct comparison - slot's startTime must be <= startUtc and endTime must be >= endUtc
			.andWhere('slot.startTime <= :start AND slot.endTime >= :end', {
				start: startUtc,
				end: endUtc
			})
			.andWhere('slot.isAvailable = true')
			.getMany();

		// Return unique clinic user IDs with availability
		const availableUserIds = [
			...new Set(availableSlots.map(slot => slot.clinicUserId))
		];

		this.logger.log('Found available users for the specified slot', {
			clinicId,
			date,
			startTime,
			endTime,
			availableUserCount: availableUserIds.length,
			availableUserIds
		});

		return availableUserIds;
	}

	/**
	 * Find available slots of a specific duration
	 * Useful for finding appointment slots of a specific length
	 * @param clinicUserId - The ID of the clinic user to check availability for
	 * @param date - The date to check in YYYY-MM-DD format
	 * @param durationMinutes - The duration of the slot in minutes
	 * @returns Array of available time slots (formatted in clinic's local time) that can fit the requested duration
	 */
	async findAvailableSlotsWithDuration(
		clinicUserId: string,
		date: string,
		durationMinutes: number
	): Promise<Array<{ startTime: string; endTime: string }>> {
		this.logger.log('Finding available slots with duration', {
			clinicUserId,
			date,
			durationMinutes
		});

		// Validate date format before proceeding
		if (!this.isValidDateFormat(date)) {
			this.logger.error(
				'Invalid date format in findAvailableSlotsWithDuration',
				{ date }
			);
			return [];
		}

		// Fetch clinic user and clinic to get timezone
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId },
			relations: ['clinic']
		});

		if (!clinicUser || !clinicUser.clinic) {
			this.logger.error(
				'Clinic user or clinic not found for duration check',
				{
					clinicUserId
				}
			);
			return [];
		}
		const clinicTimezone = clinicUser.clinic.timezone || 'UTC';

		// Get all available slots (stored in UTC) for this date
		const slots = await this.availabilityRepository.find({
			where: {
				clinicUserId,
				date,
				isAvailable: true
			},
			order: {
				startTime: 'ASC'
			}
		});

		const results: Array<{ startTime: string; endTime: string }> = [];

		// For each UTC slot, find all possible appointment times of the requested duration
		for (const slot of slots) {
			// slot.startTime and slot.endTime are UTC Date objects from the DB
			const slotStart = new Date(slot.startTime);
			const slotEnd = new Date(slot.endTime);

			// Calculate slot duration in minutes
			const slotDurationMinutes =
				(slotEnd.getTime() - slotStart.getTime()) / (1000 * 60);

			// Skip slots that are too short
			if (slotDurationMinutes < durationMinutes) {
				continue;
			}

			// Generate all possible appointment start times (UTC) at 5-minute intervals
			const intervalMinutes = 5; // 5-minute intervals
			const possibleIntervals =
				Math.floor(
					(slotDurationMinutes - durationMinutes) / intervalMinutes
				) + 1;

			for (let i = 0; i < possibleIntervals; i++) {
				// Calculate potential appointment start time in UTC
				const appointmentStart = new Date(slotStart);
				appointmentStart.setMinutes(
					appointmentStart.getMinutes() + i * intervalMinutes
				);

				// Calculate potential appointment end time in UTC
				const appointmentEnd = new Date(appointmentStart);
				appointmentEnd.setMinutes(
					appointmentEnd.getMinutes() + durationMinutes
				);

				// Ensure we don't exceed the slot end time (comparison in UTC)
				if (appointmentEnd > slotEnd) {
					break;
				}

				// Format the calculated UTC times into the clinic's local time for the response
				const startTimeLocal = formatInTimeZone(
					appointmentStart,
					clinicTimezone,
					'HH:mm' // Use desired format (e.g., HH:mm)
				);

				const endTimeLocal = formatInTimeZone(
					appointmentEnd,
					clinicTimezone,
					'HH:mm' // Use desired format (e.g., HH:mm)
				);

				results.push({
					startTime: startTimeLocal,
					endTime: endTimeLocal
				});
			}
		}

		this.logger.log('Returning available slots with duration', {
			clinicUserId,
			date,
			durationMinutes,
			clinicTimezone,
			slotCount: results.length
		});

		return results;
	}

	/**
	 * Get the next available slot after a specified time
	 * @returns Next available slot (formatted in clinic's local time) or null if none found
	 */
	async getNextAvailableSlot(
		clinicUserId: string,
		fromDate: string,
		fromTime: string
	): Promise<{ date: string; startTime: string; endTime: string } | null> {
		this.logger.log('Getting next available slot', {
			clinicUserId,
			fromDate,
			fromTime
		});

		// Validate date and time format
		if (
			!this.isValidDateFormat(fromDate) ||
			!this.isValidTimeFormat(fromTime)
		) {
			this.logger.error(
				'Invalid date or time format in getNextAvailableSlot',
				{ fromDate, fromTime }
			);
			return null;
		}

		// Fetch clinic user and clinic to get timezone
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId },
			relations: ['clinic']
		});

		if (!clinicUser || !clinicUser.clinic) {
			this.logger.error(
				'Clinic user or clinic not found for next slot check',
				{
					clinicUserId
				}
			);
			return null;
		}
		const clinicTimezone = clinicUser.clinic.timezone || 'UTC';

		// Check if input time has UTC 'Z' suffix or needs conversion
		let fromTimeStr = fromTime;

		// If the time comes from ISO string, it might have the time part extracted
		// Check if it could be from a Z-suffixed time
		if (fromTime.includes('Z')) {
			// Already in UTC format with Z
			this.logger.log(
				'Input time already contains Z suffix, using as UTC',
				{
					fromTime
				}
			);
		} else {
			// For plain time string (HH:MM:SS), construct full ISO string with date
			fromTimeStr = `${fromDate}T${fromTime}`;
		}

		// Convert input time to UTC
		const fromDateTimeUtc = this.localToUtc(fromTimeStr, clinicTimezone);

		// Verify the UTC Date object is valid
		if (isNaN(fromDateTimeUtc.getTime())) {
			this.logger.error(
				'Invalid UTC date object created from input in getNextAvailableSlot',
				{
					fromDate,
					fromTime,
					fromDateTimeUtc: fromDateTimeUtc.toString()
				}
			);
			return null;
		}

		this.logger.log('Converted input time to UTC for next slot query', {
			localFromDateTimeStr: fromTimeStr,
			clinicTimezone,
			fromDateTimeUtc: fromDateTimeUtc.toISOString()
		});

		// Search in the next 90 days (from the UTC time)
		const maxDate = new Date(fromDateTimeUtc);
		maxDate.setDate(maxDate.getDate() + 90);
		const maxDateStr = maxDate.toISOString().split('T')[0];

		// Find the first available slot (stored in UTC) after the specified UTC time
		const nextSlot = await this.availabilityRepository.findOne({
			where: [
				// Slots on the same day, starting at or after the specified UTC time
				{
					clinicUserId,
					date: fromDate,
					startTime: MoreThanOrEqual(fromDateTimeUtc), // Compare with UTC time
					isAvailable: true
				},
				// Slots on days after the specified fromDate
				{
					clinicUserId,
					// Calculate the next day based on the original fromDate string
					date: Between(this.getNextDay(fromDate), maxDateStr),
					isAvailable: true
				}
			],
			order: {
				date: 'ASC',
				startTime: 'ASC'
			}
		});

		if (!nextSlot) {
			this.logger.log('No next available slot found', {
				clinicUserId,
				fromDate,
				fromTime
			});
			return null;
		}

		// nextSlot.startTime and nextSlot.endTime are UTC Date objects from the DB
		// Format these UTC times into the clinic's local time for the response
		const startTimeLocal = formatInTimeZone(
			nextSlot.startTime,
			clinicTimezone,
			'HH:mm' // Use desired format
		);

		const endTimeLocal = formatInTimeZone(
			nextSlot.endTime,
			clinicTimezone,
			'HH:mm' // Use desired format
		);

		this.logger.log('Found next available slot', {
			clinicUserId,
			nextSlotDate: nextSlot.date,
			nextSlotStartTimeUTC: nextSlot.startTime.toISOString(),
			nextSlotEndTimeUTC: nextSlot.endTime.toISOString(),
			startTimeLocal,
			endTimeLocal
		});

		return {
			date: nextSlot.date,
			startTime: startTimeLocal,
			endTime: endTimeLocal
		};
	}

	/**
	 * Get dates with no available slots within a range
	 * @returns Array of dates (YYYY-MM-DD) with no availability
	 */
	async getUnavailableDays(
		clinicUserId: string,
		startDate: string,
		endDate: string
	): Promise<string[]> {
		this.logger.log('Getting unavailable days', {
			clinicUserId,
			startDate,
			endDate
		});

		// Validate date formats before proceeding
		if (
			!this.isValidDateFormat(startDate) ||
			!this.isValidDateFormat(endDate)
		) {
			this.logger.error('Invalid date format in getUnavailableDays', {
				startDate,
				endDate
			});
			return [];
		}

		// Ensure startDate is before or equal to endDate
		const startDateObj = new Date(startDate);
		const endDateObj = new Date(endDate);
		if (startDateObj > endDateObj) {
			this.logger.error(
				'Start date is after end date in getUnavailableDays',
				{
					startDate,
					endDate
				}
			);
			return [];
		}

		// First, get all dates in the range with at least one available slot
		const availableDatesResult = await this.availabilityRepository
			.createQueryBuilder('slot')
			.select('DISTINCT slot.date', 'date')
			.where('slot.clinicUserId = :clinicUserId', { clinicUserId })
			.andWhere('slot.date BETWEEN :startDate AND :endDate', {
				startDate,
				endDate
			})
			.andWhere('slot.isAvailable = true')
			.getRawMany();

		this.logger.log('Available dates query result', {
			clinicUserId,
			startDate,
			endDate,
			availableDatesCount: availableDatesResult.length,
			availableDatesResult
		});

		const availableDates = availableDatesResult.map(result => result.date);

		// Check if there are any availability slots for this user
		const totalSlotsCount = await this.availabilityRepository.count({
			where: {
				clinicUserId
			}
		});

		this.logger.log('Total slots for this user', {
			clinicUserId,
			totalSlotsCount
		});

		// Generate all dates in the range
		const allDates: string[] = [];
		const currentDate = new Date(startDate);
		const lastDate = new Date(endDate);

		while (currentDate <= lastDate) {
			allDates.push(currentDate.toISOString().split('T')[0]);
			currentDate.setDate(currentDate.getDate() + 1);
		}

		// Return dates that are not in the available dates list
		return allDates.filter(date => !availableDates.includes(date));
	}

	/**
	 * Get all available slots within a date range
	 * @returns Array of available time slots (formatted in clinic's local time) with date and time
	 */
	async getAllAvailableSlots(
		clinicUserId: string,
		startDate: string,
		endDate: string
	): Promise<Array<{ date: string; startTime: string; endTime: string }>> {
		this.logger.log('Getting all available slots', {
			clinicUserId,
			startDate,
			endDate
		});

		// Validate date formats
		if (
			!this.isValidDateFormat(startDate) ||
			!this.isValidDateFormat(endDate)
		) {
			this.logger.error('Invalid date format in getAllAvailableSlots', {
				startDate,
				endDate
			});
			return [];
		}

		// Ensure startDate is before or equal to endDate
		const startDateObj = new Date(startDate);
		const endDateObj = new Date(endDate);
		if (startDateObj > endDateObj) {
			this.logger.error(
				'Start date is after end date in getAllAvailableSlots',
				{ startDate, endDate }
			);
			return [];
		}

		// Fetch clinic user and clinic to get timezone
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId },
			relations: ['clinic']
		});

		if (!clinicUser || !clinicUser.clinic) {
			this.logger.error(
				'Clinic user or clinic not found for getAllAvailableSlots',
				{
					clinicUserId
				}
			);
			return [];
		}
		const clinicTimezone = clinicUser.clinic.timezone || 'UTC';

		// Find all available slots (stored in UTC) within the date range
		const slots = await this.availabilityRepository.find({
			where: {
				clinicUserId,
				date: Between(startDate, endDate),
				isAvailable: true
			},
			order: {
				date: 'ASC',
				startTime: 'ASC'
			}
		});

		this.logger.log('Fetched UTC slots for getAllAvailableSlots', {
			clinicUserId,
			startDate,
			endDate,
			slotCount: slots.length
		});

		// Format response: Convert UTC times to clinic's local time string
		const formattedSlots = slots.map(slot => {
			const startTimeLocal = formatInTimeZone(
				slot.startTime,
				clinicTimezone,
				'HH:mm' // Use desired format
			);

			const endTimeLocal = formatInTimeZone(
				slot.endTime,
				clinicTimezone,
				'HH:mm' // Use desired format
			);

			return {
				date: slot.date,
				startTime: startTimeLocal,
				endTime: endTimeLocal
			};
		});

		this.logger.log(
			'Returning all available slots formatted for clinic timezone',
			{
				clinicUserId,
				startDate,
				endDate,
				clinicTimezone,
				formattedSlotCount: formattedSlots.length
			}
		);

		return formattedSlots;
	}

	/**
	 * Merge adjacent or overlapping slots to optimize storage and queries
	 * This reduces fragmentation and improves query performance
	 */
	async defragmentSlots(clinicUserId: string, date: string): Promise<void> {
		this.logger.log('Defragmenting availability slots', {
			clinicUserId,
			date
		});

		// Validate date format before proceeding
		if (!this.isValidDateFormat(date)) {
			this.logger.error('Invalid date format in defragmentSlots', {
				date
			});
			return;
		}

		await this.dataSource.transaction(async entityManager => {
			// Get all slots for this user and date, ordered by start time
			const slots = await entityManager.find(ClinicAvailabilitySlot, {
				where: {
					clinicUserId,
					date,
					isAvailable: true
				},
				order: {
					startTime: 'ASC'
				}
			});

			if (slots.length <= 1) {
				this.logger.log(
					'Nothing to defragment, 1 or fewer slots found',
					{ clinicUserId, date }
				);
				return; // Nothing to defragment
			}

			const mergedSlots: ClinicAvailabilitySlot[] = [];
			let currentSlot = slots[0];

			// Iterate through slots looking for adjacent or overlapping ones
			for (let i = 1; i < slots.length; i++) {
				const nextSlot = slots[i];

				// Check if slots are adjacent or overlapping
				// Adjacent: currentSlot.end === nextSlot.start
				// Overlapping: currentSlot.end > nextSlot.start
				if (currentSlot.endTime >= nextSlot.startTime) {
					// Merge by extending current slot's end time if needed
					if (nextSlot.endTime > currentSlot.endTime) {
						currentSlot.endTime = nextSlot.endTime;
					}
					// Mark the next slot for deletion
					await entityManager.remove(nextSlot);
				} else {
					// Slots aren't adjacent - save current and move to next
					mergedSlots.push(currentSlot);
					currentSlot = nextSlot;
				}
			}

			// Add the last slot we were processing
			mergedSlots.push(currentSlot);

			// Save any modified slots
			for (const slot of mergedSlots) {
				await entityManager.save(slot);
			}

			this.logger.log('Defragmentation complete', {
				clinicUserId,
				date,
				originalSlotCount: slots.length,
				newSlotCount: mergedSlots.length
			});
		});
	}

	/**
	 * Validate consistency between slots and source data
	 * Checks that no appointments overlap with available slots (which shouldn't happen)
	 * @param clinicUserId - The clinic user ID to validate
	 * @param date - The date to validate
	 * @returns Boolean indicating if the data is consistent
	 */
	async validateSlotConsistency(
		clinicUserId: string,
		date: string
	): Promise<boolean> {
		this.logger.log('Validating slot consistency', {
			clinicUserId,
			date
		});

		// Validate date format before proceeding
		if (!this.isValidDateFormat(date)) {
			this.logger.error(
				'Invalid date format in validateSlotConsistency',
				{
					date
				}
			);
			return false;
		}

		// Get all appointments for this date and user
		const appointments = await this.appointmentDoctorsRepository
			.createQueryBuilder('ad')
			.leftJoinAndSelect('ad.appointment', 'a')
			.where('ad.clinic_user_id = :clinicUserId', { clinicUserId })
			.andWhere('DATE(a.date) = :date', { date })
			.getMany();

		// Get all slots for this date and user
		const slots = await this.availabilityRepository.find({
			where: {
				clinicUserId,
				date,
				isAvailable: true
			}
		});

		// Check #1: Invalid case would be an appointment overlapping with an available slot
		for (const appointmentDoctor of appointments) {
			const appointment = appointmentDoctor.appointment;
			const appStart = new Date(appointment.startTime);
			const appEnd = new Date(appointment.endTime);

			for (const slot of slots) {
				const slotStart = new Date(slot.startTime);
				const slotEnd = new Date(slot.endTime);

				// Check if appointment overlaps with a slot
				// For valid data, appointments should never overlap with available slots
				if (!(appEnd <= slotStart || appStart >= slotEnd)) {
					this.logger.error(
						'Data inconsistency: Appointment overlaps with available slot',
						{
							appointmentId: appointment.id,
							slotId: slot.id,
							date,
							appStart: appStart.toISOString(),
							appEnd: appEnd.toISOString(),
							slotStart: slotStart.toISOString(),
							slotEnd: slotEnd.toISOString()
						}
					);
					return false;
				}
			}
		}

		// Check #2: Verify working hours are respected in the slots
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId }
		});

		if (!clinicUser) {
			this.logger.error('Validation failed: Clinic user not found', {
				clinicUserId
			});
			return false;
		}

		// Only perform this check if working hours are defined
		if (clinicUser.workingHours && clinicUser.workingHours.workingHours) {
			const dayDate = new Date(date);
			const dayOfWeekIdx = dayDate.getDay(); // 0-6, Sunday to Saturday
			const daysOfWeek = [
				'sunday',
				'monday',
				'tuesday',
				'wednesday',
				'thursday',
				'friday',
				'saturday'
			];
			const dayOfWeek = daysOfWeek[dayOfWeekIdx];

			const dayWorkingHours =
				clinicUser.workingHours.workingHours[dayOfWeek] || [];

			// If no working hours are defined for this day, all slots should be from exceptions
			if (dayWorkingHours.length === 0 && slots.length > 0) {
				// Check if there are any additional hours exceptions that explain these slots
				const additionalHoursExceptions =
					await this.exceptionRepository.find({
						where: [
							{
								clinicUserId,
								startDate: Raw(alias => `${alias} <= :date`, {
									date
								}),
								endDate: IsNull(),
								type: ExceptionType.ADDITIONAL_HOURS as any
							},
							{
								clinicUserId,
								startDate: Raw(alias => `${alias} <= :date`, {
									date
								}),
								endDate: Raw(alias => `${alias} >= :date`, {
									date
								}),
								type: ExceptionType.ADDITIONAL_HOURS as any
							}
						]
					});

				if (additionalHoursExceptions.length === 0) {
					this.logger.error(
						'Data inconsistency: Slots exist on day with no working hours and no additional hours exceptions',
						{
							clinicUserId,
							date,
							slotCount: slots.length
						}
					);
					return false;
				}
			}
		}

		// Check #3: Verify there are no overlapping slots
		// This shouldn't happen if defragmentation is working correctly
		for (let i = 0; i < slots.length; i++) {
			for (let j = i + 1; j < slots.length; j++) {
				const slot1Start = new Date(slots[i].startTime);
				const slot1End = new Date(slots[i].endTime);
				const slot2Start = new Date(slots[j].startTime);
				const slot2End = new Date(slots[j].endTime);

				if (!(slot1End <= slot2Start || slot1Start >= slot2End)) {
					this.logger.error(
						'Data inconsistency: Overlapping slots detected',
						{
							clinicUserId,
							date,
							slot1Id: slots[i].id,
							slot2Id: slots[j].id
						}
					);
					return false;
				}
			}
		}

		this.logger.log('Slot consistency validation passed', {
			clinicUserId,
			date,
			appointmentCount: appointments.length,
			slotCount: slots.length
		});

		return true;
	}

	/**
	 * Handle changes to appointments (create, update, delete)
	 * Updates affected availability slots for all doctors involved
	 * Uses direct update for small changes, SQS for larger ones
	 */
	async handleAppointmentChange(
		appointment: AppointmentEntity,
		action: 'create' | 'update' | 'delete',
		previousAppointment?: Partial<AppointmentEntity>
	): Promise<void> {
		this.logger.log('Handling appointment change', {
			appointmentId: appointment.id,
			action,
			appointmentDate: appointment.date,
			appointmentStartTime: appointment.startTime,
			appointmentEndTime: appointment.endTime,
			hasAppointmentDoctors: !!appointment.appointmentDoctors,
			appointmentDoctorsLength: appointment.appointmentDoctors?.length,
			previousAppointment: previousAppointment
				? {
						date: previousAppointment.date,
						startTime: previousAppointment.startTime,
						endTime: previousAppointment.endTime
					}
				: null
		});

		// Validate appointment data
		if (
			!appointment ||
			!appointment.appointmentDoctors ||
			!appointment.date
		) {
			this.logger.error(
				'Invalid appointment data in handleAppointmentChange',
				{
					appointmentId: appointment?.id,
					action,
					appointmentData: {
						hasAppointment: !!appointment,
						hasAppointmentDoctors:
							!!appointment?.appointmentDoctors,
						hasDate: !!appointment?.date
					}
				}
			);
			return;
		}

		try {
			// Get all doctor IDs associated with this appointment
			const doctorIds = appointment.appointmentDoctors.map(
				ad => ad.doctorId
			);
			if (doctorIds.length === 0) {
				this.logger.error('No doctors associated with appointment', {
					appointmentId: appointment.id,
					action,
					appointmentDoctors: appointment.appointmentDoctors
				});
				return;
			}

			const date = new Date(appointment.date).toISOString().split('T')[0];
			if (!this.isValidDateFormat(date)) {
				this.logger.error('Invalid appointment date format', {
					appointmentId: appointment.id,
					date: appointment.date,
					parsedDate: date,
					action
				});
				return;
			}

			// For updates, also handle the previous date if available and different
			let previousDate: string | null = null;
			if (
				action === 'update' &&
				previousAppointment &&
				previousAppointment.date
			) {
				previousDate = new Date(previousAppointment.date)
					.toISOString()
					.split('T')[0];
				// Only set previousDate if it's different from current date and valid
				if (
					previousDate !== date &&
					this.isValidDateFormat(previousDate)
				) {
					this.logger.log('Processing previous appointment date', {
						appointmentId: appointment.id,
						previousDate: previousAppointment.date,
						parsedPreviousDate: previousDate
					});
				} else {
					previousDate = null; // Same date or invalid format, just process current date
				}
			}

			// For small number of updates (3 or fewer doctors), do it synchronously
			if (doctorIds.length <= 3) {
				for (const doctorId of doctorIds) {
					// Regenerate the current date
					try {
						await this.generateSlotsForUser(
							doctorId,
							new Date(date),
							new Date(date)
						);
						this.logger.log('Regenerated slots synchronously', {
							clinicUserId: doctorId,
							date
						});

						// If updating and there's a different previous date, regenerate that too
						if (previousDate) {
							this.logger.log(
								'Starting slot regeneration for previous date',
								{
									clinicUserId: doctorId,
									previousDate,
									appointmentId: appointment.id
								}
							);

							await this.generateSlotsForUser(
								doctorId,
								new Date(previousDate),
								new Date(previousDate)
							);
						}
					} catch (error) {
						this.logger.error(
							'Error regenerating slots synchronously',
							{
								clinicUserId: doctorId,
								date,
								previousDate,
								appointmentId: appointment.id,
								error
							}
						);
						// If direct update fails, fall back to SQS
						await this.queueSlotRegeneration(doctorId, date);

						// Queue previous date too if needed
						if (previousDate) {
							await this.queueSlotRegeneration(
								doctorId,
								previousDate
							);
						}
					}
				}
			} else {
				// For larger batches, send to SQS for async processing
				const regenerationPromises = doctorIds.map(doctorId =>
					this.queueSlotRegeneration(doctorId, date)
				);

				// Add regeneration for previous date if needed
				if (previousDate) {
					doctorIds.forEach(doctorId => {
						regenerationPromises.push(
							this.queueSlotRegeneration(doctorId, previousDate!)
						);
					});
				}

				await Promise.all(regenerationPromises);
			}

			this.logger.log('Appointment change handling completed', {
				appointmentId: appointment.id,
				action,
				doctorCount: doctorIds.length,
				datesProcessed: previousDate ? [date, previousDate] : [date]
			});
		} catch (error) {
			this.logger.error('Unexpected error in handleAppointmentChange', {
				appointmentId: appointment?.id,
				action,
				error
			});
			throw error; // Re-throw to propagate to caller for proper handling
		}
	}

	/**
	 * Helper to queue slot regeneration via SQS
	 */
	private async queueSlotRegeneration(
		clinicUserId: string,
		date: string
	): Promise<void> {
		// Skip if SQS service is not available
		if (!this.sqsService) {
			this.logger.warn(
				'Cannot queue slot regeneration - SqsService not available',
				{
					clinicUserId,
					date
				}
			);
			return;
		}

		try {
			await this.sqsService.sendMessage({
				queueKey: 'NidanaAvailabilityUpdate',
				messageBody: {
					data: {
						taskType: 'regenerate_slots',
						clinicUserId,
						date
					}
				},
				deduplicationId: `availability-update-${clinicUserId}-${date}-${Date.now()}`
			});
			this.logger.log('Queued slot regeneration', {
				clinicUserId,
				date
			});
		} catch (error) {
			this.logger.error('Failed to queue slot regeneration', {
				clinicUserId,
				date,
				error
			});
			throw error;
		}
	}

	/**
	 * Handle changes to availability exceptions (create, update, delete)
	 * Exceptions can affect multiple days, so this is more complex
	 */
	async handleExceptionChange(
		exception: AvailabilityExceptionEntity,
		action: 'create' | 'update' | 'delete',
		previousException?: Partial<AvailabilityExceptionEntity>
	): Promise<void> {
		this.logger.log('Handling exception change', {
			exceptionId: exception.id,
			action,
			previousException: previousException
				? {
						startDate: previousException.startDate,
						endDate: previousException.endDate
					}
				: null
		});

		// Validate exception data
		if (!exception || !exception.clinicUserId || !exception.startDate) {
			this.logger.error(
				'Invalid exception data in handleExceptionChange',
				{
					exceptionId: exception?.id,
					action
				}
			);
			return;
		}

		// Calculate date range affected by this exception
		const startDate = new Date(exception.startDate);
		if (isNaN(startDate.getTime())) {
			this.logger.error('Invalid exception start date', {
				exceptionId: exception.id,
				startDate: exception.startDate,
				action
			});
			return;
		}

		const endDate = exception.endDate
			? new Date(exception.endDate)
			: startDate;

		if (exception.endDate && isNaN(endDate.getTime())) {
			this.logger.error('Invalid exception end date', {
				exceptionId: exception.id,
				endDate: exception.endDate,
				action
			});
			return;
		}

		// Calculate date range size in days
		const days =
			Math.round(
				(endDate.getTime() - startDate.getTime()) /
					(1000 * 60 * 60 * 24)
			) + 1;

		// Process previous exception dates if this is an update and dates have changed
		let previousStartDate: Date | null = null;
		let previousEndDate: Date | null = null;
		let previousDays = 0;

		if (
			action === 'update' &&
			previousException &&
			previousException.startDate
		) {
			previousStartDate = new Date(previousException.startDate);
			previousEndDate = previousException.endDate
				? new Date(previousException.endDate)
				: previousStartDate;

			// Only process previous dates if they're different from current dates
			if (
				previousStartDate.getTime() !== startDate.getTime() ||
				previousEndDate.getTime() !== endDate.getTime()
			) {
				previousDays =
					Math.round(
						(previousEndDate.getTime() -
							previousStartDate.getTime()) /
							(1000 * 60 * 60 * 24)
					) + 1;

				this.logger.log('Processing previous exception dates', {
					exceptionId: exception.id,
					previousStartDate: previousException.startDate,
					previousEndDate: previousException.endDate,
					previousDays
				});
			} else {
				// Same date range, no need to process previous dates separately
				previousStartDate = null;
				previousEndDate = null;
			}
		}

		// If range is small (5 or fewer days), process synchronously
		if (days <= 5) {
			try {
				// Regenerate for the entire affected date range
				await this.generateSlotsForUser(
					exception.clinicUserId,
					startDate,
					endDate
				);
				this.logger.log(
					'Regenerated slots for exception synchronously',
					{
						exceptionId: exception.id,
						days
					}
				);

				// If updating and there's a different previous date range, regenerate that too
				if (previousStartDate && previousEndDate && previousDays > 0) {
					await this.generateSlotsForUser(
						exception.clinicUserId,
						previousStartDate,
						previousEndDate
					);
					this.logger.log(
						'Regenerated slots for previous exception date range',
						{
							exceptionId: exception.id,
							previousDays
						}
					);
				}
			} catch (error) {
				this.logger.error(
					'Error regenerating slots for exception synchronously',
					{
						exceptionId: exception.id,
						days,
						error
					}
				);
				// If direct update fails, fall back to SQS
				await this.queueSlotRangeRegeneration(
					exception.clinicUserId,
					startDate.toISOString(),
					endDate.toISOString()
				);

				// Queue previous date range too if needed
				if (previousStartDate && previousEndDate && previousDays > 0) {
					await this.queueSlotRangeRegeneration(
						exception.clinicUserId,
						previousStartDate.toISOString(),
						previousEndDate.toISOString()
					);
				}
			}
		} else {
			// For larger ranges, use SQS to process asynchronously
			await this.queueSlotRangeRegeneration(
				exception.clinicUserId,
				startDate.toISOString(),
				endDate.toISOString()
			);

			// Queue previous date range too if needed
			if (previousStartDate && previousEndDate && previousDays > 0) {
				await this.queueSlotRangeRegeneration(
					exception.clinicUserId,
					previousStartDate.toISOString(),
					previousEndDate.toISOString()
				);
			}
		}

		this.logger.log('Exception change handling completed', {
			exceptionId: exception.id,
			action,
			days,
			previousDays
		});
	}

	/**
	 * Helper to queue slot range regeneration via SQS
	 */
	private async queueSlotRangeRegeneration(
		clinicUserId: string,
		startDate: string,
		endDate: string
	): Promise<void> {
		// Skip if SQS service is not available
		if (!this.sqsService) {
			this.logger.warn(
				'Cannot queue slot range regeneration - SqsService not available',
				{
					clinicUserId,
					startDate,
					endDate
				}
			);
			return;
		}

		try {
			await this.sqsService.sendMessage({
				queueKey: 'NidanaAvailabilityUpdate',
				messageBody: {
					data: {
						taskType: 'regenerate_slots_range',
						clinicUserId,
						startDate,
						endDate
					}
				},
				deduplicationId: `availability-update-range-${clinicUserId}-${startDate}-${endDate}-${Date.now()}`
			});
			this.logger.log('Queued slot range regeneration', {
				clinicUserId,
				startDate,
				endDate
			});
		} catch (error) {
			this.logger.error('Failed to queue slot range regeneration', {
				clinicUserId,
				startDate,
				endDate,
				error
			});
			throw error;
		}
	}

	/**
	 * Handle changes to working hours
	 * Since working hours affect a long time period (potentially infinite),
	 * this always uses SQS to process asynchronously
	 */
	async handleWorkingHoursChange(clinicUserId: string): Promise<void> {
		this.logger.log('Handling working hours change', { clinicUserId });

		// Validate clinic user ID
		if (!clinicUserId) {
			this.logger.error(
				'Invalid clinic user ID in handleWorkingHoursChange'
			);
			return;
		}

		// Check if clinic user exists
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId }
		});

		if (!clinicUser) {
			this.logger.error(
				'Clinic user not found in handleWorkingHoursChange',
				{
					clinicUserId
				}
			);
			return;
		}

		// For working hours change, always use SQS since it affects many days
		// Generate for a finite window (today + 90 days)
		const today = new Date();
		const futureDate = new Date();
		futureDate.setDate(today.getDate() + 90); // 90 days horizon

		try {
			await this.queueSlotRangeRegeneration(
				clinicUserId,
				today.toISOString(),
				futureDate.toISOString()
			);

			// Also schedule periodic validation for the affected days
			await this.scheduleValidation(clinicUserId, today, futureDate);

			this.logger.log('Working hours change handling completed', {
				clinicUserId,
				horizon: '90 days'
			});
		} catch (error) {
			this.logger.error('Error handling working hours change', {
				clinicUserId,
				error
			});
			throw error;
		}
	}

	/**
	 * Schedule validation for sample days in a date range
	 * This helps ensure data consistency after large changes
	 */
	private async scheduleValidation(
		clinicUserId: string,
		startDate: Date,
		endDate: Date
	): Promise<void> {
		// Sample up to 5 random days for validation to spread the load
		const totalDays = Math.round(
			(endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
		);
		const sampleCount = Math.min(5, totalDays);
		const sampleDays: string[] = [];

		if (sampleCount <= 0) {
			return;
		}

		// Always validate today to catch immediate issues
		sampleDays.push(startDate.toISOString().split('T')[0]);

		// Add some random future days if needed
		if (sampleCount > 1) {
			// Pick random days without duplicates
			const daySet = new Set<string>([sampleDays[0]]);

			while (daySet.size < sampleCount) {
				const randomDayOffset =
					Math.floor(Math.random() * totalDays) + 1;
				const randomDay = new Date(startDate);
				randomDay.setDate(randomDay.getDate() + randomDayOffset);
				daySet.add(randomDay.toISOString().split('T')[0]);
			}

			sampleDays.push(
				...Array.from(daySet).filter(d => d !== sampleDays[0])
			);
		}

		// Queue validation for each sample day
		this.logger.log('Scheduling validation for sample days', {
			clinicUserId,
			sampleDays
		});

		const validationPromises = sampleDays.map(async (date, index) => {
			// Add delay to spread the load
			const delayMinutes = index * 5; // 5-minute spacing

			await this.sqsService.sendMessage({
				queueKey: 'NidanaAvailabilityUpdate',
				messageBody: {
					data: {
						taskType: 'validate_slots',
						clinicUserId,
						date,
						delayMinutes
					}
				},
				deduplicationId: `availability-validate-${clinicUserId}-${date}-${Date.now()}`
			});
		});

		await Promise.all(validationPromises);
	}

	/**
	 * Helper method to remove time ranges from available slots
	 * Used for handling OUT_OF_OFFICE exceptions.
	 * IMPORTANT: This method now expects UTC Date objects in the `ranges` parameter.
	 */
	private removeTimeRanges(
		slots: Array<{ start: Date; end: Date }>,
		ranges: Array<{ start: Date; end: Date }> // Expects UTC Date objects
	): Array<{ start: Date; end: Date }> {
		let result = [...slots];

		for (const range of ranges) {
			// Directly use the provided UTC start and end times
			const rangeStart = range.start;
			const rangeEnd = range.end;

			// Validate the range dates
			if (
				isNaN(rangeStart.getTime()) ||
				isNaN(rangeEnd.getTime()) ||
				rangeEnd <= rangeStart
			) {
				this.logger.warn('Skipping invalid range in removeTimeRanges', {
					start: rangeStart?.toISOString(),
					end: rangeEnd?.toISOString()
				});
				continue;
			}

			result = this.removeAppointmentTime(result, rangeStart, rangeEnd);
		}

		return result;
	}

	/**
	 * Helper method to create time ranges from exception data
	 * Used for handling ADDITIONAL_HOURS exceptions.
	 * IMPORTANT: This method now expects UTC Date objects in the `ranges` parameter.
	 */
	private createTimeRanges(
		ranges: Array<{ start: Date; end: Date }> // Expects UTC Date objects
	): Array<{ start: Date; end: Date }> {
		this.logger.log('Creating time ranges from UTC exception data', {
			ranges: ranges.map(r => ({
				start: r.start?.toISOString(),
				end: r.end?.toISOString()
			}))
		});

		const result: Array<{ start: Date; end: Date }> = [];

		for (const range of ranges) {
			try {
				const start = range.start;
				const end = range.end;

				// Validate dates
				if (
					!start ||
					!end ||
					isNaN(start.getTime()) ||
					isNaN(end.getTime())
				) {
					this.logger.error(
						'Invalid Date object provided to createTimeRanges',
						{
							start: start?.toISOString(),
							end: end?.toISOString()
						}
					);
					continue;
				}

				// Ensure end time is after start time
				if (end <= start) {
					this.logger.warn(
						'End time is not after start time in createTimeRanges (using provided UTC)',
						{
							start: start.toISOString(),
							end: end.toISOString()
						}
					);
					// If end time is before or same as start time, skip this range
					continue;
				}

				this.logger.log('Created UTC time range from exception data', {
					start: start.toISOString(),
					end: end.toISOString()
				});

				result.push({ start, end });
			} catch (error: any) {
				this.logger.error('Error creating time range from UTC data', {
					start: range.start?.toISOString(),
					end: range.end?.toISOString(),
					error: error.message || String(error)
				});
			}
		}

		return result;
	}

	/**
	 * Helper method to remove appointment times from available slots
	 * Handles different overlap scenarios with proper slot splitting.
	 * Expects all inputs (slots, appointmentStart, appointmentEnd) to be UTC Date objects.
	 */
	private removeAppointmentTime(
		slots: Array<{ start: Date; end: Date }>,
		appointmentStart: Date,
		appointmentEnd: Date
	): Array<{ start: Date; end: Date }> {
		const result: Array<{ start: Date; end: Date }> = [];

		for (const slot of slots) {
			// Appointment completely outside slot - keep slot unchanged
			if (appointmentEnd <= slot.start || appointmentStart >= slot.end) {
				result.push(slot);
				continue;
			}

			// Appointment at start of slot - adjust slot start time
			if (
				appointmentStart <= slot.start &&
				appointmentEnd > slot.start &&
				appointmentEnd < slot.end
			) {
				result.push({ start: appointmentEnd, end: slot.end });
				continue;
			}

			// Appointment at end of slot - adjust slot end time
			if (
				appointmentStart > slot.start &&
				appointmentStart < slot.end &&
				appointmentEnd >= slot.end
			) {
				result.push({ start: slot.start, end: appointmentStart });
				continue;
			}

			// Appointment in middle of slot - split into two slots
			if (appointmentStart > slot.start && appointmentEnd < slot.end) {
				result.push({ start: slot.start, end: appointmentStart });
				result.push({ start: appointmentEnd, end: slot.end });
				continue;
			}

			// Appointment covers entire slot - skip this slot
			// (Implicitly handled by the above conditions)
		}

		return result;
	}

	/**
	 * Helper method to get the next day's date string (YYYY-MM-DD)
	 */
	private getNextDay(dateString: string): string {
		const date = new Date(dateString);
		date.setDate(date.getDate() + 1);
		return date.toISOString().split('T')[0];
	}

	/**
	 * Get available dates for a doctor within a date range
	 * @param clinicUserId The clinic user ID (doctor ID)
	 * @param startDate Start date in YYYY-MM-DD format
	 * @param endDate End date in YYYY-MM-DD format
	 * @returns Array of available dates in YYYY-MM-DD format
	 */
	async getAvailableDatesForDoctor(
		clinicUserId: string,
		startDate: string,
		endDate: string
	): Promise<string[]> {
		this.logger.log('Getting available dates for doctor', {
			clinicUserId,
			startDate,
			endDate
		});

		// Validate date formats
		if (
			!this.isValidDateFormat(startDate) ||
			!this.isValidDateFormat(endDate)
		) {
			this.logger.error(
				'Invalid date format in getAvailableDatesForDoctor',
				{
					startDate,
					endDate
				}
			);
			return [];
		}

		// Ensure startDate <= endDate
		const startDateObj = new Date(startDate);
		const endDateObj = new Date(endDate);
		if (startDateObj > endDateObj) {
			this.logger.error(
				'Start date is after end date in getAvailableDatesForDoctor',
				{
					startDate,
					endDate
				}
			);
			return [];
		}

		const availableDatesResult = await this.availabilityRepository
			.createQueryBuilder('slot')
			.select("DISTINCT TO_CHAR(slot.date, 'YYYY-MM-DD')", 'date')
			.where('slot.clinicUserId = :clinicUserId', { clinicUserId })
			.andWhere('slot.date BETWEEN :startDate AND :endDate', {
				startDate,
				endDate
			})
			.andWhere('slot.isAvailable = true')
			.orderBy('date', 'ASC')
			.getRawMany();

		return availableDatesResult.map(result => result.date);
	}

	/**
	 * Get all time slots for a doctor on a specific date with a specific duration, including availability status
	 * @param clinicUserId The clinic user ID (doctor ID)
	 * @param date The date in YYYY-MM-DD format
	 * @param slotDuration The duration of each slot in minutes (default: 30)
	 * @returns Array of time slots (formatted in clinic's local time) with start/end times and availability status
	 */
	async getAvailableTimeSlotsForDate(
		clinicUserId: string,
		date: string,
		slotDuration: number = 30
	): Promise<Array<TimeSlotResult>> {
		// Use TimeSlotResult type
		this.logger.log('Getting all time slots for date (single user)', {
			clinicUserId,
			date,
			slotDuration
		});

		// Validate date format
		if (!this.isValidDateFormat(date)) {
			this.logger.error(
				'Invalid date format in getAvailableTimeSlotsForDate',
				{ date }
			);
			return [];
		}

		// Fetch clinic user and clinic to get timezone
		const clinicUser = await this.clinicUserRepository.findOne({
			where: { id: clinicUserId },
			relations: ['clinic']
		});

		if (!clinicUser || !clinicUser.clinic) {
			this.logger.error(
				'Clinic user or clinic not found for getAvailableTimeSlotsForDate',
				{
					clinicUserId
				}
			);
			return [];
		}
		const clinicTimezone = clinicUser.clinic.timezone || 'UTC';

		// Get all slots for this date (stored in UTC), both available and unavailable
		const slots = await this.availabilityRepository.find({
			where: {
				clinicUserId,
				date
			},
			order: {
				startTime: 'ASC'
			}
		});

		this.logger.log(
			'Fetched UTC slots query result for getAvailableTimeSlotsForDate',
			{
				clinicUserId,
				date,
				slotsCount: slots.length,
				slots: slots.map(slot => ({
					startTime: slot.startTime.toISOString(),
					endTime: slot.endTime.toISOString(),
					isAvailable: slot.isAvailable
				}))
			}
		);

		const results: Array<TimeSlotResult> = []; // Use TimeSlotResult type

		// For each UTC slot, generate possible appointment times of the requested duration
		for (const slot of slots) {
			// slot.startTime and slot.endTime are UTC Date objects from the DB
			const slotStart = new Date(slot.startTime);
			const slotEnd = new Date(slot.endTime);
			const isAvailable = slot.isAvailable;

			// Calculate slot duration in minutes
			const slotDurationMinutes =
				(slotEnd.getTime() - slotStart.getTime()) / (1000 * 60);

			// Skip slots that are too short for the requested duration
			if (slotDurationMinutes < slotDuration) {
				continue;
			}

			// Generate all possible appointment start times (UTC) at slotDuration intervals
			let currentStart = new Date(slotStart); // UTC

			while (
				currentStart.getTime() + slotDuration * 60 * 1000 <=
				slotEnd.getTime()
			) {
				const currentEnd = new Date( // UTC
					currentStart.getTime() + slotDuration * 60 * 1000
				);

				// Format the UTC start/end times into the clinic's local time string
				const startTimeLocal = formatInTimeZone(
					currentStart,
					clinicTimezone,
					'HH:mm' // Use desired format
				);
				const endTimeLocal = formatInTimeZone(
					currentEnd,
					clinicTimezone,
					'HH:mm' // Use desired format
				);

				results.push({
					startTime: startTimeLocal,
					endTime: endTimeLocal,
					isAvailable: isAvailable
				});

				// Move to the next potential slot start time (UTC)
				currentStart = currentEnd;
			}
		}

		this.logger.log('Returning time slots formatted for clinic timezone', {
			clinicUserId,
			date,
			slotDuration,
			clinicTimezone,
			resultCount: results.length
		});

		return results;
	}

	/**
	 * NEW METHOD: Get all time slots for multiple doctors on a specific date with a specific duration.
	 * @param clinicUserIds Array of clinic user IDs (doctor IDs)
	 * @param date The date in YYYY-MM-DD format
	 * @param slotDuration The duration of each slot in minutes (default: 30)
	 * @returns A Map where keys are clinicUserIds and values are arrays of their time slots
	 */
	async getAvailableTimeSlotsForMultipleDoctors(
		clinicUserIds: string[],
		date: string,
		slotDuration: number = 30
	): Promise<Map<string, Array<TimeSlotResult>>> {
		this.logger.log('Getting all time slots for date (multiple users)', {
			clinicUserIds,
			date,
			slotDuration
		});

		const resultsMap = new Map<string, Array<TimeSlotResult>>();
		if (!clinicUserIds || clinicUserIds.length === 0) {
			this.logger.warn(
				'No clinicUserIds provided to getAvailableTimeSlotsForMultipleDoctors'
			);
			return resultsMap;
		}

		// Validate date format
		if (!this.isValidDateFormat(date)) {
			this.logger.error(
				'Invalid date format in getAvailableTimeSlotsForMultipleDoctors',
				{ date }
			);
			return resultsMap; // Return empty map
		}

		// Fetch clinic users and clinics to get timezones
		// Note: This assumes all requested users belong to clinics with potentially different timezones.
		// If they are always from the SAME clinic (as implied by ClientAvailabilityService logic),
		// we could optimize this to fetch the timezone once.
		// For now, fetch all to handle potentially mixed clinic scenarios.
		const clinicUsers = await this.clinicUserRepository.find({
			where: { id: In(clinicUserIds) },
			relations: ['clinic']
		});

		const timezoneMap = new Map<string, string>();
		clinicUsers.forEach(user => {
			if (user.clinic) {
				timezoneMap.set(user.id, user.clinic.timezone || 'UTC');
			} else {
				timezoneMap.set(user.id, 'UTC'); // Fallback if clinic relation is missing
			}
		});

		// Get all slots for the specified users and date (stored in UTC)
		const allSlots = await this.availabilityRepository.find({
			where: {
				clinicUserId: In(clinicUserIds), // Use IN operator for multiple users
				date
			},
			order: {
				clinicUserId: 'ASC', // Order to help grouping
				startTime: 'ASC'
			}
		});

		this.logger.log(
			'Fetched UTC slots query result for getAvailableTimeSlotsForMultipleDoctors',
			{
				requestedUserIds: clinicUserIds,
				date,
				totalSlotsFound: allSlots.length
			}
		);

		// Group slots by clinicUserId
		const slotsByUser = new Map<string, ClinicAvailabilitySlot[]>();
		allSlots.forEach(slot => {
			if (!slotsByUser.has(slot.clinicUserId)) {
				slotsByUser.set(slot.clinicUserId, []);
			}
			slotsByUser.get(slot.clinicUserId)!.push(slot);
		});

		// Process slots for each user
		for (const [clinicUserId, userSlots] of slotsByUser.entries()) {
			const clinicTimezone = timezoneMap.get(clinicUserId) || 'UTC';
			const userResults: Array<TimeSlotResult> = [];

			for (const slot of userSlots) {
				// slot.startTime and slot.endTime are UTC Date objects from the DB
				const slotStart = new Date(slot.startTime);
				const slotEnd = new Date(slot.endTime);
				const isAvailable = slot.isAvailable;

				// Calculate slot duration in minutes
				const slotDurationMinutes =
					(slotEnd.getTime() - slotStart.getTime()) / (1000 * 60);

				// Skip slots that are too short for the requested duration
				if (slotDurationMinutes < slotDuration) {
					continue;
				}

				// Generate all possible appointment start times (UTC) at slotDuration intervals
				let currentStart = new Date(slotStart); // UTC

				while (
					currentStart.getTime() + slotDuration * 60 * 1000 <=
					slotEnd.getTime()
				) {
					const currentEnd = new Date( // UTC
						currentStart.getTime() + slotDuration * 60 * 1000
					);

					// Format the UTC start/end times into the clinic's local time string
					const startTimeLocal = formatInTimeZone(
						currentStart,
						clinicTimezone,
						'HH:mm' // Use desired format
					);
					const endTimeLocal = formatInTimeZone(
						currentEnd,
						clinicTimezone,
						'HH:mm' // Use desired format
					);

					userResults.push({
						startTime: startTimeLocal,
						endTime: endTimeLocal,
						isAvailable: isAvailable
					});

					// Move to the next potential slot start time (UTC)
					currentStart = currentEnd;
				}
			}

			if (userResults.length > 0) {
				resultsMap.set(clinicUserId, userResults);
			}

			this.logger.log('Processed slots for user', {
				clinicUserId,
				clinicTimezone,
				processedSlotsCount: userResults.length
			});
		}

		this.logger.log(
			'Returning map of time slots formatted for clinic timezones (multiple users)',
			{
				date,
				slotDuration,
				usersWithSlotsCount: resultsMap.size
			}
		);

		return resultsMap;
	}

	/**
	 * Get available dates for multiple doctors within a date range, after applying all relevant filters
	 * @param doctorIds Array of clinic user IDs (doctor IDs)
	 * @param startDate Start date in YYYY-MM-DD format
	 * @param endDate End date in YYYY-MM-DD format
	 * @param options Object containing filtering options
	 * @returns Array of available dates in YYYY-MM-DD format that have at least one available slot after filtering
	 */
	async getFilteredAvailableDatesForDoctors(
		doctorIds: string[],
		startDate: string,
		endDate: string,
		options: {
			clinicTimezone: string;
			workingHours?: any; // ClientBookingWorkingHours type
			minBookingLeadMinutes?: number | null;
			slotDurationMinutes: number;
		}
	): Promise<string[]> {
		this.logger.log('Getting filtered available dates for doctors', {
			doctorIds,
			startDate,
			endDate,
			options: {
				clinicTimezone: options.clinicTimezone,
				hasWorkingHours: !!options.workingHours,
				minBookingLeadMinutes: options.minBookingLeadMinutes
			}
		});

		// Validate inputs
		if (!doctorIds || doctorIds.length === 0) {
			this.logger.warn(
				'No doctorIds provided to getFilteredAvailableDatesForDoctors'
			);
			return [];
		}

		if (
			!this.isValidDateFormat(startDate) ||
			!this.isValidDateFormat(endDate)
		) {
			this.logger.error(
				'Invalid date format in getFilteredAvailableDatesForDoctors',
				{
					startDate,
					endDate
				}
			);
			return [];
		}

		// Ensure startDate <= endDate
		const startDateObj = new Date(startDate);
		const endDateObj = new Date(endDate);
		if (startDateObj > endDateObj) {
			this.logger.error(
				'Start date is after end date in getFilteredAvailableDatesForDoctors',
				{
					startDate,
					endDate
				}
			);
			return [];
		}

		const {
			clinicTimezone,
			workingHours,
			minBookingLeadMinutes,
			slotDurationMinutes
		} = options;

		// Get current date/time in clinic timezone for filtering
		const nowMoment = moment().tz(clinicTimezone);
		const todayMoment = nowMoment.clone().startOf('day');
		const leadTimeThreshold = minBookingLeadMinutes
			? nowMoment.clone().add(minBookingLeadMinutes, 'minutes')
			: null;

		// Fetch all raw availability slots for the date range and all doctors
		const rawDbSlots = await this.availabilityRepository.find({
			where: {
				clinicUserId: In(doctorIds),
				date: Between(startDate, endDate),
				isAvailable: true
			},
			order: {
				date: 'ASC',
				startTime: 'ASC'
			}
		});

		if (rawDbSlots.length === 0) {
			return [];
		}

		// Group raw DB slots by date for efficient processing
		const slotsByDate = new Map<string, ClinicAvailabilitySlot[]>();
		for (const slot of rawDbSlots) {
			if (!slotsByDate.has(slot.date)) {
				slotsByDate.set(slot.date, []);
			}
			slotsByDate.get(slot.date)!.push(slot);
		}

		// Dates that have at least one available slot after all filtering
		const trulyAvailableDates = new Set<string>();

		// Process each date
		for (const [dateStr, slotsForDate] of slotsByDate.entries()) {
			const isToday = moment(dateStr).isSame(todayMoment, 'day');
			let dateHasAvailableSlot = false;

			// Group slots by doctor
			const slotsByDoctor = new Map<string, ClinicAvailabilitySlot[]>();
			for (const slot of slotsForDate) {
				if (!slotsByDoctor.has(slot.clinicUserId)) {
					slotsByDoctor.set(slot.clinicUserId, []);
				}
				slotsByDoctor.get(slot.clinicUserId)!.push(slot);
			}

			// Check each doctor for this date
			for (const doctorSlots of slotsByDoctor.values()) {
				if (dateHasAvailableSlot) break; // Already found an available slot for this date

				// Generate discrete slots from continuous availability blocks
				const discreteSlots: Array<{
					startTime: string;
					endTime: string;
				}> = [];

				for (const continuousSlot of doctorSlots) {
					// Convert UTC slot times to clinic timezone for easier filtering
					const slotStartUtc = new Date(continuousSlot.startTime);
					const slotEndUtc = new Date(continuousSlot.endTime);

					// Calculate slot duration in minutes
					const slotDurationMs =
						slotEndUtc.getTime() - slotStartUtc.getTime();
					const slotDurationMinTotal = slotDurationMs / (60 * 1000);

					// Skip if slot is too short for requested duration
					if (slotDurationMinTotal < slotDurationMinutes) {
						continue;
					}

					// Generate discrete slots at the required duration intervals
					let currentSlotStart = new Date(slotStartUtc);

					while (
						currentSlotStart.getTime() +
							slotDurationMinutes * 60 * 1000 <=
						slotEndUtc.getTime()
					) {
						const currentSlotEnd = new Date(
							currentSlotStart.getTime() +
								slotDurationMinutes * 60 * 1000
						);

						// Format the slot times in clinic timezone
						const startTimeLocal = formatInTimeZone(
							currentSlotStart,
							clinicTimezone,
							'HH:mm'
						);
						const endTimeLocal = formatInTimeZone(
							currentSlotEnd,
							clinicTimezone,
							'HH:mm'
						);

						discreteSlots.push({
							startTime: startTimeLocal,
							endTime: endTimeLocal
						});

						// Move to next slot start
						currentSlotStart = currentSlotEnd;
					}
				}

				if (discreteSlots.length === 0) {
					continue;
				}

				// Apply working hours filter if provided
				let filteredSlots = [...discreteSlots];

				if (workingHours) {
					const dayOfWeek = moment(dateStr)
						.format('dddd')
						.toLowerCase();
					const daySchedule = workingHours[dayOfWeek];

					if (Array.isArray(daySchedule) && daySchedule.length > 0) {
						const workingIntervals = daySchedule.filter(
							interval =>
								interval.isWorkingDay &&
								interval.startTime &&
								interval.endTime
						);

						if (workingIntervals.length > 0) {
							filteredSlots = filteredSlots.filter(slot => {
								const slotStartMoment = moment.tz(
									`${dateStr} ${slot.startTime}`,
									'YYYY-MM-DD HH:mm',
									clinicTimezone
								);
								const slotEndMoment = moment.tz(
									`${dateStr} ${slot.endTime}`,
									'YYYY-MM-DD HH:mm',
									clinicTimezone
								);

								return workingIntervals.some(interval => {
									if (
										!interval.startTime ||
										!interval.endTime
									)
										return false;

									const intervalStartMoment = moment.tz(
										`${dateStr} ${interval.startTime}`,
										'YYYY-MM-DD HH:mm',
										clinicTimezone
									);
									const intervalEndMoment = moment.tz(
										`${dateStr} ${interval.endTime}`,
										'YYYY-MM-DD HH:mm',
										clinicTimezone
									);

									return (
										slotStartMoment.isSameOrAfter(
											intervalStartMoment
										) &&
										slotEndMoment.isSameOrBefore(
											intervalEndMoment
										)
									);
								});
							});
						} else {
							filteredSlots = []; // No working intervals for this day
						}
					} else {
						filteredSlots = []; // No schedule for this day
					}
				}

				if (filteredSlots.length === 0) {
					continue;
				}

				// Apply lead time filter if provided
				if (leadTimeThreshold) {
					filteredSlots = filteredSlots.filter(slot => {
						const slotStartMoment = moment.tz(
							`${dateStr} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);
						return slotStartMoment.isAfter(leadTimeThreshold);
					});
				}

				// Filter out past slots if processing today
				if (isToday) {
					filteredSlots = filteredSlots.filter(slot => {
						const slotStartMoment = moment.tz(
							`${dateStr} ${slot.startTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);
						return slotStartMoment.isAfter(nowMoment);
					});
				}

				// If any slots remain after all filtering, this date is truly available
				if (filteredSlots.length > 0) {
					dateHasAvailableSlot = true;
					break;
				}
			}

			if (dateHasAvailableSlot) {
				trulyAvailableDates.add(dateStr);
			}
		}

		return Array.from(trulyAvailableDates).sort();
	}
}
