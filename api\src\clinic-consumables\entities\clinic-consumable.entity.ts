import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('clinic_consumables')
export class ClinicConsumableEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ name: 'unique_id' })
	uniqueId!: string;

	@Column({ name: 'product_name' })
	productName!: string;

	@Column({ name: 'current_stock' })
	currentStock!: number;

	@Column({ name: 'minimum_quantity' })
	minimumQuantity!: number;
}
