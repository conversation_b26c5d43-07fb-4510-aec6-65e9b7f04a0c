import { Test, TestingModule } from '@nestjs/testing';
import { ClinicService } from './clinic.service';
import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { ClinicEntity } from './entities/clinic.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
	ConflictException,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { Repository, DataSource, QueryRunner, In, DeleteResult } from 'typeorm';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { User } from '../users/entities/user.entity';
import { ClinicUser } from './entities/clinic-user.entity';
import { Brand } from '../brands/entities/brand.entity';
import { ClinicConsumblesService } from '../clinic-consumables/clinic-consumbles.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import { UsersService } from '../users/users.service';
import {
	CreateClinicRoomDto,
	UpdateClinicRoomDto
} from './dto/create-clinic-room.dto';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { LongTermMedicationEntity } from '../long-term-medications/entities/long-term-medication.entity';
import { FormatService } from '../utils/excel/format.service';
import { ReadService } from '../utils/excel/read.service';
import { BrandService } from '../brands/brands.service';

jest.mock('../utils/excel/read.service');
jest.mock('../utils/excel/format.service');

describe('ClinicService', () => {
	let service: ClinicService;
	let clinicRepository: jest.Mocked<Repository<ClinicEntity>>;
	let clinicRoomRepository: jest.Mocked<Repository<ClinicRoomEntity>>;
	let clinicUserRepository: jest.Mocked<Repository<ClinicUser>>;
	let userRepository: jest.Mocked<Repository<User>>;
	let dataSource: jest.Mocked<DataSource>;
	let queryRunner: jest.Mocked<QueryRunner>;
	let mailService: jest.Mocked<SESMailService>;
	let consumblesService: jest.Mocked<ClinicConsumblesService>;
	let clinicMedicationsService: jest.Mocked<ClinicMedicationsService>;
	let productsService: jest.Mocked<ClinicProductsService>;
	let clinicServicesService: jest.Mocked<ClinicServicesService>;
	let vaccinationService: jest.Mocked<ClinicVaccinationsService>;
	let clinicLabReportService: jest.Mocked<ClinicLabReportService>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockClinics: ClinicEntity[] = [
		{
			id: 'uuid_clinic_1',
			mobile: '**********',
			name: 'Test 1 Clinic',
			createdAt: new Date(),
			updatedAt: new Date(),
			addressLine1: '',
			addressLine2: '',
			city: '',
			addressPincode: '',
			state: '',
			country: '',
			email: '',
			website: '',
			logoUrl: '',
			gstNumber: '',
			drugLicenseNumber: '',
			phoneNumbers: [],
			brandId: '',
			brand: new Brand(),
			isOnboarded: false,
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			clinicUsers: [],
			adminFirstName: '',
			adminLastName: '',
			adminEmail: '',
			adminMobile: '',
			working_hours: {},
			isActive: true,
			clinicLogo: 'filekey',
			documentLibrary: []
		},
		{
			id: 'uuid_clinic_2',
			mobile: '**********',
			name: 'Test 2 Clinic',
			createdAt: new Date(),
			updatedAt: new Date(),
			addressLine1: '',
			addressLine2: '',
			city: '',
			addressPincode: '',
			state: '',
			country: '',
			email: '',
			website: '',
			logoUrl: '',
			gstNumber: '',
			drugLicenseNumber: '',
			phoneNumbers: [],
			brandId: '',
			brand: new Brand(),
			isOnboarded: false,
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			clinicUsers: [],
			adminFirstName: '',
			adminLastName: '',
			adminEmail: '',
			adminMobile: '',
			working_hours: {},
			isActive: true,
			clinicLogo: 'filekey',
			documentLibrary: []
		},
		{
			id: 'uuid_clinic_3',
			mobile: '**********',
			name: 'Test 3 Clinic',
			createdAt: new Date(),
			updatedAt: new Date(),
			addressLine1: '',
			addressLine2: '',
			city: '',
			addressPincode: '',
			state: '',
			country: '',
			email: '',
			website: '',
			logoUrl: '',
			gstNumber: '',
			drugLicenseNumber: '',
			phoneNumbers: [],
			brandId: '',
			brand: new Brand(),
			isOnboarded: false,
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			clinicUsers: [],
			adminFirstName: '',
			adminLastName: '',
			adminEmail: '',
			adminMobile: '',
			working_hours: {},
			isActive: true,
			clinicLogo: 'filekey',
			documentLibrary: []
		}
	];

	beforeEach(async () => {
		queryRunner = {
			connect: jest.fn(),
			startTransaction: jest.fn(),
			commitTransaction: jest.fn(),
			rollbackTransaction: jest.fn(),
			release: jest.fn(),
			manager: {
				update: jest.fn(),
				create: jest.fn(),
				save: jest.fn(),
				findOne: jest.fn().mockResolvedValue(mockClinics),
				find: jest.fn().mockResolvedValue(mockClinics)
			}
		} as unknown as jest.Mocked<QueryRunner>;

		dataSource = {
			createQueryRunner: jest.fn().mockReturnValue(queryRunner)
		} as unknown as jest.Mocked<DataSource>;
		mailService = {
			sendMail: jest.fn()
		} as unknown as jest.Mocked<SESMailService>;

		logger = {
			log: jest.fn(),
			error: jest.fn()
		} as unknown as jest.Mocked<WinstonLogger>;

		const mockBrands = {
			create: jest.fn(),
			save: jest.fn(),
			findOne: jest.fn(),
			findAndCount: jest.fn(),
			find: jest.fn(),
			delete: jest.fn()
		};

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicService,
				{
					provide: getRepositoryToken(ClinicEntity),
					useValue: {
						save: jest.fn(),
						findAndCount: jest.fn(),
						findOne: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(ClinicRoomEntity),
					useValue: {
						findOne: jest.fn(),
						findAndCount: jest.fn(),
						create: jest.fn(),
						save: jest.fn(),
						delete: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(ClinicUser),
					useValue: {
						find: jest.fn()
					}
				},
				{
					provide: getRepositoryToken(User),
					useValue: {
						update: jest.fn()
					}
				},
				{
					provide: DataSource,
					useValue: {
						createQueryRunner: jest.fn().mockReturnValue({
							connect: jest.fn(),
							startTransaction: jest.fn(),
							commitTransaction: jest.fn(),
							rollbackTransaction: jest.fn(),
							release: jest.fn(),
							manager: {
								findOne: jest.fn(),
								save: jest.fn(),
								update: jest.fn()
							}
						})
					}
				},
				{
					provide: SESMailService,
					useValue: mailService
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				},
				{
					provide: DataSource,
					useValue: dataSource
				},
				{
					provide: ClinicConsumblesService,
					useValue: {
						getConsumables: jest.fn(),
						findOneEntry: jest.fn(),
						bulkInsert: jest.fn(),
						deleteItem: jest.fn()
					}
				},
				{
					provide: ClinicMedicationsService,
					useValue: {
						getMedications: jest.fn(),
						findOneEntry: jest.fn(),
						bulkInsert: jest.fn(),
						deleteItem: jest.fn()
					}
				},
				{
					provide: ClinicProductsService,
					useValue: {
						getProducts: jest.fn(),
						findOneEntry: jest.fn(),
						bulkInsert: jest.fn(),
						deleteItem: jest.fn()
					}
				},
				{
					provide: ClinicServicesService,
					useValue: {
						getServices: jest.fn(),
						findOneEntry: jest.fn(),
						bulkInsert: jest.fn(),
						deleteItem: jest.fn()
					}
				},
				{
					provide: ClinicVaccinationsService,
					useValue: {
						getVaccinations: jest.fn(),
						findOneEntry: jest.fn(),
						bulkInsert: jest.fn(),
						deleteItem: jest.fn()
					}
				},
				{
					provide: ClinicLabReportService,
					useValue: {
						getLabReports: jest.fn(),
						findOneEntry: jest.fn(),
						bulkInsert: jest.fn(),
						deleteItem: jest.fn()
					}
				},
				{
					provide: BrandService,
					useValue: mockBrands
				},
				{
					provide: UsersService,
					useValue: {
						findOneByEmail: jest.fn(),
						generateUniquePin: jest.fn().mockResolvedValue('1234')
					}
				}
			]
		}).compile();

		service = module.get<ClinicService>(ClinicService);
		clinicRepository = module.get(getRepositoryToken(ClinicEntity));
		clinicRoomRepository = module.get(getRepositoryToken(ClinicRoomEntity));
		clinicUserRepository = module.get(getRepositoryToken(ClinicUser));
		userRepository = module.get(getRepositoryToken(User));
		service = module.get<ClinicService>(ClinicService);
		clinicRepository = module.get(getRepositoryToken(ClinicEntity));
		consumblesService = module.get(ClinicConsumblesService);
		clinicMedicationsService = module.get(ClinicMedicationsService);
		productsService = module.get(ClinicProductsService);
		clinicServicesService = module.get(ClinicServicesService);
		vaccinationService = module.get(ClinicVaccinationsService);
		clinicLabReportService = module.get(ClinicLabReportService);
		// dataSource = module.get<DataSource>(DataSource);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('Create new clinic', () => {
		it('should throw ConflictException if the clinic name already exists', async () => {
			const mockInputClinicDto: CreateClinicDto = {
				name: 'Test Clinic',
				adminEmail: '<EMAIL>',
				adminFirstName: 'Test',
				adminLastName: 'Admin',
				adminMobile: '**********',
				brandId: 'brand_1'
			};

			(queryRunner.manager.findOne as jest.Mock).mockResolvedValueOnce(
				mockClinics[0]
			);
			await expect(
				service.createClinic(mockInputClinicDto, 'createdBy_userId')
			).rejects.toThrow(ConflictException);
			expect(queryRunner.manager.findOne).toHaveBeenCalledWith(
				ClinicEntity,
				{
					where: { name: mockInputClinicDto.name }
				}
			);
		});
		it('should throw ConflictException if the admin email already exists', async () => {
			const mockInputClinicDto: CreateClinicDto = {
				name: 'Test Clinic',
				adminEmail: '<EMAIL>',
				adminFirstName: 'Test',
				adminLastName: 'Admin',
				adminMobile: '**********',
				brandId: 'brand_1'
			};

			(queryRunner.manager.findOne as jest.Mock)
				.mockResolvedValueOnce(null)
				.mockResolvedValueOnce({ id: 'existing_user_id' });

			await expect(
				service.createClinic(mockInputClinicDto, 'createdBy_userId')
			).rejects.toThrow(ConflictException);
			expect(queryRunner.manager.findOne).toHaveBeenCalledWith(User, {
				where: { email: mockInputClinicDto.adminEmail }
			});
		});

		it('should create a new clinic and send a mail', async () => {
			try {
				const mockInputClinicDto: CreateClinicDto = {
					name: 'Test Clinic',
					adminEmail: '<EMAIL>',
					adminFirstName: 'Test',
					adminLastName: 'Admin',
					adminMobile: '**********',
					brandId: 'brand_1'
				};

				const mockNewClinic: ClinicEntity = {
					...mockInputClinicDto,
					id: 'new_clinic_id',
					createdAt: new Date(),
					updatedAt: new Date(),
					addressLine1: '',
					addressLine2: '',
					city: '',
					addressPincode: '',
					state: '',
					country: '',
					email: '',
					website: '',
					mobile: '',
					logoUrl: '',
					gstNumber: '',
					drugLicenseNumber: '',
					phoneNumbers: [],
					brand: new Brand(),
					isOnboarded: false,
					createdBy: '',
					createdByUser: new User(),
					updatedBy: '',
					updatedByUser: new User(),
					clinicUsers: [],
					working_hours: {},
					isActive: true,
					clinicLogo: 'filekey',
					documentLibrary: []
				};

				const mockNewUser: User = {
					id: 'new_user_id',
					firstName: 'Test',
					lastName: 'Admin',
					email: '<EMAIL>',
					mobileNumber: '**********',
					pin: 'hashedPin',
					roleId: 'mockRoleId'
				} as User;

				(queryRunner.manager.findOne as jest.Mock)
					.mockResolvedValueOnce(null)
					.mockResolvedValueOnce(null);

				(queryRunner.manager.create as jest.Mock)
					.mockReturnValueOnce(mockNewUser)
					.mockReturnValueOnce(mockNewClinic);

				(queryRunner.manager.save as jest.Mock)
					.mockResolvedValueOnce(mockNewUser)
					.mockResolvedValueOnce(mockNewClinic);

				// Mock the generateUniquePin method
				jest.spyOn(
					service['userService'],
					'generateUniquePin'
				).mockResolvedValue('1234');

				const result = await service.createClinic(
					mockInputClinicDto,
					'createdBy_userId'
				);
				expect(result).toEqual(mockNewClinic);
				expect(mailService.sendMail).toHaveBeenCalled();
			} catch (error) {
				expect(error).toBeInstanceOf(InternalServerErrorException);
			}
		});

		describe('getAllClinics', () => {
			it('should return all clinics with pagination', async () => {
				clinicRepository.findAndCount.mockResolvedValue([
					mockClinics,
					2
				]);

				const result = await service.getAllClinics(1, 10);

				expect(result).toEqual({ clinics: mockClinics, total: 2 });
				expect(clinicRepository.findAndCount).toHaveBeenCalledWith({
					skip: 0,
					take: 10,
					order: { createdAt: 'DESC' }
				});
			});
		});

		describe('Update basic clinic info', () => {
			it('should update basic clinic details successfully', async () => {
				const mockUpdateDto: UpdateBasicClinicDto = {
					name: 'Updated Clinic',
					adminFirstName: 'Updated',
					adminLastName: 'Admin',
					adminEmail: '<EMAIL>',
					adminMobile: '9876543210'
				};

				const mockClinic = mockClinics[0];
				const mockUser = {
					id: 'user_id',
					email: '<EMAIL>'
				} as User;

				(
					queryRunner.manager.findOne as jest.Mock
				).mockResolvedValueOnce(mockClinic); // Find clinic
				(
					queryRunner.manager.findOne as jest.Mock
				).mockResolvedValueOnce({
					clinicId: mockClinic.id,
					isPrimary: true,
					user: mockUser
				}); // Find primary admin
				(
					queryRunner.manager.findOne as jest.Mock
				).mockResolvedValueOnce(mockUser); // Find user

				const result = await service.updateBasicClinicInfo(
					mockClinic.id,
					mockUpdateDto,
					'createdBy_userId'
				);
				expect(result.name).toEqual(mockUpdateDto.name);
				expect(queryRunner.manager.save).toHaveBeenCalledTimes(2); // Save user and clinic
			});
		});

		describe('updateBasicClinicInfo', () => {
			it('should update basic clinic info', async () => {
				const mockClinic = { id: '1', name: 'Old Clinic' };
				const mockUser = { id: '1', email: '<EMAIL>' };
				const mockClinicUser = {
					clinicId: '1',
					userId: '1',
					isPrimary: true
				};
				const updateDto: UpdateBasicClinicDto = {
					name: 'New Clinic',
					adminEmail: '<EMAIL>',
					adminFirstName: 'New',
					adminLastName: 'Admin',
					adminMobile: '1234567890'
				};

				const queryRunner = dataSource.createQueryRunner();
				(queryRunner.manager.findOne as jest.Mock)
					.mockResolvedValueOnce(mockClinic)
					.mockResolvedValueOnce(mockClinicUser)
					.mockResolvedValueOnce(mockUser);

				const result = await service.updateBasicClinicInfo(
					'1',
					updateDto,
					'userId'
				);

				expect(result).toEqual({ ...mockClinic, ...updateDto });
				expect(queryRunner.manager.save).toHaveBeenCalledTimes(2);
			});
		});

		describe('updateClinic', () => {
			it('should update clinic', async () => {
				const mockClinic = {
					id: '1',
					name: 'Old Clinic'
				} as ClinicEntity;
				const updateDto: UpdateClinicDto = {
					phoneNumbers: [{ country_code: '+1', number: '1234567890' }]
				};

				(clinicRepository.findOne as jest.Mock).mockResolvedValue(
					mockClinic
				);
				(clinicRepository.save as jest.Mock).mockResolvedValue({
					...mockClinic,
					...updateDto,
					updatedBy: 'userId'
				});

				const result = await service.updateClinic(
					'1',
					updateDto,
					'userId'
				);

				expect(result).toEqual({
					...mockClinic,
					...updateDto,
					updatedBy: 'userId'
				});
				expect(result.phoneNumbers).toEqual([
					{ country_code: '+1', number: '1234567890' }
				]);
			});
		});

		describe('getClinicById', () => {
			it('should return a clinic', async () => {
				const mockClinic = {
					id: '1',
					name: 'Test Clinic'
				} as ClinicEntity;
				(clinicRepository.findOne as jest.Mock).mockResolvedValue(
					mockClinic
				);

				const result = await service.getClinicById('1');

				expect(result).toEqual(mockClinic);
			});

			it('should throw NotFoundException if clinic not found', async () => {
				(clinicRepository.findOne as jest.Mock).mockResolvedValue(null);

				await expect(service.getClinicById('1')).rejects.toThrow(
					NotFoundException
				);
			});
		});

		describe('getClinicRooms', () => {
			it('should return clinic rooms', async () => {
				const mockClinic = {
					id: '1',
					name: 'Test Clinic'
				} as ClinicEntity;
				const mockRooms = [
					{ id: '1', name: 'Room 1' },
					{ id: '2', name: 'Room 2' }
				] as ClinicRoomEntity[];
				(clinicRepository.findOne as jest.Mock).mockResolvedValue(
					mockClinic
				);
				(
					clinicRoomRepository.findAndCount as jest.Mock
				).mockResolvedValue([mockRooms, 2]);

				const result = await service.getClinicRooms('1');

				expect(result).toEqual({ rooms: mockRooms, total: 2 });
			});
		});

		describe('createClinicRoom', () => {
			it('should create a clinic room', async () => {
				const createDto: CreateClinicRoomDto = {
					name: 'New Room',
					clinicId: '1'
				};
				const mockClinic = {
					id: '1',
					name: 'Test Clinic'
				} as ClinicEntity;
				const mockRoom = {
					id: '1',
					...createDto,
					brandId: 'brand-1'
				} as ClinicRoomEntity;

				(clinicRepository.findOne as jest.Mock).mockResolvedValue(
					mockClinic
				);
				(clinicRoomRepository.create as jest.Mock).mockReturnValue(
					mockRoom
				);
				(clinicRoomRepository.save as jest.Mock).mockResolvedValue(
					mockRoom
				);

				const result = await service.createClinicRoom(
					createDto,
					'brand-1'
				);

				expect(result).toEqual(mockRoom);
			});
		});

		describe('updateClinicRoom', () => {
			it('should update a clinic room', async () => {
				const updateDto: UpdateClinicRoomDto = { name: 'Updated Room' };
				const mockRoom = {
					id: '1',
					name: 'Old Room'
				} as ClinicRoomEntity;

				(clinicRoomRepository.findOne as jest.Mock).mockResolvedValue(
					mockRoom
				);
				(clinicRoomRepository.save as jest.Mock).mockResolvedValue({
					...mockRoom,
					...updateDto
				});

				const result = await service.updateClinicRoom('1', updateDto);

				expect(result).toEqual({ ...mockRoom, ...updateDto });
			});
		});

		describe('deleteRoom', () => {
			it('should delete a room', async () => {
				(clinicRoomRepository.delete as jest.Mock).mockResolvedValue({
					affected: 1
				});

				await service.deleteRoom('1');

				expect(clinicRoomRepository.delete).toHaveBeenCalledWith('1');
			});

			it('should throw NotFoundException if room not found', async () => {
				(clinicRoomRepository.delete as jest.Mock).mockResolvedValue({
					affected: 0
				});

				await expect(service.deleteRoom('1')).rejects.toThrow(
					NotFoundException
				);
			});
		});

		describe('deactivateClinic', () => {
			it('should deactivate a clinic and associated users', async () => {
				const mockClinic = {
					id: '1',
					name: 'Test Clinic',
					isActive: true
				} as ClinicEntity;
				const mockClinicUsers = [{ userId: '1' }, { userId: '2' }];

				const queryRunner = dataSource.createQueryRunner();
				(queryRunner.manager.findOne as jest.Mock).mockResolvedValue(
					mockClinic
				);
				(queryRunner.manager.find as jest.Mock).mockResolvedValue(
					mockClinicUsers
				);

				const result = await service.deactivateClinic('1');

				expect(result).toEqual({ ...mockClinic, isActive: false });
				expect(queryRunner.manager.update).toHaveBeenCalledWith(
					User,
					{ id: In(['1', '2']) },
					{ isActive: false }
				);
			});

			it('should throw ConflictException if clinic is already deactivated', async () => {
				const mockClinic = {
					id: '1',
					name: 'Test Clinic',
					isActive: false
				} as ClinicEntity;

				const queryRunner = dataSource.createQueryRunner();
				(queryRunner.manager.findOne as jest.Mock).mockResolvedValue(
					mockClinic
				);

				await expect(service.deactivateClinic('1')).rejects.toThrow(
					ConflictException
				);
			});
		});

		describe('processBulkUpload', () => {
			it('should process bulk upload successfully', async () => {
				const mockFile = {
					buffer: Buffer.from('mock excel content')
				} as Express.Multer.File;

				const mockSheetData = {
					consumables: [{ id: 'c1', name: 'Consumable 1' }],
					medications: [{ id: 'm1', name: 'Medication 1' }]
				};

				(ReadService.readExcelBuffer as jest.Mock).mockResolvedValue(
					mockSheetData
				);

				(
					FormatService.formatClinicConsumables as jest.Mock
				).mockReturnValue({
					insertArray: [{ uniqueId: 'c1', name: 'Consumable 1' }],
					errorArray: []
				});

				(
					FormatService.formatClinicMedications as jest.Mock
				).mockReturnValue({
					insertArray: [{ uniqueId: 'm1', name: 'Medication 1' }],
					errorArray: []
				});

				jest.spyOn(
					service['consumblesService'],
					'findOneEntry'
				).mockResolvedValue(null);
				jest.spyOn(
					service['clinicMedicationsService'],
					'findOneEntry'
				).mockResolvedValue(null);

				jest.spyOn(
					service['consumblesService'],
					'bulkInsert'
				).mockResolvedValue('');
				jest.spyOn(
					service['clinicMedicationsService'],
					'bulkInsert'
				).mockResolvedValue('');

				const result = await service.processBulkUpload(
					mockFile,
					'clinic1',
					'brand1'
				);

				expect(result).toHaveProperty('consumables');
				expect(result).toHaveProperty('medications');
				expect(result.consumables).toHaveProperty('summary');
				expect(result.medications).toHaveProperty('summary');
				expect(
					service['consumblesService'].bulkInsert
				).toHaveBeenCalled();
				expect(
					service['clinicMedicationsService'].bulkInsert
				).toHaveBeenCalled();
			});

			it('should handle errors during bulk upload', async () => {
				const mockFile = {
					buffer: Buffer.from('mock excel content')
				} as Express.Multer.File;

				const mockSheetData = {
					consumables: [{ id: 'c1', name: 'Consumable 1' }]
				};

				(ReadService.readExcelBuffer as jest.Mock).mockResolvedValue(
					mockSheetData
				);

				(
					FormatService.formatClinicConsumables as jest.Mock
				).mockReturnValue({
					insertArray: [{ uniqueId: 'c1', name: 'Consumable 1' }],
					errorArray: []
				});

				jest.spyOn(
					service['consumblesService'],
					'findOneEntry'
				).mockRejectedValue(new Error('DB Error'));

				const result = await service.processBulkUpload(
					mockFile,
					'clinic1',
					'brand1'
				);

				expect(result).toHaveProperty('consumables');
				expect(result.consumables).toHaveProperty('summary');
				expect(result.consumables.summary.failed).toBeGreaterThan(0);
			});
		});

		describe('generateInventoryExcel', () => {
			it('should generate inventory excel successfully', async () => {
				const mockClinic = { id: 'clinic1' } as ClinicEntity;
				clinicRepository.findOne.mockResolvedValue(mockClinic);

				consumblesService.getConsumables.mockResolvedValue([
					{
						uniqueId: 'c1',
						productName: 'Consumable 1',
						id: '',
						clinicId: '',
						brandId: '',
						currentStock: 0,
						minimumQuantity: 0
					}
				]);
				productsService.getProducts.mockResolvedValue([
					{
						uniqueId: 'p1',
						productName: 'Product 1',
						id: '',
						clinicId: '',
						brandId: '',
						chargeablePrice: 0,
						tax: 0,
						currentStock: 0,
						minimumQuantity: 0
					}
				]);
				clinicServicesService.getServices.mockResolvedValue([
					{
						uniqueId: 's1',
						serviceName: 'Service 1',
						id: '',
						clinicId: '',
						brandId: '',
						chargeablePrice: 0,
						tax: 0
					}
				]);
				clinicLabReportService.getLabReports.mockResolvedValue([
					{
						uniqueId: 'l1',
						name: 'Lab Report 1',
						id: '',
						clinicId: '',
						createdAt: new Date(),
						updatedAt: new Date(),
						createdBy: '',
						createdByUser: new User(),
						updatedBy: '',
						updatedByUser: new User(),
						chargeablePrice: 0,
						tax: 0,
						associatedLab: 0,
						description: '',
						brandId: ''
					}
				]);
				vaccinationService.getVaccinations.mockResolvedValue([
					{
						uniqueId: 'v1',
						productName: 'Vaccine 1',
						id: '',
						clinicId: '',
						brandId: '',
						chargeablePrice: 0,
						tax: 0,
						currentStock: 0,
						minimumQuantity: 0
					}
				]);
				clinicMedicationsService.getMedications.mockResolvedValue([
					{
						uniqueId: 'm1',
						name: 'Medication 1',
						clinicId: '',
						createdAt: new Date(),
						updatedAt: new Date(),
						createdBy: '',
						createdByUser: new User(),
						updatedBy: '',
						updatedByUser: new User(),
						isRestricted: '',
						isAddedByUser: false,
						longTermMedication: new LongTermMedicationEntity(),
						brandId: '',
						chargeablePrice: 0,
						tax: 0,
						currentStock: 0,
						minimumQuantity: 0,
						id: ''
					}
				]);

				const result = await service.generateInventoryExcel('clinic1');

				expect(result).toBeInstanceOf(Buffer);
				expect(clinicRepository.findOne).toHaveBeenCalledWith({
					where: { id: 'clinic1' }
				});
				expect(consumblesService.getConsumables).toHaveBeenCalledWith(
					'clinic1'
				);
				expect(productsService.getProducts).toHaveBeenCalledWith(
					'clinic1'
				);
				expect(clinicServicesService.getServices).toHaveBeenCalledWith(
					'clinic1'
				);
				expect(
					clinicLabReportService.getLabReports
				).toHaveBeenCalledWith('clinic1');
				expect(vaccinationService.getVaccinations).toHaveBeenCalledWith(
					'clinic1'
				);
				expect(
					clinicMedicationsService.getMedications
				).toHaveBeenCalledWith('clinic1');
			});

			it('should throw NotFoundException if clinic not found', async () => {
				clinicRepository.findOne.mockResolvedValue(null);

				await expect(
					service.generateInventoryExcel('nonexistent')
				).rejects.toThrow(NotFoundException);
			});
		});

		describe('deleteInventoryItem', () => {
			it('should delete consumable item successfully', async () => {
				const mockDeleteResult: DeleteResult = { affected: 1, raw: {} };
				consumblesService.deleteItem.mockResolvedValue(
					mockDeleteResult
				);

				const result = await service.deleteInventoryItem(
					'consumables',
					'item1'
				);

				expect(result).toEqual({
					message: 'consumables item deleted successfully'
				});
				expect(consumblesService.deleteItem).toHaveBeenCalledWith(
					'item1'
				);
			});

			it('should delete medication item successfully', async () => {
				const mockDeleteResult: DeleteResult = { affected: 1, raw: {} };
				clinicMedicationsService.deleteItem.mockResolvedValue(
					mockDeleteResult
				);

				const result = await service.deleteInventoryItem(
					'medications',
					'item1'
				);

				expect(result).toEqual({
					message: 'medications item deleted successfully'
				});
				expect(
					clinicMedicationsService.deleteItem
				).toHaveBeenCalledWith('item1');
			});

			it('should throw NotFoundException for invalid item type', async () => {
				await expect(
					service.deleteInventoryItem('invalidType', 'item1')
				).rejects.toThrow(NotFoundException);
			});

			it('should throw InternalServerErrorException if deletion fails', async () => {
				productsService.deleteItem.mockRejectedValue(
					new Error('Deletion failed')
				);

				await expect(
					service.deleteInventoryItem('products', 'item1')
				).rejects.toThrow(InternalServerErrorException);
			});
		});
	});
});
