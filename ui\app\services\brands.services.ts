import * as HttpService from './http.service';
import { CREATE_BRAND, GET_BRAND, GET_BRAND_BY_SLUG, GET_BRANDS } from './url.service';

export const createBrand = (brandName: string) => {
    return HttpService.postWithAuth(CREATE_BRAND(brandName), {});
};

export const getBrands = () => {
    return HttpService.getWithAuth(GET_BRANDS());
};

export const getBrand = (id: string) => {
    return HttpService.getWithOutAuth(GET_BRAND(id));
};

export const getBrandBySlug = (slug: string) => {
    return HttpService.getWithOutAuth(GET_BRAND_BY_SLUG(slug));
};
