import { ApiProperty } from '@nestjs/swagger';
import {
	IsEnum,
	IsNotEmpty,
	IsOptional,
	IsUUID,
	IsNumber,
	IsString
} from 'class-validator';
import { EnumAmountType } from '../../payment-details/enums/enum-credit-types';

export class CreateCreditTransactionDto {
	@ApiProperty({
		description: 'The owner id',
		example: 'uuid'
	})
	@IsUUID()
	@IsNotEmpty()
	ownerId!: string;

	@ApiProperty({
		description: 'Amount of credit',
		example: 1500
	})
	@IsNumber()
	@IsNotEmpty()
	amount!: number;

	@ApiProperty({
		description: 'Type of credit transaction',
		example: EnumAmountType.Collect
	})
	@IsEnum(EnumAmountType)
	@IsNotEmpty()
	transactionType!: EnumAmountType;

	@ApiProperty({
		description: 'Description of the transaction',
		example: 'Credit added for future use'
	})
	@IsString()
	@IsNotEmpty()
	description!: string;

	@ApiProperty({
		description: 'Related invoice ID if applicable',
		example: 'uuid',
		required: false
	})
	@IsUUID()
	@IsOptional()
	relatedInvoiceId?: string;

	@ApiProperty({
		description: 'Additional metadata for the transaction',
		example: { source: 'manual_addition', notes: 'Customer requested' },
		required: false
	})
	@IsOptional()
	metadata?: Record<string, any>;
}

export class OwnerCreditBalanceResponseDto {
	@ApiProperty({
		description: 'Owner ID',
		example: 'uuid'
	})
	ownerId!: string;

	@ApiProperty({
		description: 'Current credit balance',
		example: 1500
	})
	creditBalance!: number;

	@ApiProperty({
		description: 'Legacy owner balance (for backward compatibility)',
		example: 2000
	})
	ownerBalance!: number;
}
