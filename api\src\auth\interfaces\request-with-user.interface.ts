import { Request } from 'express';

/**
 * Extended Request interface that includes authenticated user data
 */
export interface RequestWithUser extends Request {
	user: {
		// Primary user identifiers
		id?: string;
		userId?: string; // Alias for id for backward compatibility
		email?: string;

		// Role information
		roleId?: string;
		role?: string; // Alias for roleId for backward compatibility

		// Organization identifiers
		clinicId?: string;
		brandId?: string;

		// Additional user data
		owner?: {
			id: string;
			fullName?: string;
		};
	};
}
