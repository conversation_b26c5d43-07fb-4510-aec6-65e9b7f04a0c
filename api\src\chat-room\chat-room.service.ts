import {
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatRoom } from './chat-room.entity';
import { CreateChatRoomDto } from './dto/create-chat-room.dto';
import { ChatRoomUser } from './chat-room-users.entity';
import * as uuid from 'uuid';
import { CreateChatMessageDto } from './dto/create-chat-message.dto';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { UpdateChatUserRoomDto } from './dto/update-chat-user-room.dto';
@Injectable()
export class ChatRoomService {
	constructor(
		@InjectRepository(ChatRoom)
		private chatRoomRepository: Repository<ChatRoom>,
		@InjectRepository(ChatRoomUser)
		private chatRoomUserRepository: Repository<ChatRoomUser>,
		@InjectRepository(ChatRoomMessage)
		private chatMessageRepository: Repository<ChatRoomMessage>
	) {}

	async create(createChatRoomDto: CreateChatRoomDto): Promise<ChatRoom> {
		const chatRoomId = uuid.v4();
		const chatRoom = await this.chatRoomRepository.save({ id: chatRoomId });
		console.log(chatRoom);
		const chatRoomUserPromises = createChatRoomDto.userIds.map(async id => {
			const createChatRoomUser = await this.chatRoomUserRepository.save({
				chatRoomId: chatRoom.id,
				userId: id
			});

			if (!createChatRoomUser) {
				throw new InternalServerErrorException(
					'Failed in creating an chat room user'
				);
			}
		});

		await Promise.all(chatRoomUserPromises);
		return chatRoom;
	}

	async findAllForUser(userId: string): Promise<ChatRoomUser[]> {
		return this.chatRoomUserRepository.find({
			where: {
				userId: userId
			},
			relations: {
				chatRoom: {
					users: {
						clinicUser: {
							user: true
						}
					}
				}
			},
			select: {
				chatRoom: {
					id: true,
					lastMessage: true,
					updatedAt: true,
					lastMessageSender: true,
					unreadMessage: true,
					users: {
						id: true,
						clinicUser: {
							id: true,
							user: {
								firstName: true,
								lastName: true
							}
						}
					}
				}
			}
		});
	}

	async sendMessage(
		createChatMessageDto: CreateChatMessageDto
	): Promise<ChatRoomMessage> {
		console.log('createChatMessageDto :>> ', createChatMessageDto);
		const { chatRoomId, message, senderId } = createChatMessageDto;

		// Find the chat room to update
		const chatRoom = await this.chatRoomRepository.findOne({
			where: { id: chatRoomId },
			relations: ['messages']
		});

		if (!chatRoom) {
			throw new InternalServerErrorException('Chat room not found');
		}
		// Update the chat room's last message details
		chatRoom.lastMessage = message;
		chatRoom.lastMessageSender = senderId;
		chatRoom.updatedAt = new Date();
		chatRoom.unreadMessage = chatRoom.unreadMessage + 1;
		await this.chatRoomRepository.save(chatRoom);

		const chatRoomMessage = await this.chatMessageRepository.save({
			...createChatMessageDto
		});
		return chatRoomMessage;
	}

	async getChatRoomDetails(chatRoomId: string): Promise<ChatRoom> {
		const chatRoom = await this.chatRoomRepository.findOne({
			where: {
				id: chatRoomId
			},
			select: {
				users: {
					id: true,
					userId: true,
					clinicUser: {
						user: {
							id: true,
							firstName: true,
							lastName: true
						}
					}
				},
				messages: true
			},
			relations: {
				users: {
					clinicUser: true
				},
				messages: {
					user: true
				}
			},
			order: {
				messages: {
					createdAt: 'ASC'
				}
			}
		});
		if (!chatRoom) {
			throw new InternalServerErrorException(
				'No chat room found with given id'
			);
		}
		return chatRoom;
	}

	async updateChatRoom(
		id: string,
		updateChatRoomDto: UpdateChatUserRoomDto
	): Promise<ChatRoom> {
		let chatRoom = await this.chatRoomRepository.findOne({
			where: { id }
		});
		console.log('exectution reached here ', { id, updateChatRoomDto });

		if (!chatRoom) {
			throw new NotFoundException(`chatRoom with ID "${id}" not found`);
		}
		chatRoom = {
			...chatRoom,
			...updateChatRoomDto
		};
		return this.chatRoomRepository.save(chatRoom);
	}
}
