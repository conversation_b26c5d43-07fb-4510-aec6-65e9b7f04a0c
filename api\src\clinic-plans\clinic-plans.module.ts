import { Module } from '@nestjs/common';
import { ClinicPlansService } from './clinic-plans.service';
import { ClinicPlansController } from './clinic-plans.controller';
import { ClinicPlan } from './entities/clinic-plan.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicProductEntity } from '../clinic-products/entities/clinic-product.entity';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { ClinicVaccinationEntity } from '../clinic-vaccinations/entities/clinic-vaccination.entity';
import { ClinicServiceEntity } from '../clinic-services/entities/clinic-service.entity';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
import { ClinicMedicationEntity } from '../clinic-medications/entities/clinic-medication.entity';
import { ClinicLabReportModule } from '../clinic-lab-report/clinic-lab-report.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([
			ClinicPlan,
			ClinicProductEntity,
			ClinicVaccinationEntity,
			ClinicServiceEntity,
			ClinicMedicationEntity
		]),
		ClinicLabReportModule
	],
	controllers: [ClinicPlansController],
	providers: [
		ClinicPlansService,
		ClinicProductsService,
		ClinicVaccinationsService,
		ClinicServicesService,
		ClinicMedicationsService
	]
})
export class ClinicPlansModule {}
