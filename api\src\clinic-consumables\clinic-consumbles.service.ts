import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { ClinicConsumableEntity } from './entities/clinic-consumable.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateConsumableDto } from './dto/create-consumable.dto';
import { UpdateConsumableDto } from './dto/update-consumable.dto';

@Injectable()
export class ClinicConsumblesService {
	constructor(
		@InjectRepository(ClinicConsumableEntity)
		private readonly consumableRepository: Repository<ClinicConsumableEntity>,
		private readonly logger: WinstonLogger
	) {}

	// Make generateUniqueId asynchronous and accept clinicId
	private async generateUniqueId(
		prefix: string,
		clinicId: string
	): Promise<string> {
		const count = await this.consumableRepository.count({
			where: { clinicId }
		});
		const nextNumber = count + 1;
		return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
	}
	async getConsumables(clinicId: string, searchKeyword?: string) {
		if (searchKeyword) {
			if (clinicId) {
				return this.consumableRepository.find({
					where: {
						productName: ILike(`%${searchKeyword}%`),
						clinicId
					}
				});
			}

			return this.consumableRepository.find({
				where: { productName: ILike(`%${searchKeyword}%`) }
			});
		}

		return this.consumableRepository.find({
			where: { clinicId }
		});
	}

	async bulkInsert(consumables: CreateConsumableDto[]): Promise<string> {
		try {
			for (const consumable of consumables) {
				// Check if item exists for the clinic
				const existingConsumable =
					await this.consumableRepository.findOne({
						where: {
							clinicId: consumable.clinicId,
							productName: consumable.productName
						}
					});

				if (existingConsumable) {
					// Update existing item
					Object.assign(existingConsumable, consumable);
					await this.consumableRepository.save(existingConsumable);
				} else {
					// Create new item with unique ID
					const uniqueId = await this.generateUniqueId(
						'C_',
						consumable.clinicId
					);
					await this.consumableRepository.save({
						...consumable,
						uniqueId
					});
				}
			}

			const message = `Bulk insert of ${consumables.length} consumables completed successfully`;
			this.logger.log(
				`Bulk insert completed. Inserted ${consumables.length} consumables.`
			);
			return message;
		} catch (error) {
			this.logger.error('Error during bulk insert of consumables', {
				error
			});
			throw new Error('Failed to insert consumables');
		}
	}

	async create(
		createConsumableDto: CreateConsumableDto
	): Promise<ClinicConsumableEntity> {
		try {
			const uniqueId = await this.generateUniqueId(
				'C_',
				createConsumableDto.clinicId
			);
			const consumable = this.consumableRepository.create({
				...createConsumableDto,
				uniqueId
			});
			return await this.consumableRepository.save(consumable);
		} catch (error) {
			this.logger.error('Error creating consumable', { error });
			throw error;
		}
	}

	async update(
		id: string,
		updateConsumableDto: UpdateConsumableDto
	): Promise<ClinicConsumableEntity> {
		try {
			const consumable = await this.consumableRepository.findOne({
				where: { id }
			});
			if (!consumable) {
				throw new NotFoundException(
					`Consumable with ID ${id} not found`
				);
			}

			Object.assign(consumable, updateConsumableDto);
			return await this.consumableRepository.save(consumable);
		} catch (error) {
			this.logger.error('Error updating consumable', { error });
			throw error;
		}
	}

	async findOne(id: string): Promise<ClinicConsumableEntity> {
		const consumable = await this.consumableRepository.findOne({
			where: { id }
		});
		if (!consumable) {
			throw new NotFoundException(`Consumable with ID ${id} not found`);
		}
		return consumable;
	}

	async remove(id: string): Promise<void> {
		try {
			const result = await this.consumableRepository.delete(id);
			if (result.affected === 0) {
				throw new NotFoundException(
					`Consumable with ID ${id} not found`
				);
			}
		} catch (error) {
			this.logger.error('Error deleting consumable', { error });
			throw error;
		}
	}

	async findOneEntry(criteria: { productName: string; clinicId: string }) {
		return this.consumableRepository.findOne({ where: criteria });
	}

	async deleteItem(itemId: string) {
		return this.consumableRepository.delete(itemId);
	}
}
