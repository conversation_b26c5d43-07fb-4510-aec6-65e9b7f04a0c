import { ApiProperty } from '@nestjs/swagger';
import {
	IsArray,
	IsBoolean,
	IsInt,
	IsObject,
	IsOptional,
	IsString,
	Min,
	ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';

// Defines the structure for a single time slot within a working day.
class WorkingHoursDayDetail {
	@ApiProperty({
		example: '09:00',
		nullable: true,
		description: 'Start time (HH:mm) or null if not set'
	})
	@IsString()
	@IsOptional()
	startTime!: string | null;

	@ApiProperty({
		example: '17:00',
		nullable: true,
		description: 'End time (HH:mm) or null if not set'
	})
	@IsString()
	@IsOptional()
	endTime!: string | null;

	@ApiProperty({
		example: true,
		description: 'Indicates if this is a working day'
	})
	@IsBoolean()
	isWorkingDay!: boolean;
}

// Defines the structure for the working hours across the week.
class WorkingHoursSlotsDto {
	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	monday?: WorkingHoursDayDetail[];

	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	tuesday?: WorkingHoursDayDetail[];

	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	wednesday?: WorkingHoursDayDetail[];

	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	thursday?: WorkingHoursDayDetail[];

	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	friday?: WorkingHoursDayDetail[];

	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	saturday?: WorkingHoursDayDetail[];

	@ApiProperty({ type: [WorkingHoursDayDetail], nullable: true })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => WorkingHoursDayDetail)
	sunday?: WorkingHoursDayDetail[];
}

// Defines the structure for returning basic doctor information.
class DoctorInfo {
	@ApiProperty({
		description: 'Doctor User ID',
		example: '3b67e9e5-2415-4f53-8a67-69dd321bdc0f'
	})
	@IsString()
	id!: string;

	@ApiProperty({ description: 'Doctor Full Name', example: 'Dr. Jane Doe' })
	@IsString()
	name!: string;
}

// Time duration object for various booking time settings
class TimeDurationDto {
	@ApiProperty({
		description: 'Number of days component',
		example: 1,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	days?: number;

	@ApiProperty({
		description: 'Number of hours component',
		example: 12,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	hours?: number;

	@ApiProperty({
		description: 'Number of minutes component',
		example: 30,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	minutes?: number;

	@ApiProperty({
		description: 'Total minutes for backward compatibility',
		example: 2190,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	totalMinutes?: number;
}

// Main DTO for the response of the getClientBookingSettings endpoint.
export class ClientBookingSettingsResponseDto {
	@ApiProperty({
		example: true,
		description: 'Whether client booking is enabled'
	})
	@IsBoolean()
	isEnabled!: boolean;

	@ApiProperty({
		description:
			'Indicates if booking with any doctor is allowed (overrides allowedDoctorIds).',
		example: false
	})
	@IsBoolean()
	allowAllDoctors!: boolean;

	@ApiProperty({
		type: WorkingHoursSlotsDto,
		description:
			"Working hours settings for the clinic (used if specific doctors aren't required or as fallback)"
	})
	@IsObject()
	@IsOptional()
	@ValidateNested()
	@Type(() => WorkingHoursSlotsDto)
	workingHours?: WorkingHoursSlotsDto; // References the nested DTO

	@ApiProperty({
		type: [String],
		example: ['3b67e9e5-2415-4f53-8a67-69dd321bdc0f'],
		description:
			'List of user IDs for doctors specifically allowed for client booking.',
		required: false
	})
	@IsArray()
	@IsString({ each: true })
	@IsOptional()
	allowedDoctorIds?: string[];

	@ApiProperty({
		type: [DoctorInfo],
		description:
			'Detailed information about the doctors allowed for client booking.',
		required: false
	})
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => DoctorInfo)
	@IsOptional()
	allowedDoctorsInfo?: DoctorInfo[];

	@ApiProperty({
		description:
			'Minimum lead time required before an appointment can be booked.',
		type: TimeDurationDto,
		required: false,
		nullable: true
	})
	@ValidateNested()
	@Type(() => TimeDurationDto)
	@IsOptional()
	minBookingLeadTime?: TimeDurationDto;

	@ApiProperty({
		description:
			'Deadline before the appointment time within which modifications/cancellations are allowed.',
		type: TimeDurationDto,
		required: false,
		nullable: true
	})
	@ValidateNested()
	@Type(() => TimeDurationDto)
	@IsOptional()
	modificationDeadlineTime?: TimeDurationDto;

	@ApiProperty({
		description:
			'Maximum time in advance that a client can book an appointment.',
		type: TimeDurationDto,
		required: false,
		nullable: true
	})
	@ValidateNested()
	@Type(() => TimeDurationDto)
	@IsOptional()
	maxAdvanceBookingTime?: TimeDurationDto;

	// For backward compatibility
	@ApiProperty({
		example: 2,
		description:
			'Minimum lead time in hours required before an appointment can be booked (legacy field).',
		required: false
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	minBookingLeadHours?: number;

	@ApiProperty({
		example: 12,
		description:
			'Deadline in hours before the appointment time within which modifications/cancellations are allowed (legacy field).',
		required: false
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	modificationDeadlineHours?: number;
}
