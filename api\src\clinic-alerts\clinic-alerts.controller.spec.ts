import { Test, TestingModule } from '@nestjs/testing';
import { ClinicAlertsController } from './clinic-alerts.controller';
import { ClinicAlertsService } from './clinic-alerts.service';
import { <PERSON>Logger } from '../utils/logger/winston-logger.service';
import { CreateClinicAlertDto } from './dto/create-clinicAlerts.dto';
import { ClinicAlerts } from './entities/clinicAlerts.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { UpdateClinicAlertsDto } from './dto/update-clinicAlerts.dto';
import { RoleService } from '../roles/role.service';

describe('ClinicAlertsController', () => {
	let controller: ClinicAlertsController;
	let clinicAlertService: {
		createClinicAlerts: jest.Mock;
		getClinicAlerts: jest.Mock;
		deleteClinicAlert: jest.Mock;
		updateClinicAlerts: jest.Mock;
	};

	beforeEach(async () => {
		clinicAlertService = {
			createClinicAlerts: jest.fn(),
			getClinicAlerts: jest.fn(),
			deleteClinicAlert: jest.fn(),
			updateClinicAlerts: jest.fn()
		};

		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicAlertsController],
			providers: [
				{
					provide: ClinicAlertsService,
					useValue: clinicAlertService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicAlertsController>(ClinicAlertsController);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create clinic-alert', () => {
		it('should be defined the createClinicAlerts ', () => {
			expect(controller.createClinicAlerts).toBeDefined();
		});

		it('should create clinic-alerts with proper dto', async () => {
			const mockClinicAlertsDto: CreateClinicAlertDto = {
				severity: 'High',
				alertName: 'Fever',
				clinicId: 'c_1'
			};
			const mockClinicAlert: ClinicAlerts = {
				id: 'U_1',
				severity: 'High',
				alertName: 'Fever',
				clinicId: 'c_1',
				brandId: 'b_1',
				clinic: {} as ClinicEntity
			};
			clinicAlertService.createClinicAlerts.mockResolvedValue(
				mockClinicAlert
			);

			const result = await controller.createClinicAlerts(
				mockClinicAlertsDto,
				{ user: { clinicId: 'c_1', brandId: 'b_1' } }
			);

			expect(result).toEqual(mockClinicAlert);
			expect(clinicAlertService.createClinicAlerts).toHaveBeenCalledWith(
				mockClinicAlertsDto,
				'b_1'
			);
		});

		it('should throw error while failed to create clinic-alerts', async () => {
			const mockClinicAlertsDto: CreateClinicAlertDto = {
				severity: 'High',
				alertName: '',
				clinicId: 'c_1'
			};
			const error = new HttpException(
				'fialed to create clinic-alerts',
				HttpStatus.BAD_REQUEST
			);

			clinicAlertService.createClinicAlerts.mockRejectedValue(error);
			await expect(
				controller.createClinicAlerts(mockClinicAlertsDto, {
					user: { clinicId: 'c_1', brandId: 'b_1' }
				})
			).rejects.toThrow(error);

			expect(clinicAlertService.createClinicAlerts).toHaveBeenCalledWith(
				mockClinicAlertsDto,
				'b_1'
			);
		});
	});

	describe('get Clinic-alerts', () => {
		it('should be defined the getClinicAlerts ', () => {
			expect(controller.getClinicAlerts).toBeDefined();
		});
		it('should get clinic-alerts with registered clinicId', async () => {
			const clinicId: string = 'c_1';
			const mockClinicAlerts: ClinicAlerts[] = [
				{
					id: 'U_1',
					severity: 'High',
					alertName: 'Fever',
					clinicId: 'c_1',
					brandId: 'b_1',
					clinic: {} as ClinicEntity
				}
			];

			clinicAlertService.getClinicAlerts.mockResolvedValue(
				mockClinicAlerts
			);

			const result = await controller.getClinicAlerts(clinicId);

			expect(clinicAlertService.getClinicAlerts).toHaveBeenCalled();
			expect(result).toEqual(mockClinicAlerts);
		});
		it('should not return clinic-alerts if clinicId is not registered', async () => {
			const clinicId = 'non_existing';
			const error = new HttpException(
				'no clinic-alerts with requested clinicId',
				HttpStatus.BAD_REQUEST
			);
			clinicAlertService.getClinicAlerts.mockRejectedValue(error);
			await expect(
				controller.getClinicAlerts(clinicId, 'search')
			).rejects.toThrow(error);

			expect(clinicAlertService.getClinicAlerts).toHaveBeenCalledWith(
				clinicId,
				'search'
			);
		});
	});
	describe('delete clinic-alert', () => {
		it('functon should be defined', () => {
			expect(controller.deleteClinicAlert).toBeDefined();
		});
		it('should delete the clinic-alerts with requiest clinic-alert-id', async () => {
			const clinicAlertId = 'existing_uuid';
			clinicAlertService.deleteClinicAlert.mockResolvedValue({
				affected: 1
			});

			const result = await controller.deleteClinicAlert(clinicAlertId);

			expect(clinicAlertService.deleteClinicAlert).toHaveBeenCalledWith(
				clinicAlertId
			);
			expect(result).toEqual({ affected: 1 });
		});
		it('should throw an error if deletion fails', async () => {
			const clinicAlertId = 'non_existing_uuid';
			const error = new HttpException(
				'Error removing clinic-alert',
				HttpStatus.BAD_REQUEST
			);
			clinicAlertService.deleteClinicAlert.mockRejectedValue(error);

			await expect(
				controller.deleteClinicAlert(clinicAlertId)
			).rejects.toThrow('Error removing clinic-alert');

			expect(clinicAlertService.deleteClinicAlert).toHaveBeenCalledWith(
				clinicAlertId
			);
		});
	});
	describe('update clinic-alert', () => {
		it('function should be defined', () => {
			expect(controller.updateClinicAlert).toBeDefined();
		});
		it('should update a clinic-alert', async () => {
			const id = 'clinic-uuid';
			const updateClinicAlertsDto: UpdateClinicAlertsDto = {
				alertName: 'aggressive',
				severity: 'high'
			};

			const updatedClinicAlert: ClinicAlerts = {
				id,
				alertName: 'abc',
				severity: 'medium',
				clinicId: 'c_1',
				brandId: 'b_1',
				clinic: {} as ClinicEntity
			};

			clinicAlertService.updateClinicAlerts.mockResolvedValue(
				updatedClinicAlert
			);

			const result = await controller.updateClinicAlert(
				id,
				updateClinicAlertsDto
			);

			expect(result).toEqual(updatedClinicAlert);
			expect(clinicAlertService.updateClinicAlerts).toHaveBeenCalledWith(
				id,
				updateClinicAlertsDto
			);
		});
	});
});
