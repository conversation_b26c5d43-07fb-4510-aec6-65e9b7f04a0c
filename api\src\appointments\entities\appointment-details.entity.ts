import {
	Column,
	CreateDate<PERSON><PERSON>umn,
	<PERSON><PERSON>ty,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { AppointmentEntity } from './appointment.entity';

@Entity('appointment_details')
export class AppointmentDetailsEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'appointment_id' })
	appointmentId!: string;

	@Column({
		type: 'jsonb',
		nullable: true,
		name: 'details'
	})
	details!: object;

	@CreateDateColumn({ name: 'created_at' })
	createdAt?: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt?: Date;

	@Column({ name: 'prescription_created_at', nullable: true })
	prescriptionCreatedAt!: Date;

	@Column({ type: 'uuid', nullable: true, name: 'created_by' })
	createdBy?: string;

	@Column({ type: 'uuid', nullable: true, name: 'updated_by' })
	updatedBy?: string;

	@OneToOne(() => AppointmentEntity, appointmentEntity => appointmentEntity)
	@JoinColumn({ name: 'appointment_id' })
	appointment!: AppointmentEntity;
}
