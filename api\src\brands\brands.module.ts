import { Module } from '@nestjs/common';
import { BrandService } from './brands.service';
import { BrandController } from './brands.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Brand } from './entities/brand.entity';
import { RoleModule } from '../roles/role.module';

@Module({
	imports: [TypeOrmModule.forFeature([Brand]), RoleModule],
	controllers: [BrandController],
	providers: [BrandService],
	exports: [BrandService]
})
export class BrandsModule {}
