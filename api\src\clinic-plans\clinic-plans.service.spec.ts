import { Test, TestingModule } from '@nestjs/testing';
import { ClinicPlansService } from './clinic-plans.service';
import { ClinicPlan } from './entities/clinic-plan.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { EnumPlanType } from './enums/enum-plan-type';

describe('ClinicPlansService', () => {
  let service: ClinicPlansService;
  let repository: Repository<ClinicPlan>;
  let logger: jest.Mocked<WinstonLogger>;
  let clinicProductsService: jest.Mocked<ClinicProductsService>;
  let clinicVaccinationsService: jest.Mocked<ClinicVaccinationsService>;
  let clinicServicesService: jest.Mocked<ClinicServicesService>;
  let clinicMedicationsService: jest.Mocked<ClinicMedicationsService>;
  let clinicLabReportService: jest.Mocked<ClinicLabReportService>;

  const mockClinicPlansRepository = {
    find: jest.fn()
  };

  const mockProducts = [{ id: 'p1', name: 'Product 1' }];
  const mockServices = [{ id: 's1', name: 'Service 1' }];
  const mockVaccinations = [{ id: 'v1', name: 'Vaccination 1' }];
  const mockMedications = [{ id: 'm1', name: 'Medication 1' }];
  const mockLabReports = [{ id: 'l1', name: 'Lab Report 1' }];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClinicPlansService,
        {
          provide: getRepositoryToken(ClinicPlan),
          useValue: mockClinicPlansRepository
        },
        {
          provide: WinstonLogger,
          useValue: {
            log: jest.fn(),
            error: jest.fn()
          }
        },
        {
          provide: ClinicMedicationsService,
          useValue: { getMedications: jest.fn().mockResolvedValue(mockMedications) }
        },
        {
          provide: ClinicProductsService,
          useValue: { getProducts: jest.fn().mockResolvedValue(mockProducts) }
        },
        {
          provide: ClinicServicesService,
          useValue: { getServices: jest.fn().mockResolvedValue(mockServices) }
        },
        {
          provide: ClinicVaccinationsService,
          useValue: { getVaccinations: jest.fn().mockResolvedValue(mockVaccinations) }
        },
        {
          provide: ClinicLabReportService,
          useValue: { getLabReports: jest.fn().mockResolvedValue(mockLabReports) }
        }
      ]
    }).compile();

    service = module.get<ClinicPlansService>(ClinicPlansService);
    repository = module.get<Repository<ClinicPlan>>(getRepositoryToken(ClinicPlan));
    logger = module.get(WinstonLogger);
    clinicProductsService = module.get(ClinicProductsService);
    clinicVaccinationsService = module.get(ClinicVaccinationsService);
    clinicServicesService = module.get(ClinicServicesService);
    clinicMedicationsService = module.get(ClinicMedicationsService);
    clinicLabReportService = module.get(ClinicLabReportService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of all plans without a search keyword', async () => {
      const result = await service.findAll('clinic-id');

      expect(result).toHaveLength(5); // One item from each service
      expect(result).toEqual(expect.arrayContaining([
        expect.objectContaining({ type: EnumPlanType.Product, name: 'Product 1' }),
        expect.objectContaining({ type: EnumPlanType.Service, name: 'Service 1' }),
        expect.objectContaining({ type: EnumPlanType.Vaccination, name: 'Vaccination 1' }),
        expect.objectContaining({ type: EnumPlanType.Medication, name: 'Medication 1' }),
        expect.objectContaining({ type: EnumPlanType.Labreport, name: 'Lab Report 1' }),
      ]));
    });

    it('should return a filtered array of plans when excludeTypes is provided', async () => {
      const result = await service.findAll('clinic-id', undefined, [EnumPlanType.Product, EnumPlanType.Service]);

      expect(result).toHaveLength(3); // Excluding Product and Service
      expect(result).toEqual(expect.arrayContaining([
        expect.objectContaining({ type: EnumPlanType.Vaccination, name: 'Vaccination 1' }),
        expect.objectContaining({ type: EnumPlanType.Medication, name: 'Medication 1' }),
        expect.objectContaining({ type: EnumPlanType.Labreport, name: 'Lab Report 1' }),
      ]));
    });
  });
});