import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicProductEntity } from './entities/clinic-product.entity';
import { ClinicProductsController } from './clinic-products.controller';
import { ClinicProductsService } from './clinic-products.service';
import { RoleModule } from '../roles/role.module';

@Module({
  imports: [TypeOrmModule.forFeature([ClinicProductEntity]),RoleModule],
  controllers: [ClinicProductsController],
  providers: [ClinicProductsService],
  exports:[ClinicProductsService]
})
export class  ClinicProductsModule {}
