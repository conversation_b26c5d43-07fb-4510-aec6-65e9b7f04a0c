import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, Min, Max, IsInt, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { CreditTransactionType } from '../enums/enum-credit-transaction-type';

export class OwnerCreditTransactionsFiltersDto {
	@ApiProperty({
		description: 'Start date for filtering transactions',
		example: '2023-01-01',
		required: false
	})
	@IsOptional()
	@IsString()
	startDate?: string;

	@ApiProperty({
		description: 'End date for filtering transactions',
		example: '2023-12-31',
		required: false
	})
	@IsOptional()
	@IsString()
	endDate?: string;

	@ApiProperty({
		description: 'Transaction type filter (ADD or USE)',
		enum: CreditTransactionType,
		required: false
	})
	@IsOptional()
	@IsEnum(CreditTransactionType)
	transactionType?: CreditTransactionType;

	@ApiProperty({
		description:
			'Derived transaction type filter (CREDITS_RETURNED, CREDITS_USED, EXCESS_PAYMENT, CREDITS_ADDED). Comma-separated for multiple values.',
		example: 'CREDITS_ADDED,CREDITS_USED',
		required: false
	})
	@IsOptional()
	@IsString()
	derivedTransactionTypes?: string;

	@ApiProperty({
		description: 'User ID who created the transaction',
		example: 'uuid',
		required: false
	})
	@IsOptional()
	@IsString()
	userId?: string;

	@ApiProperty({
		description:
			'Search term for filtering by description or related references',
		example: 'invoice payment',
		required: false
	})
	@IsOptional()
	@IsString()
	searchTerm?: string;

	@ApiProperty({
		description: 'Page number for pagination',
		example: 1,
		default: 1,
		required: false
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	@Type(() => Number)
	page?: number;

	@ApiProperty({
		description: 'Number of items per page',
		example: 20,
		default: 20,
		required: false
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	@Max(100)
	@Type(() => Number)
	limit?: number;
}
