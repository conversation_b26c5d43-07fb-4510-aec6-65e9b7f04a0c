import { IsDateString, IsOptional, IsUUID, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for requesting available dates for a doctor
 */
export class AvailableDatesRequestDto {
	@IsDateString()
	@IsOptional()
	@ApiProperty({
		description: 'Start date for date range (default: current date)',
		required: false,
		example: '2023-06-01'
	})
	startDate?: string;

	@IsDateString()
	@IsOptional()
	@ApiProperty({
		description:
			'End date for date range (default: current date + 30 days)',
		required: false,
		example: '2023-06-30'
	})
	endDate?: string;

	@IsUUID()
	@IsOptional()
	@ApiProperty({
		description: 'Optional clinic ID to filter availability by clinic',
		required: false,
		example: '123e4567-e89b-12d3-a456-************'
	})
	clinicId?: string;
}

/**
 * DTO for response with available dates for a doctor
 */
export class AvailableDatesResponseDto {
	@ApiProperty({
		description: 'List of dates available for booking',
		example: ['2023-12-25', '2023-12-26']
	})
	availableDates!: string[];
}

/**
 * DTO for requesting available time slots for a doctor on a specific date
 */
export class TimeSlotRequestDto {
	@IsUUID()
	@IsOptional()
	@ApiProperty({
		description: 'Optional clinic ID to filter availability by clinic',
		required: false,
		example: '123e4567-e89b-12d3-a456-************'
	})
	clinicId?: string;
}

/**
 * DTO representing a time slot
 */
export class TimeSlotDto {
	@ApiProperty({
		description: 'Start time of the slot (HH:mm)',
		example: '09:00'
	})
	startTime!: string;

	@ApiProperty({
		description: 'End time of the slot (HH:mm)',
		example: '09:30'
	})
	endTime!: string;

	@ApiProperty({
		description: 'Indicates if the slot is currently available for booking',
		example: true
	})
	isAvailable!: boolean;

	@ApiProperty({
		description: 'ID of the doctor associated with the slot',
		example: 'uuid-for-doctor-1',
		required: false // Might not be present if requesting for a specific doctor
	})
	doctorId?: string;

	@ApiProperty({
		description: 'Name of the doctor associated with the slot',
		example: 'Dr. Jane Doe',
		required: false // Might not be present if requesting for a specific doctor
	})
	doctorName?: string;
}

/**
 * DTO for response with available time slots for a doctor on a specific date
 */
export class NextAvailableSlotInfoDto {
	@ApiProperty({
		description: 'Date of the next available slot (YYYY-MM-DD)',
		example: '2024-01-10'
	})
	date!: string;

	@ApiProperty({
		description: 'Start time of the next available slot (HH:mm)',
		example: '14:00'
	})
	startTime!: string;

	@ApiProperty({
		description: 'End time of the next available slot (HH:mm)',
		example: '14:30'
	})
	endTime!: string;
}

export class AvailableTimeSlotsResponseDto {
	@ApiProperty({
		description: 'The date for which time slots are listed (YYYY-MM-DD)',
		example: '2023-12-25'
	})
	date!: string;

	@ApiProperty({
		description: 'List of time slots for the specified date',
		type: [TimeSlotDto]
	})
	timeSlots!: TimeSlotDto[];

	@ApiProperty({
		description:
			'Information about the next available slot for doctors who were unavailable on the requested date. Keyed by doctor ID.',
		type: 'object',
		additionalProperties: {
			$ref: '#/components/schemas/NextAvailableSlotInfoDto'
		},
		required: false, // Only present if applicable
		example: {
			'uuid-doctor-unavailable': {
				date: '2024-01-10',
				startTime: '14:00',
				endTime: '14:30'
			}
		}
	})
	nextAvailableSlots?: Record<string, NextAvailableSlotInfoDto | null>;
}
