import { ApiProperty } from '@nestjs/swagger';

/**
 * Simple DTO for clinic information including contact details
 */
export class ClinicSummaryDto {
	@ApiProperty({ description: 'The unique identifier of the clinic' })
	id!: string;

	@ApiProperty({ description: 'The name of the clinic' })
	name!: string;

	@ApiProperty({
		description: 'Phone numbers of the clinic',
		type: 'array',
		items: {
			type: 'object',
			properties: {
				country_code: { type: 'string' },
				number: { type: 'string' }
			}
		}
	})
	phoneNumbers!: Array<{ country_code: string; number: string }>;
}

/**
 * DTO for Brand response with additional client booking settings information
 * Contains only essential brand information plus the client booking flag
 */
export class BrandWithSettingsDto {
	@ApiProperty({ description: 'The unique identifier of the brand' })
	id!: string;

	@ApiProperty({ description: 'The name of the brand' })
	name!: string;

	@ApiProperty({ description: 'URL-friendly slug for the brand' })
	slug!: string;

	@ApiProperty({ description: 'When the brand was created' })
	createdAt!: Date;

	@ApiProperty({ description: 'When the brand was last updated' })
	updatedAt!: Date;

	@ApiProperty({
		description: 'User ID who created the brand',
		nullable: true
	})
	createdBy!: string | null;

	@ApiProperty({
		description: 'User ID who last updated the brand',
		nullable: true
	})
	updatedBy!: string | null;

	@ApiProperty({
		description:
			'Indicates if at least one clinic in the brand has client booking enabled'
	})
	hasClientBookingEnabled!: boolean;

	@ApiProperty({
		description:
			'Summary information about clinics associated with this brand',
		type: [ClinicSummaryDto],
		nullable: true
	})
	clinics?: ClinicSummaryDto[];
}
