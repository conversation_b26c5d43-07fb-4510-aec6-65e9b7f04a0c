import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateAppointmentAssessmentDto {
	@ApiProperty({
		description: 'The name of the lab report.',
		example: 'Blood Test',
		required: true
	})
	@IsNotEmpty({ message: 'The lab report should have a name.' })
	name!: string;

	@ApiProperty({
		description:
			'If the assessment is getting added by the user. The default valus is true',
		example: true,
		required: false
	})
	isAddedByUser: boolean = true;
}
