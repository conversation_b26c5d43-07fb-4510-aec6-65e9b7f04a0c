import { Repository } from 'typeorm';
import { ClinicServiceEntity } from './entities/clinic-service.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateServiceDto } from './dto/create-services.dto';
import { UpdateServiceDto } from './dto/update-services.dto';
export declare class ClinicServicesService {
    private readonly servicesRepository;
    private readonly logger;
    constructor(servicesRepository: Repository<ClinicServiceEntity>, logger: WinstonLogger);
    private generateUniqueId;
    getServices(clinicId: string, searchKeyword?: string): Promise<ClinicServiceEntity[]>;
    bulkInsert(clinicServices: CreateServiceDto[]): Promise<string>;
    create(createServiceDto: CreateServiceDto): Promise<ClinicServiceEntity>;
    update(id: string, updateServiceDto: UpdateServiceDto): Promise<ClinicServiceEntity>;
    findOne(id: string): Promise<ClinicServiceEntity>;
    remove(id: string): Promise<void>;
    findOneEntry(criteria: {
        serviceName: string;
        clinicId: string;
    }): Promise<ClinicServiceEntity | null>;
    deleteItem(itemId: string): Promise<import("typeorm").DeleteResult>;
}
