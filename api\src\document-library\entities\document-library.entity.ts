import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn
} from 'typeorm';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';

@Entity('document_library') // Table name
export class DocumentLibrary {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ name: 'clinic_id', type: 'uuid', nullable: true })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@ManyToOne(() => ClinicEntity, clinic => clinic.id, { onDelete: 'CASCADE' })
	@JoinColumn({ name: 'clinic_id' })
	clinic?: ClinicEntity;

	@Column({ name: 'document_name', type: 'varchar', nullable: false })
	documentName!: string;

	@Column({ name: 'signature_required', type: 'boolean', nullable: false })
	signatureRequired!: boolean;

	@Column({ name: 'category', type: 'varchar', nullable: true })
	category!: string;

	@Column({
		name: 'document_type',
		type: 'enum',
		enum: ['upload', 'create'],
		nullable: false
	})
	documentType!: 'upload' | 'create';

	@Column({ name: 'file_key', type: 'varchar', nullable: true })
	fileKey!: string;

	@Column({ name: 'document_body', type: 'json', nullable: true })
	documentBody!: { title: string; bodyText: string };

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@UpdateDateColumn({ name: 'deleted_at', nullable: true })
	deletedAt!: Date;

	@Column({ nullable: true, name: 'created_by' })
	createdBy!: string;

	@Column({ nullable: true, name: 'updated_by' })
	updatedBy!: string;
}
