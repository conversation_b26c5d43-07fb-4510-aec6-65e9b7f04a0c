import { Test, TestingModule } from '@nestjs/testing';
import { DeleteR<PERSON>ult, ILike, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ClinicProductsService } from './clinic-products.service';
import { ClinicProductEntity } from './entities/clinic-product.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { NotFoundException } from '@nestjs/common';
import { CreateProductDto } from './dto/create-products.dto';
import { UpdateProductDto } from './dto/update-products.dto';

describe('ClinicProductsService', () => {
	let service: ClinicProductsService;
	let repository: jest.Mocked<Repository<ClinicProductEntity>>;
	let logger: jest.Mocked<WinstonLogger>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ClinicProductsService,
				{
					provide: getRepositoryToken(ClinicProductEntity),
					useValue: {
						find: jest.fn(),
						save: jest.fn(),
						findOne: jest.fn(),
						findOneBy: jest.fn(),
						delete: jest.fn(),
						create: jest.fn(),
						count: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		service = module.get<ClinicProductsService>(ClinicProductsService);
		repository = module.get(getRepositoryToken(ClinicProductEntity));
		logger = module.get(WinstonLogger);
	});

	it('should be defined', () => {
		expect(service).toBeDefined();
	});

	describe('generateUniqueId', () => {
		it('should generate a unique ID with correct format', async () => {
			repository.count.mockResolvedValue(5);
			const uniqueId = await (service as any).generateUniqueId(
				'P_',
				'clinic_uuid'
			);
			expect(uniqueId).toBe('P_000006');
			expect(repository.count).toHaveBeenCalledWith({
				where: { clinicId: 'clinic_uuid' }
			});
		});

		it('should handle first product case', async () => {
			repository.count.mockResolvedValue(0);
			const uniqueId = await (service as any).generateUniqueId(
				'P_',
				'clinic_uuid'
			);
			expect(uniqueId).toBe('P_000001');
		});
	});

	describe('create', () => {
		it('should create and save a new product', async () => {
			const createDto: CreateProductDto = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				productName: 'Product A',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 100,
				minimumQuantity: 10
			};

			repository.count.mockResolvedValue(0);
			const createdEntity = {
				...createDto,
				id: 'new_uuid',
				uniqueId: 'P_000001'
			};
			repository.create.mockReturnValue(createdEntity);
			repository.save.mockResolvedValue(createdEntity);

			const result = await service.create(createDto);

			expect(repository.create).toHaveBeenCalledWith({
				...createDto,
				uniqueId: 'P_000001'
			});
			expect(repository.save).toHaveBeenCalledWith(createdEntity);
			expect(result).toEqual(createdEntity);
		});

		it('should log and throw an error if creation fails', async () => {
			const createDto: CreateProductDto = {
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				productName: 'Product A',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 100,
				minimumQuantity: 10
			};
			const error = new Error('Creation failed');
			repository.count.mockResolvedValue(0);
			repository.save.mockRejectedValue(error);

			await expect(service.create(createDto)).rejects.toThrow(error);
			expect(logger.error).toHaveBeenCalledWith(
				'Error creating product',
				{
					error
				}
			);
		});
	});

	describe('update', () => {
		it('should update and save an existing product', async () => {
			const updateDto: UpdateProductDto = {
				productName: 'Updated Product',
				currentStock: 150
			};
			const existingEntity = {
				id: 'existing_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'P_000001',
				productName: 'Product A',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 100,
				minimumQuantity: 10
			};
			const updatedEntity = { ...existingEntity, ...updateDto };

			repository.findOneBy.mockResolvedValue(existingEntity);
			repository.save.mockResolvedValue(updatedEntity);

			const result = await service.update('existing_uuid', updateDto);

			expect(repository.findOneBy).toHaveBeenCalledWith({
				id: 'existing_uuid'
			});
			expect(repository.save).toHaveBeenCalledWith(updatedEntity);
			expect(result).toEqual(updatedEntity);
		});

		it('should throw NotFoundException if product not found', async () => {
			repository.findOneBy.mockResolvedValue(null);

			await expect(
				service.update('nonexistent_uuid', {})
			).rejects.toThrow(NotFoundException);

			expect(logger.error).toHaveBeenCalledWith(
				'Error updating product',
				{
					error: expect.any(NotFoundException)
				}
			);
		});

		it('should handle save error during update', async () => {
			const existingEntity = {
				id: 'existing_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'P_000001',
				productName: 'Product A',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 100,
				minimumQuantity: 10
			};
			const error = new Error('Save failed');

			repository.findOneBy.mockResolvedValue(existingEntity);
			repository.save.mockRejectedValue(error);

			await expect(
				service.update('existing_uuid', { productName: 'Updated' })
			).rejects.toThrow(error);
			expect(logger.error).toHaveBeenCalledWith(
				'Error updating product',
				{
					error
				}
			);
		});
	});

	describe('findOne', () => {
		it('should return a product if found', async () => {
			const product = {
				id: 'product_uuid',
				clinicId: 'clinic_uuid',
				brandId: 'brand_uuid',
				uniqueId: 'P_000001',
				productName: 'Product A',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 100,
				minimumQuantity: 10
			};
			repository.findOneBy.mockResolvedValue(product);

			const result = await service.findOne('product_uuid');

			expect(repository.findOneBy).toHaveBeenCalledWith({
				id: 'product_uuid'
			});
			expect(result).toEqual(product);
		});

		it('should throw NotFoundException if product not found', async () => {
			repository.findOneBy.mockResolvedValue(null);

			await expect(service.findOne('nonexistent_uuid')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('remove', () => {
		it('should delete the product if it exists', async () => {
			const deleteResult: DeleteResult = {
				raw: {},
				affected: 1
			};
			repository.delete.mockResolvedValue(deleteResult);

			await service.remove('product_uuid');

			expect(repository.delete).toHaveBeenCalledWith('product_uuid');
		});

		it('should throw NotFoundException if product does not exist', async () => {
			const deleteResult: DeleteResult = {
				raw: {},
				affected: 0
			};
			repository.delete.mockResolvedValue(deleteResult);

			await expect(service.remove('nonexistent_uuid')).rejects.toThrow(
				NotFoundException
			);
		});
	});

	describe('findOneEntry', () => {
		it('should return a product matching the criteria', async () => {
			const criteria = {
				productName: 'Product A',
				clinicId: 'clinic_uuid'
			};
			const product = {
				id: 'product_uuid',
				...criteria,
				brandId: 'brand_uuid',
				uniqueId: 'P_000001',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 100,
				minimumQuantity: 10
			};
			repository.findOne.mockResolvedValue(product);

			const result = await service.findOneEntry(criteria);

			expect(repository.findOne).toHaveBeenCalledWith({
				where: criteria
			});
			expect(result).toEqual(product);
		});

		it('should return null if no product matches the criteria', async () => {
			const criteria = {
				productName: 'Nonexistent Product',
				clinicId: 'clinic_uuid'
			};
			repository.findOne.mockResolvedValue(null);

			const result = await service.findOneEntry(criteria);

			expect(result).toBeNull();
			expect(repository.findOne).toHaveBeenCalledWith({
				where: criteria
			});
		});
	});

	describe('deleteItem', () => {
		it('should delete the item and return delete result', async () => {
			const deleteResult: DeleteResult = {
				raw: {},
				affected: 1
			};
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('item_uuid');

			expect(result).toEqual(deleteResult);
			expect(repository.delete).toHaveBeenCalledWith('item_uuid');
		});

		it('should return delete result even if item does not exist', async () => {
			const deleteResult: DeleteResult = {
				raw: {},
				affected: 0
			};
			repository.delete.mockResolvedValue(deleteResult);

			const result = await service.deleteItem('nonexistent_uuid');

			expect(result).toEqual(deleteResult);
			expect(repository.delete).toHaveBeenCalledWith('nonexistent_uuid');
		});
	});

	describe('getProducts', () => {
		const productsDataList: ClinicProductEntity[] = [
			{
				id: 'product_uuid_1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				productName: 'product_name_A',
				currentStock: 12,
				minimumQuantity: 2,
				chargeablePrice: 0,
				tax: 10
			},
			{
				id: 'product_uuid_2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_2',
				productName: 'product_name_B',
				currentStock: 12,
				minimumQuantity: 2,
				chargeablePrice: 0,
				tax: 5
			}
		];

		it('should return all products for a clinic without search keyword', async () => {
			repository.find.mockResolvedValue(productsDataList);

			const result = await service.getProducts('clinic_uuid_1');

			expect(result).toEqual(productsDataList);
			expect(repository.find).toHaveBeenCalledWith({
				where: { clinicId: 'clinic_uuid_1' }
			});
		});

		it('should return filtered products with search keyword and clinicId', async () => {
			const searchKeyword = 'product_name_A';
			const filteredData = [productsDataList[0]];
			repository.find.mockResolvedValue(filteredData);

			const result = await service.getProducts(
				'clinic_uuid_1',
				searchKeyword
			);

			expect(result).toEqual(filteredData);
			expect(repository.find).toHaveBeenCalledWith({
				where: {
					productName: ILike(`%${searchKeyword}%`),
					clinicId: 'clinic_uuid_1'
				}
			});
		});

		it('should return filtered products with only search keyword', async () => {
			const searchKeyword = 'product_name_A';
			const filteredData = [productsDataList[0]];
			repository.find.mockResolvedValue(filteredData);

			const result = await service.getProducts('', searchKeyword);

			expect(result).toEqual(filteredData);
			expect(repository.find).toHaveBeenCalledWith({
				where: { productName: ILike(`%${searchKeyword}%`) }
			});
		});
	});

	describe('bulkInsert', () => {
		const productsToInsert: CreateProductDto[] = [
			{
				productName: 'New Product 1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				chargeablePrice: 100,
				tax: 10,
				currentStock: 50,
				minimumQuantity: 5
			},
			{
				productName: 'New Product 2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				chargeablePrice: 150,
				tax: 15,
				currentStock: 75,
				minimumQuantity: 10
			}
		];

		it('should insert multiple products successfully', async () => {
			repository.count.mockResolvedValue(0);
			repository.findOne.mockResolvedValue(null);
			repository.save.mockImplementation((entity: any) =>
				Promise.resolve(entity)
			);

			const result = await service.bulkInsert(productsToInsert);

			expect(result).toBe(
				'Bulk insert of 2 products completed successfully'
			);
			expect(repository.save).toHaveBeenCalledTimes(2);
			expect(repository.findOne).toHaveBeenCalledTimes(2);
			expect(logger.log).toHaveBeenCalledWith(
				'Bulk insert completed. Inserted 2 products.'
			);
		});

		it('should update existing products and create new ones', async () => {
			const existingProduct = {
				id: 'existing_uuid',
				...productsToInsert[0],
				uniqueId: 'P_000001'
			};

			repository.count.mockResolvedValue(1);
			repository.findOne.mockImplementation(async (query: any) =>
				query.where.productName === 'New Product 1'
					? existingProduct
					: null
			);
			repository.save.mockImplementation(async (entity: any) =>
				Promise.resolve(entity)
			);

			const result = await service.bulkInsert(productsToInsert);

			expect(result).toBe(
				'Bulk insert of 2 products completed successfully'
			);
			expect(repository.save).toHaveBeenCalledTimes(2);
			expect(repository.findOne).toHaveBeenCalledTimes(2);
		});

		it('should throw Error when bulk insert fails', async () => {
			const mockError = new Error('Database error');
			repository.findOne.mockRejectedValue(mockError);

			await expect(service.bulkInsert(productsToInsert)).rejects.toThrow(
				'Failed to insert products'
			);

			expect(logger.error).toHaveBeenCalledWith(
				'Error during bulk insert of products',
				{ error: mockError }
			);
		});
	});
});
