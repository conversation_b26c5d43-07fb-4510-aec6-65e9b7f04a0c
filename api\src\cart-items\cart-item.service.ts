import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { CreateCartItemDto } from './dto/create-cart-item.dto';
import { CartItemEntity } from './entities/cart-item.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, UpdateResult } from 'typeorm';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
import { CartsService } from '../carts/carts.service';
import { BulkInsertIntoCartItemDto } from './dto/bulkInsert-cartItem.dto';
import { Inject, forwardRef } from '@nestjs/common';

@Injectable()
export class CartItemService {
	private readonly logger = new Logger(CartItemService.name);

	constructor(
		@InjectRepository(CartItemEntity)
		private cartRepository: Repository<CartItemEntity>,
		@Inject(forwardRef(() => CartsService))
		private readonly cartsService: CartsService
	) {}

	async createCartItem(
		createCartDto: CreateCartItemDto,
		userContext?: {
			clinicId: string;
			brandId: string;
			userId: string;
		}
	) {
		this.logger.log(
			`[CART-ITEM][${createCartDto.appointmentId}][CREATE] Creating item from ${createCartDto.addedFrom || 'manual'}`,
			{
				operation: 'CREATE',
				appointmentId: createCartDto.appointmentId,
				itemType: this.determineItemType(createCartDto),
				source: createCartDto.addedFrom || 'manual',
				details: createCartDto
			}
		);

		// Extract user details for impromptu appointment if needed
		const impromptuData =
			createCartDto.impromptu && createCartDto.patientId && userContext
				? {
						impromptu: createCartDto.impromptu,
						patientId: createCartDto.patientId,
						clinicId: userContext.clinicId,
						brandId: userContext.brandId,
						userId: userContext.userId
					}
				: undefined;

		if (impromptuData) {
			this.logger.log('Using context for impromptu appointment', {
				clinicId: impromptuData.clinicId,
				brandId: impromptuData.brandId,
				userId: impromptuData.userId,
				patientId: impromptuData.patientId,
				context: userContext // Log the full context for debugging
			});
		}

		const cart = await this.cartsService.createCart(
			createCartDto.appointmentId,
			createCartDto.cartId,
			impromptuData
		);

		const updatedCreateCartDto = { ...createCartDto, cartId: cart.id };

		// If an impromptu appointment was created, update the appointmentId
		if (!createCartDto.appointmentId && cart.appointmentId) {
			updatedCreateCartDto.appointmentId = cart.appointmentId;
		}

		const result = await this.cartRepository.save(updatedCreateCartDto);
		const response = {
			...result,
			lineItemId: createCartDto.lineItemId // Include lineItemId in response if it exists
		};

		this.logger.log(
			`[CART-ITEM][${updatedCreateCartDto.appointmentId}][CREATE_SUCCESS] Item created successfully`,
			{
				operation: 'CREATE_SUCCESS',
				appointmentId: updatedCreateCartDto.appointmentId,
				cartId: cart.id,
				itemId: result.id,
				lineItemId: createCartDto.lineItemId, // Log lineItemId if it exists
				source: createCartDto.addedFrom || 'manual'
			}
		);

		return response;
	}

	async deleteCartItem(cartId: string, source: string = 'manual') {
		const cart = await this.cartRepository.findOne({
			where: { id: cartId }
		});

		if (!cart) {
			this.logger.error(
				`[CART-ITEM][${cartId}][DELETE_ERROR] Cart item not found`,
				{
					operation: 'DELETE_ERROR',
					cartId,
					error: 'NOT_FOUND',
					source
				}
			);
			throw new NotFoundException(
				`This cart with ${cartId} doesn't exist`
			);
		}

		// Log before deletion
		this.logger.log(
			`[CART-ITEM][${cart.appointmentId}][DELETE] Deleting item from ${source}`,
			{
				operation: 'DELETE',
				appointmentId: cart.appointmentId,
				cartId: cart.id,
				itemType: cart.type,
				source,
				itemDetails: this.getItemDetails(cart)
			}
		);

		const result = await this.cartRepository.delete({
			id: cart.id
		});

		const affectedRows = result.affected ?? 0;

		// Log after deletion
		if (result.affected === 0) {
			this.logger.warn(
				`[CART-ITEM][${cart.appointmentId}][DELETE_FAILED] No records found to delete`,
				{
					operation: 'DELETE_FAILED',
					appointmentId: cart.appointmentId,
					cartId: cart.id,
					source
				}
			);
			return { status: false, message: 'No records found to delete.' };
		} else if (affectedRows > 1) {
			this.logger.error(
				`[CART-ITEM][${cart.appointmentId}][DELETE_ERROR] Multiple records deleted`,
				{
					operation: 'DELETE_ERROR',
					appointmentId: cart.appointmentId,
					cartId: cart.id,
					affectedRows,
					source
				}
			);
			return {
				status: false,
				message: 'Multiple records deleted. Check your query.'
			};
		} else {
			this.logger.log(
				`[CART-ITEM][${cart.appointmentId}][DELETE_SUCCESS] Item deleted successfully`,
				{
					operation: 'DELETE_SUCCESS',
					appointmentId: cart.appointmentId,
					cartId: cart.id,
					source
				}
			);
			return { status: true };
		}
	}

	async getCartItemDetailsByAppointmentId(appointmentId: string) {
		const cartItems = await this.cartRepository.find({
			where: { appointmentId },
			order: { createdAt: 'DESC' }
		});

		const transformedCartItems = cartItems.map((item: any) => {
			let itemDetails = null;

			// Check which item type exists and assign it to itemDetails
			if (item.product) {
				itemDetails = item.product;
			} else if (item.service) {
				itemDetails = item.service;
			} else if (item.vaccination) {
				itemDetails = item.vaccination;
			} else if (item.prescription) {
				itemDetails = item.prescription;
			} else if (item.labReport) {
				itemDetails = item.labReport;
			}

			// Return the modified object with a consistent key for the details
			return {
				...item,
				itemDetails,
				product: undefined,
				service: undefined,
				vaccination: undefined,
				prescription: undefined,
				labReport: undefined
			};
		});

		return transformedCartItems;
	}

	async getCartItemDetailsByCartId(cartId: string) {
		const cartItems = await this.cartRepository.find({
			where: { cartId },
			order: { createdAt: 'DESC' }
		});

		const transformedCartItems = cartItems.map((item: any) => {
			let itemDetails = null;

			// Check which item type exists and assign it to itemDetails
			if (item.product) {
				itemDetails = item.product;
			} else if (item.service) {
				itemDetails = item.service;
			} else if (item.vaccination) {
				itemDetails = item.vaccination;
			} else if (item.prescription) {
				itemDetails = item.prescription;
			} else if (item.labReport) {
				itemDetails = item.labReport;
			}

			// Return the modified object with a consistent key for the details
			return {
				...item,
				itemDetails,
				product: undefined,
				service: undefined,
				vaccination: undefined,
				prescription: undefined,
				labReport: undefined
			};
		});

		return transformedCartItems;
	}

	async updateCartItemDetails(
		cartId: string,
		updateCartDto: UpdateCartItemDto,
		source: string = 'manual'
	) {
		const cartDetails = await this.cartRepository.findOne({
			where: { id: cartId }
		});

		if (!cartDetails) {
			this.logger.error(
				`[CART-ITEM][${cartId}][UPDATE_ERROR] Cart item not found`,
				{
					operation: 'UPDATE_ERROR',
					cartId,
					error: 'NOT_FOUND',
					source
				}
			);
			throw new NotFoundException(
				`This cart with ${cartId} doesn't exist`
			);
		}

		// Log before update
		this.logger.log(
			`[CART-ITEM][${cartDetails.appointmentId}][UPDATE] Updating item from ${source}`,
			{
				operation: 'UPDATE',
				appointmentId: cartDetails.appointmentId,
				cartId: cartDetails.id,
				itemType: cartDetails.type,
				source,
				currentState: this.getItemDetails(cartDetails),
				updates: updateCartDto
			}
		);

		Object.assign(cartDetails, updateCartDto);
		const updateResult: UpdateResult = await this.cartRepository.update(
			cartId,
			cartDetails
		);

		if (updateResult.affected === 0) {
			this.logger.warn(
				`[CART-ITEM][${cartDetails.appointmentId}][UPDATE_FAILED] Update failed`,
				{
					operation: 'UPDATE_FAILED',
					appointmentId: cartDetails.appointmentId,
					cartId: cartDetails.id,
					source
				}
			);
			return { status: false };
		}

		const updatedCart = await this.cartRepository.findOne({
			where: { id: cartId }
		});

		if (updatedCart) {
			this.logger.log(
				`[CART-ITEM][${cartDetails.appointmentId}][UPDATE_SUCCESS] Item updated successfully`,
				{
					operation: 'UPDATE_SUCCESS',
					appointmentId: cartDetails.appointmentId,
					cartId: cartDetails.id,
					source,
					newState: this.getItemDetails(updatedCart)
				}
			);
		}

		return updatedCart ? updatedCart : { status: false };
	}

	async createCartAndBulkInsert(bulkInsertDto: BulkInsertIntoCartItemDto) {
		const cart = await this.cartsService.createCart(
			bulkInsertDto.appointmentId
		);
		const cartItemInsertionArray = bulkInsertDto?.prescriptionIds.map(
			(presId: string) => {
				return {
					appointmentId: bulkInsertDto.appointmentId,
					cartId: cart.id,
					prescriptionId: presId,
					type: 'Medication',
					isAddedToCart: false,
					addedFrom: bulkInsertDto.addedFrom
				};
			}
		);

		return await this.cartRepository.save(cartItemInsertionArray);
	}

	// Helper method to determine item type
	private determineItemType(dto: CreateCartItemDto | CartItemEntity): string {
		if (dto.productId) return 'Product';
		if (dto.serviceId) return 'Service';
		if (dto.vaccinationId) return 'Vaccination';
		if (dto.prescriptionId) return 'Prescription';
		if (dto.labReportId) return 'LabReport';
		return 'Unknown';
	}

	// Helper method to get relevant item details
	private getItemDetails(cart: CartItemEntity) {
		return {
			id: cart.id,
			type: cart.type,
			itemId:
				cart.productId ||
				cart.serviceId ||
				cart.vaccinationId ||
				cart.prescriptionId ||
				cart.labReportId,
			quantity: cart.quantity,
			price: cart.price,
			isAddedToCart: cart.isAddedToCart,
			addedFrom: cart.addedFrom,
			comment: cart.comment
		};
	}
}
