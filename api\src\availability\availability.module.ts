import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoggerModule } from '../utils/logger/logger-module';
import { SqsModule } from '../utils/aws/sqs/sqs.module';

import { ClinicAvailabilitySlot } from './entities/clinic-availability-slot.entity';
import { AvailabilityService } from './availability.service';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { AvailabilityExceptionEntity } from '../users/entities/availability-exception.entity';
import { AvailabilityController } from './availability.controller';

/**
 * Module for handling clinic user availability
 * Provides optimized mechanisms for availability checks, slot generation, and maintenance
 */
@Module({
	imports: [
		TypeOrmModule.forFeature([
			ClinicAvailabilitySlot,
			ClinicUser,
			AppointmentEntity,
			AppointmentDoctorsEntity,
			AvailabilityExceptionEntity
		]),
		LoggerModule,
		forwardRef(() => SqsModule.forRoot(true))
	],
	providers: [AvailabilityService],
	controllers: [AvailabilityController],
	exports: [AvailabilityService]
})
export class AvailabilityModule {}
