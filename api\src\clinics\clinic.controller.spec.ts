import { Test, TestingModule } from '@nestjs/testing';
import { ClinicController } from './clinic.controller';
import { ClinicService } from './clinic.service';
import { CreateClinicDto, UpdateBasicClinicDto } from './dto/create-clinic.dto';
import { <PERSON><PERSON>ogger } from '../utils/logger/winston-logger.service';
import { ClinicEntity } from './entities/clinic.entity';
import { ClinicRoomEntity } from './entities/clinic-room.entity';
import { HttpException, HttpStatus } from '@nestjs/common';
import { UpdateClinicDto } from './dto/update-clinic.dto';
import { Brand } from '../brands/entities/brand.entity';
import { User } from '../users/entities/user.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { Request } from 'express';
import { RoleService } from '../roles/role.service';
import {
	CreateClinicRoomDto,
	UpdateClinicRoomDto
} from './dto/create-clinic-room.dto';

describe('ClinicController', () => {
	let controller: ClinicController;
	let clinicService: jest.Mocked<ClinicService>;

	const createdDate = new Date();
	const mockClinics: ClinicEntity[] = [
		{
			id: 'uuid_clinic_1',
			mobile: '**********',
			name: 'Test 1 Clinic',
			createdAt: createdDate,
			updatedAt: createdDate,
			addressLine1: '',
			addressLine2: '',
			city: '',
			addressPincode: '',
			state: '',
			country: '',
			email: '',
			website: '',
			logoUrl: '',
			gstNumber: '',
			drugLicenseNumber: '',
			phoneNumbers: [],
			brandId: '',
			brand: new Brand(),
			isOnboarded: false,
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			clinicUsers: [],
			adminFirstName: '',
			adminLastName: '',
			adminEmail: '',
			adminMobile: '',
			working_hours: {},
			isActive: true,
			clinicLogo: 'filekey',
			documentLibrary: []
		},
		{
			id: 'uuid_clinic_2',
			mobile: '**********',
			name: 'Test 2 Clinic',
			createdAt: createdDate,
			updatedAt: createdDate,
			addressLine1: '',
			addressLine2: '',
			city: '',
			addressPincode: '',
			state: '',
			country: '',
			email: '',
			website: '',
			logoUrl: '',
			gstNumber: '',
			drugLicenseNumber: '',
			phoneNumbers: [],
			brandId: '',
			brand: new Brand(),
			isOnboarded: false,
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			clinicUsers: [],
			adminFirstName: '',
			adminLastName: '',
			adminEmail: '',
			adminMobile: '',
			working_hours: {},
			isActive: true,
			clinicLogo: 'filekey',
			documentLibrary: []
		},
		{
			id: 'uuid_clinic_3',
			mobile: '**********',
			name: 'Test 3 Clinic',
			createdAt: createdDate,
			updatedAt: createdDate,
			addressLine1: '',
			addressLine2: '',
			city: '',
			addressPincode: '',
			state: '',
			country: '',
			email: '',
			website: '',
			logoUrl: '',
			gstNumber: '',
			drugLicenseNumber: '',
			phoneNumbers: [],
			brandId: '',
			brand: new Brand(),
			isOnboarded: false,
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			clinicUsers: [],
			adminFirstName: '',
			adminLastName: '',
			adminEmail: '',
			adminMobile: '',
			working_hours: {},
			isActive: true,
			clinicLogo: 'filekey',
			documentLibrary: []
		}
	];

	const createMockOutput = (clinics: ClinicEntity[], total: number) => ({
		clinics,
		total
	});

	const mockRequest = {
		user: { userId: 'mock-user-id' }
	} as unknown as Request;

	const mockResponse = {
		set: jest.fn(),
		end: jest.fn(),
		status: jest.fn().mockReturnThis(),
		json: jest.fn()
	} as unknown as Response;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicController],
			providers: [
				{
					provide: ClinicService,
					useValue: {
						createClinic: jest.fn(),
						getAllClinics: jest.fn(),
						getClinicById: jest.fn(),
						updateClinic: jest.fn(),
						getClinicRooms: jest.fn(),
						updateBasicClinicInfo: jest.fn(),
						createClinicRoom: jest.fn(),
						updateClinicRoom: jest.fn(),
						deleteRoom: jest.fn(),
						deactivateClinic: jest.fn(),
						processBulkUpload: jest.fn(),
						generateInventoryExcel: jest.fn(),
						deleteInventoryItem: jest.fn()
					}
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicController>(ClinicController);
		clinicService = module.get(ClinicService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('Create clinic - POST /clinic', () => {
		it('should create a clinic and return with id', async () => {
			const mockInputCreateClinicDto: CreateClinicDto = {
				name: 'Test Clinic',
				brandId: 'band-id',
				adminFirstName: 'sample',
				adminLastName: 'name',
				adminEmail: '<EMAIL>',
				adminMobile: '**********'
			};

			const mockOuputClinicEntity: ClinicEntity = {
				id: 'uuid_clinic',
				...mockInputCreateClinicDto,
				createdAt: new Date(),
				updatedAt: new Date(),
				addressLine1: '',
				addressLine2: '',
				city: '',
				addressPincode: '',
				state: '',
				country: '',
				email: '',
				website: '',
				mobile: '',
				logoUrl: '',
				gstNumber: '',
				drugLicenseNumber: '',
				phoneNumbers: [],
				brand: new Brand(),
				isOnboarded: false,
				createdBy: '',
				createdByUser: new User(),
				updatedBy: '',
				updatedByUser: new User(),
				clinicUsers: [],
				working_hours: {},
				isActive: true,
				clinicLogo: 'filekey',
				documentLibrary: []
			};

			clinicService.createClinic.mockResolvedValue(mockOuputClinicEntity);
			const result = await controller.createClinic(
				mockInputCreateClinicDto,
				mockRequest
			);
			expect(result).toEqual(mockOuputClinicEntity);
			expect(clinicService.createClinic).toHaveBeenCalledWith(
				mockInputCreateClinicDto,
				'mock-user-id'
			);
		});

		it('should return error if the clinic with name exists', async () => {
			const mockInputClinicDto: CreateClinicDto = {
				name: 'Test Clinic',
				brandId: 'band-id',
				adminFirstName: 'sample',
				adminLastName: 'name',
				adminEmail: '<EMAIL>',
				adminMobile: '**********'
			};

			const error = new HttpException(
				`The clinic with name ${mockInputClinicDto.name} already exists`,
				HttpStatus.BAD_REQUEST
			);

			clinicService.createClinic.mockRejectedValue(error);

			await expect(
				controller.createClinic(mockInputClinicDto, mockRequest)
			).rejects.toThrow(
				`The clinic with name ${mockInputClinicDto.name} already exists`
			);
			expect(clinicService.createClinic).toHaveBeenCalledWith(
				mockInputClinicDto,
				'mock-user-id'
			);
		});
	});

	describe('Get clinic by ID - GET /clinic/:id', () => {
		it('should return a clinic by ID', async () => {
			const clinicId = 'uuid_clinic_1';
			const mockClinic = mockClinics[0];

			clinicService.getClinicById.mockResolvedValue(mockClinic);

			const result = await controller.getClinicById(clinicId);

			expect(result).toEqual(mockClinic);
			expect(clinicService.getClinicById).toHaveBeenCalledWith(clinicId);
		});

		it('should throw an exception if the clinic is not found', async () => {
			const clinicId = 'non_existing_id';
			const error = new HttpException(
				`This clinic with ${clinicId} doesn't exist`,
				HttpStatus.NOT_FOUND
			);

			clinicService.getClinicById.mockRejectedValue(error);

			await expect(controller.getClinicById(clinicId)).rejects.toThrow(
				`This clinic with ${clinicId} doesn't exist`
			);
			expect(clinicService.getClinicById).toHaveBeenCalledWith(clinicId);
		});
	});

	describe('Get list of clinics - GET /clinics', () => {
		it('should return the list of clinics with default query params', async () => {
			const mockOutput = createMockOutput(
				[mockClinics[2], mockClinics[1], mockClinics[0]],
				3
			);
			clinicService.getAllClinics.mockResolvedValue(mockOutput);
			const result = await controller.getAllClinics();

			expect(clinicService.getAllClinics).toHaveBeenCalledWith(
				1,
				10,
				'DESC'
			);
			expect(result.clinics).toEqual(mockOutput.clinics);
			expect(result.total).toBe(mockOutput.total);
		});

		it('should handle valid orderBy value (ASC)', async () => {
			const mockOutput = createMockOutput(
				[mockClinics[0], mockClinics[1]],
				2
			);

			clinicService.getAllClinics.mockResolvedValue(mockOutput);
			const result = await controller.getAllClinics(1, 2, 'ASC');

			expect(clinicService.getAllClinics).toHaveBeenCalledWith(
				1,
				2,
				'ASC'
			);
			expect(result.clinics).toEqual(mockOutput.clinics);
			expect(result.total).toBe(mockOutput.total);
		});
	});

	describe('Update a clinic - PUT /clinic/:id', () => {
		it('should update a clinic successfully', async () => {
			const clinicId = 'uuid_clinic_1';
			const updateClinicDto: UpdateClinicDto = {
				addressLine1: 'Adress lINE',
				addressLine2: 'Sample Address Line 12',
				city: 'Sample City',
				addressPincode: '123456',
				state: 'Sample State',
				country: 'India',
				email: '<EMAIL>',
				website: 'www.sample.com',
				logoUrl: 'ww.sample.com/login',
				gstNumber: '12345XXXXX',
				drugLicenseNumber: '123XXXXXXX',
				phoneNumbers: [
					{
						country_code: '+91',
						number: '90191121133'
					}
				]
			};
			const updatedClinic: any = {
				...mockClinics[0],
				...updateClinicDto
			};

			clinicService.updateClinic.mockResolvedValue(updatedClinic);

			const result = await controller.updateClinic(
				clinicId,
				updateClinicDto,
				mockRequest
			);

			expect(result).toEqual(updatedClinic);
			expect(clinicService.updateClinic).toHaveBeenCalledWith(
				clinicId,
				updateClinicDto,
				'mock-user-id'
			);
		});

		// it('should throw an exception if clinic update fails', async () => {
		// 	const clinicId = 'uuid_clinic_1';
		// 	const updateClinicDto: UpdateBasicClinicDto = {
		// 		name: 'updates Test Clinic',
		// 		adminFirstName: 'sample',
		// 		adminLastName: 'name',
		// 		adminEmail: '<EMAIL>',
		// 		adminMobile: '**********'
		// 	};
		// 	const error = new HttpException(
		// 		'Clinic update failed',
		// 		HttpStatus.BAD_REQUEST
		// 	);

		// 	clinicService.updateClinic.mockRejectedValue(error);

		// 	await expect(
		// 		controller.updateBasicCinic(clinicId, updateClinicDto)
		// 	).rejects.toThrow('Clinic update failed');
		// 	expect(clinicService.updateClinic).toHaveBeenCalledWith(
		// 		clinicId,
		// 		updateClinicDto
		// 	);
		// });
	});

	describe('Update basic clinic details - PUT /clinic/basic/:id', () => {
		it('should update basic clinic details successfully', async () => {
			const clinicId = 'uuid_clinic_1';
			const updateBasicClinicDto: UpdateBasicClinicDto = {
				name: 'updates Test Clinic',
				adminFirstName: 'sample',
				adminLastName: 'name',
				adminEmail: '<EMAIL>',
				adminMobile: '**********'
			};
			const updatedClinic: ClinicEntity = {
				...mockClinics[0],
				...updateBasicClinicDto
			};

			clinicService.updateBasicClinicInfo.mockResolvedValue(
				updatedClinic
			);

			const result = await controller.updateBasicClinic(
				clinicId,
				updateBasicClinicDto,
				mockRequest
			);

			expect(result).toEqual(updatedClinic);
			expect(clinicService.updateBasicClinicInfo).toHaveBeenCalledWith(
				clinicId,
				updateBasicClinicDto,
				'mock-user-id'
			);
		});
	});

	describe('Get clinic rooms - GET /clinic/:id/rooms', () => {
		it('should return clinic rooms', async () => {
			const clinicId = 'uuid_clinic_1';
			const mockRooms: ClinicRoomEntity[] = [
				{
					id: 'room_1',
					name: 'Room 1',
					clinicId: clinicId,
					description: '',
					brandId: 'brand-1',
					appointment: new AppointmentEntity()
				},
				{
					id: 'room_2',
					name: 'Room 2',
					clinicId: clinicId,
					description: '',
					brandId: 'brand-1',
					appointment: new AppointmentEntity()
				}
			];

			clinicService.getClinicRooms.mockResolvedValue({
				rooms: mockRooms,
				total: mockRooms.length
			});

			const result = await controller.getClinicRooms(clinicId);

			expect(result).toEqual({
				rooms: mockRooms,
				total: mockRooms.length
			});
			expect(clinicService.getClinicRooms).toHaveBeenCalledWith(clinicId);
		});

		it('should throw an error if fetching rooms fails', async () => {
			const clinicId = 'uuid_clinic_1';
			const error = new HttpException(
				'Error fetching rooms',
				HttpStatus.BAD_REQUEST
			);

			clinicService.getClinicRooms.mockRejectedValue(error);

			await expect(controller.getClinicRooms(clinicId)).rejects.toThrow(
				'Error fetching rooms'
			);
			expect(clinicService.getClinicRooms).toHaveBeenCalledWith(clinicId);
		});
	});

	describe('createClinicRoom', () => {
		it('should create a clinic room', async () => {
			const createDto: CreateClinicRoomDto = {
				name: 'New Room',
				clinicId: 'clinic-id'
			};
			const mockRoom: ClinicRoomEntity = {
				id: 'room-id',
				...createDto
			} as ClinicRoomEntity;
			const mockReq = {
				user: { clinicId: 'clinic-id', brandId: 'brand-id' }
			};

			clinicService.createClinicRoom.mockResolvedValue(mockRoom);

			const result = await controller.createClinicRoom(
				createDto,
				mockReq
			);

			expect(result).toEqual(mockRoom);
			expect(clinicService.createClinicRoom).toHaveBeenCalledWith(
				createDto,
				mockReq.user.brandId
			);
		});

		it('should throw an exception if creation fails', async () => {
			const createDto: CreateClinicRoomDto = {
				name: 'New Room',
				clinicId: 'clinic-id'
			};
			const mockReq = {
				user: { clinicId: 'clinic-id', brandId: 'brand-id' }
			};
			clinicService.createClinicRoom.mockRejectedValue(
				new Error('Creation failed')
			);

			await expect(
				controller.createClinicRoom(createDto, mockReq)
			).rejects.toThrow(HttpException);
		});
	});

	describe('updateClinicRoom', () => {
		it('should update a clinic room', async () => {
			const updateDto: UpdateClinicRoomDto = { name: 'Updated Room' };
			const mockRoom: ClinicRoomEntity = {
				id: 'room-id',
				...updateDto
			} as ClinicRoomEntity;

			clinicService.updateClinicRoom.mockResolvedValue(mockRoom);

			const result = await controller.updateClinicRoom(
				'room-id',
				updateDto
			);

			expect(result).toEqual(mockRoom);
			expect(clinicService.updateClinicRoom).toHaveBeenCalledWith(
				'room-id',
				updateDto
			);
		});

		it('should throw an exception if update fails', async () => {
			const updateDto: UpdateClinicRoomDto = { name: 'Updated Room' };
			clinicService.updateClinicRoom.mockRejectedValue(
				new Error('Update failed')
			);

			await expect(
				controller.updateClinicRoom('room-id', updateDto)
			).rejects.toThrow(HttpException);
		});
	});

	describe('deleteRoom', () => {
		it('should delete a room', async () => {
			clinicService.deleteRoom.mockResolvedValue(undefined);

			const result = await controller.deleteRoom('room-id');

			expect(result).toEqual({ message: 'Room deleted successfully' });
			expect(clinicService.deleteRoom).toHaveBeenCalledWith('room-id');
		});

		it('should throw an exception if deletion fails', async () => {
			clinicService.deleteRoom.mockRejectedValue(
				new Error('Deletion failed')
			);

			await expect(controller.deleteRoom('room-id')).rejects.toThrow(
				HttpException
			);
		});
	});

	describe('deactivateClinic', () => {
		it('should deactivate a clinic', async () => {
			const mockDeactivatedClinic = {
				id: 'clinic-id',
				isActive: false
			} as ClinicEntity;
			clinicService.deactivateClinic.mockResolvedValue(
				mockDeactivatedClinic
			);

			const result = await controller.deactivateClinic('clinic-id');

			expect(result).toEqual(mockDeactivatedClinic);
			expect(clinicService.deactivateClinic).toHaveBeenCalledWith(
				'clinic-id'
			);
		});

		it('should throw an exception if deactivation fails', async () => {
			clinicService.deactivateClinic.mockRejectedValue(
				new Error('Deactivation failed')
			);

			await expect(
				controller.deactivateClinic('clinic-id')
			).rejects.toThrow(HttpException);
		});
	});

	//   describe('uploadFile', () => {
	// 	it('should process bulk upload', async () => {
	// 	  const mockFile = { buffer: Buffer.from('test') } as Express.Multer.File;
	// 	  const mockResult = { success: true };
	// 	  clinicService.processBulkUpload.mockResolvedValue(mockResult);

	// 	  const result = await controller.uploadFile(mockFile, 'clinic-id', 'brand-id', mockRequest);

	// 	  expect(result).toEqual(mockResult);
	// 	  expect(clinicService.processBulkUpload).toHaveBeenCalledWith(mockFile, 'clinic-id', 'brand-id');
	// 	});

	// 	it('should throw an exception if file is missing', async () => {
	// 		await expect(controller.uploadFile(null, 'clinic-id', 'brand-id', mockRequest)).rejects.toThrow(HttpException);
	// 	});

	// 	it('should throw an exception if clinicId or brandId is missing', async () => {
	// 	  const mockFile = { buffer: Buffer.from('test') } as Express.Multer.File;
	// 	  await expect(controller.uploadFile(mockFile, null, 'brand-id', mockRequest)).rejects.toThrow(HttpException);
	// 	});
	//   });

	//   describe('downloadInventory', () => {
	// 	it('should generate and send inventory excel', async () => {
	// 	  const mockBuffer = Buffer.from('test');
	// 	  clinicService.generateInventoryExcel.mockResolvedValue(mockBuffer);

	// 	  await controller.downloadInventory('clinic-id', mockResponse);

	// 	  expect(clinicService.generateInventoryExcel).toHaveBeenCalledWith('clinic-id');
	// 	  expect(mockResponse).toHaveBeenCalled();
	// 	  expect(mockResponse).toHaveBeenCalledWith(mockBuffer);
	// 	});
	//   });

	describe('deleteInventoryItem', () => {
		it('should delete an inventory item', async () => {
			const mockResult = { message: 'Item deleted successfully' };
			clinicService.deleteInventoryItem.mockResolvedValue(mockResult);

			const result = await controller.deleteInventoryItem(
				'itemType',
				'itemId'
			);

			expect(result).toEqual(mockResult);
			expect(clinicService.deleteInventoryItem).toHaveBeenCalledWith(
				'itemType',
				'itemId'
			);
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
	});
});
