import {
	Controller,
	Get,
	Post,
	Param,
	Query,
	Body,
	UseGuards,
	Req,
	HttpException,
	HttpStatus,
	UsePipes,
	ValidationPipe
} from '@nestjs/common';
import {
	ApiOkResponse,
	ApiParam,
	ApiQuery,
	ApiTags,
	<PERSON>piOperation,
	ApiResponse
} from '@nestjs/swagger';
import { ClientDashboardService } from './client-dashboard.service';
import { ClientDashboardResponseDto } from './dto/client-dashboard-response.dto';
import { ClientAppointmentsResponseDto } from './dto/client-appointments-response.dto';
import { DirectLoginDto } from './dto/direct-login.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Role } from '../roles/role.enum';
import { Roles } from '../roles/roles.decorator';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

// Expanded interface to include everything we need from our JWT token
interface RequestWithUser {
	user: {
		brandId?: string;
		sub?: string; // Owner ID
		phoneNumber?: string;
		role?: string;
		globalOwnerId?: string;
	};
}

@ApiTags('Client Dashboard')
@Controller()
export class ClientDashboardController {
	constructor(
		private readonly clientDashboardService: ClientDashboardService,
		private readonly logger: WinstonLogger
	) {}

	@Post('auth/booking/direct-login')
	@ApiOperation({ summary: 'Direct login for pet owner' })
	@ApiResponse({
		status: 200,
		description: 'Login successful. Access token provided.'
	})
	@ApiResponse({ status: 404, description: 'Brand not found.' })
	@ApiResponse({ status: 500, description: 'Failed to login.' })
	@UsePipes(ValidationPipe)
	@TrackMethod('directLogin-client-dashboard')
	async directLogin(@Body() directLoginDto: DirectLoginDto) {
		try {
			this.logger.log('Direct login for booking portal', {
				dto: directLoginDto
			});
			const response =
				await this.clientDashboardService.directLogin(directLoginDto);
			this.logger.log('Direct login successful', {
				phoneNumber: directLoginDto.phoneNumber,
				countryCode: directLoginDto.countryCode || '91',
				brandId: directLoginDto.brandId
			});
			return response;
		} catch (error) {
			this.logger.error('Error during direct login for booking portal', {
				error
			});
			if (error instanceof HttpException) {
				throw error;
			}
			throw new HttpException(
				'Failed to login',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns client dashboard information',
		type: ClientDashboardResponseDto
	})
	@ApiParam({ name: 'ownerId', description: 'The ID of the owner' })
	@Get('/client-dashboard/:ownerId')
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST, Role.OWNER)
	@TrackMethod('getClientDashboard-client-dashboard')
	async getClientDashboard(
		@Param('ownerId') ownerId: string,
		@Req() req: RequestWithUser
	): Promise<ClientDashboardResponseDto> {
		try {
			const brandId = req.user?.brandId;

			if (!brandId) {
				this.logger.error('Brand ID not found in user context', {
					user: req.user,
					ownerId
				});
				throw new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				);
			}

			this.logger.log('Getting client dashboard', { ownerId, brandId });
			return await this.clientDashboardService.getClientDashboard(
				ownerId,
				brandId
			);
		} catch (error: any) {
			this.logger.error('Error in getClientDashboard', {
				error,
				ownerId
			});
			throw new HttpException(
				error.message || 'Error retrieving client dashboard',
				error.status || HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns client appointments',
		type: ClientAppointmentsResponseDto
	})
	@ApiParam({ name: 'ownerId', description: 'The ID of the owner' })
	@ApiQuery({
		name: 'date',
		required: false,
		description: 'Filter by date (YYYY-MM-DD)'
	})
	@ApiQuery({
		name: 'status',
		required: false,
		description: 'Filter by appointment status'
	})
	@Get('/client-appointments/:ownerId')
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST, Role.OWNER)
	@TrackMethod('getClientAppointments-client-dashboard')
	async getClientAppointments(
		@Param('ownerId') ownerId: string,
		@Req() req: RequestWithUser,
		@Query('date') date?: string,
		@Query('status') status?: string
	): Promise<ClientAppointmentsResponseDto> {
		try {
			const brandId = req.user?.brandId;

			if (!brandId) {
				this.logger.error('Brand ID not found in user context', {
					user: req.user,
					ownerId
				});
				throw new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				);
			}

			// Parse status if provided
			let parsedStatus: string[] | undefined;
			if (status) {
				try {
					parsedStatus = JSON.parse(status);
				} catch (err) {
					this.logger.error('Error parsing status', {
						error: err,
						status
					});
					parsedStatus = undefined;
				}
			}

			this.logger.log('Getting client appointments', {
				ownerId,
				brandId,
				date,
				status: parsedStatus
			});

			return await this.clientDashboardService.getClientAppointments(
				ownerId,
				brandId,
				{ date, status: parsedStatus }
			);
		} catch (error: any) {
			this.logger.error('Error in getClientAppointments', {
				error,
				ownerId
			});
			throw new HttpException(
				error.message || 'Error retrieving client appointments',
				error.status || HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns list of clinics for client dashboard',
		type: Array
	})
	@Get('/client-clinics')
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST, Role.OWNER)
	@TrackMethod('getClientClinics-client-dashboard')
	async getClientClinics(@Req() req: RequestWithUser): Promise<any[]> {
		try {
			const brandId = req.user?.brandId;

			if (!brandId) {
				this.logger.error('Brand ID not found in user context', {
					user: req.user
				});
				throw new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				);
			}

			this.logger.log('Getting clinics for client dashboard', {
				brandId
			});
			return await this.clientDashboardService.getClientClinics(brandId);
		} catch (error: any) {
			this.logger.error('Error in getClientClinics', {
				error
			});
			throw new HttpException(
				error.message ||
					'Error retrieving clinics for client dashboard',
				error.status || HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}

	@ApiOkResponse({
		description: 'Returns list of doctors for client dashboard',
		type: Array
	})
	@ApiQuery({
		name: 'clinicId',
		required: false,
		description: 'Filter doctors by clinic ID'
	})
	@Get('/client-doctors')
	@UseGuards(JwtAuthGuard, RolesGuard)
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST, Role.OWNER)
	@TrackMethod('getClientDoctors-client-dashboard')
	async getClientDoctors(
		@Req() req: RequestWithUser,
		@Query('clinicId') clinicId?: string
	): Promise<any[]> {
		try {
			const brandId = req.user?.brandId;

			if (!brandId) {
				this.logger.error('Brand ID not found in user context', {
					user: req.user
				});
				throw new HttpException(
					'Brand ID not found in user context',
					HttpStatus.BAD_REQUEST
				);
			}

			this.logger.log('Getting doctors for client dashboard', {
				brandId,
				clinicId
			});
			return await this.clientDashboardService.getClientDoctors(
				brandId,
				clinicId
			);
		} catch (error: any) {
			this.logger.error('Error in getClientDoctors', {
				error,
				clinicId
			});
			throw new HttpException(
				error.message ||
					'Error retrieving doctors for client dashboard',
				error.status || HttpStatus.INTERNAL_SERVER_ERROR
			);
		}
	}
}
