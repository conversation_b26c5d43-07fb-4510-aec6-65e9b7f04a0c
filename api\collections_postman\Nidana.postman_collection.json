{"info": {"_postman_id": "62bd7b83-cd98-4162-8d32-d0959fbeba81", "name": "<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29380890"}, "item": [{"name": "Health Check", "item": [{"name": "Server Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}]}, {"name": "Patients APIs", "item": [{"name": "Create Patient", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.NSAi7_sOtIGGSTy7EJ4pKK_Tu1aSIgO6yKiIXpb2m1I", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"patient_name\": \"<PERSON><PERSON>\",\n  \"ownerFirstName\": \"<PERSON><PERSON><PERSON>\",\n  \"ownerLastName\":\"<PERSON>\",\n  \"ownerPhoneNumber\": \"9104567a8901\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/patients", "host": ["{{base_url}}"], "path": ["patients"]}}, "response": []}, {"name": "Update Patient", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n         \"breed\": \"Ragamuffin\",\n         \"age\": \"2 years\",\n         \"gender\": \"Male\",\n         \"reproductiveStatus\": \"Neutered\",\n         \"microchipId\": \"1234\",\n         \"identification\": \"Heart shape black patch\",\n         \"allergies\": \"no\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/patients/0190d9c0-b620-77f7-9c7c-b9258311e1d8", "host": ["{{base_url}}"], "path": ["patients", "0190d9c0-b620-77f7-9c7c-b9258311e1d8"]}}, "response": []}, {"name": "Get Patient Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/patients/0190d9c0-b620-77f7-9c7c-b9258311e1d8", "host": ["{{base_url}}"], "path": ["patients", "0190d9c0-b620-77f7-9c7c-b9258311e1d8"]}}, "response": []}, {"name": "Get All Patients", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/patients", "host": ["{{base_url}}"], "path": ["patients"]}}, "response": []}, {"name": "Get Clinic Patients", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/patients", "host": ["{{base_url}}"], "path": ["patients"]}}, "response": []}]}, {"name": "Owner's APIs", "item": [{"name": "Create Owner", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/owners", "host": ["{{base_url}}"], "path": ["owners"]}}, "response": []}, {"name": "Update Owner", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/owners/0190d9c0-7512-7c41-abbf-4147e82e800b", "host": ["{{base_url}}"], "path": ["owners", "0190d9c0-7512-7c41-abbf-4147e82e800b"]}}, "response": []}]}, {"name": "Authentication", "item": [{"name": "Super Admin Create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register/super-admin", "host": ["{{base_url}}"], "path": ["auth", "register", "super-admin"]}}, "response": []}, {"name": "Admin Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.oj7mk0SRxY79y_dlyAf0aFuorKASGzY2VgNR-MAy5Xg", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register/admin", "host": ["{{base_url}}"], "path": ["auth", "register", "admin"]}}, "response": []}, {"name": "Staff Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.oj7mk0SRxY79y_dlyAf0aFuorKASGzY2VgNR-MAy5Xg", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register/admin", "host": ["{{base_url}}"], "path": ["auth", "register", "admin"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register/super-admin", "host": ["{{base_url}}"], "path": ["auth", "register", "super-admin"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register/super-admin", "host": ["{{base_url}}"], "path": ["auth", "register", "super-admin"]}}, "response": []}, {"name": "Verify OTP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/auth/register/super-admin", "host": ["{{base_url}}"], "path": ["auth", "register", "super-admin"]}}, "response": []}, {"name": "Login With Pin", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/login/pin?pin=5529", "host": ["{{base_url}}"], "path": ["auth", "login", "pin"], "query": [{"key": "pin", "value": "5529"}]}}, "response": []}]}, {"name": "Users", "item": [{"name": "GetAllUsers", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Update Super Admin", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/users/super-admin", "host": ["{{base_url}}"], "path": ["users", "super-admin"]}}, "response": []}, {"name": "Update Admin", "request": {"method": "PATCH", "header": [], "url": {"raw": "{{base_url}}/users/admin/", "host": ["{{base_url}}"], "path": ["users", "admin", ""]}}, "response": []}, {"name": "Reset PIN for a User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.lltlu15bqBM9yQeAqwYzq1xHUX4OTuYahComHWgIIOM", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\":\"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/reset-pin", "host": ["{{base_url}}"], "path": ["users", "reset-pin"]}}, "response": []}, {"name": "Update User Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.lltlu15bqBM9yQeAqwYzq1xHUX4OTuYahComHWgIIOM", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"mobileNumber\": \"+1234567890\",\n    \"workingHoursStart\": \"9am\",\n    \"workingHoursEnd\": \"5pm\",\n    \"workingDays\": [\"Monday\", \"tuseday\"],\n    \"digitalSignature\": \"My Signature\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/users/complete-profile?pin=5222", "host": ["{{base_url}}"], "path": ["users", "complete-profile"], "query": [{"key": "pin", "value": "5222"}]}}, "response": []}]}, {"name": "Clinic", "item": [{"name": "Create Clinic", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test 5 Clinic\",\n    \"mobile\": \"1111111115\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/clinic/", "host": ["{{base_url}}"], "path": ["clinic", ""]}}, "response": []}, {"name": "Get all clinics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clinic?page=1&limit=6&orderBy=DESC", "host": ["{{base_url}}"], "path": ["clinic"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "6"}, {"key": "orderBy", "value": "DESC"}]}}, "response": []}, {"name": "Get Clinic By Id", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clinic/ac84a9f8-b958-42a1-9ecb-1316f19fba01", "host": ["{{base_url}}"], "path": ["clinic", "ac84a9f8-b958-42a1-9ecb-1316f19fba01"]}}, "response": []}]}, {"name": "Appointments", "item": [{"name": "Create appointment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"date\": \"2024-08-29T10:00:00Z\",\n    \"startTime\": \"2024-07-29T10:00:00Z\",\n    \"doctorIds\": [\"faf8a253-3377-4ba2-a9e9-3ca736fc3ca5\"],\n    \"endTime\": \"2024-07-29T11:00:00Z\",\n    \"patientId\": \"faf8a253-3377-4ba2-a9e9-3ca736fc3ca5\",\n    \"clinicId\": \"faf8a253-3377-4ba2-a9e9-3ca736fc3ca5\",\n    \"type\": \"General Visit\",\n    \"reason\":\"Tired\",\n    \"triage\": \"No Priority\",\n    \"roomId\": \"faf8a253-3377-4ba2-a9e9-3ca736fc3ca5\",\n    \"initialNotes\": \"This field is optional\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/appointments/", "host": ["{{base_url}}"], "path": ["appointments", ""]}}, "response": []}, {"name": "Get all appointments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/appointments?limit=10&orderBy=ASC&page=1", "host": ["{{base_url}}"], "path": ["appointments"], "query": [{"key": "limit", "value": "10"}, {"key": "orderBy", "value": "ASC"}, {"key": "page", "value": "1"}]}}, "response": []}, {"name": "Update appointment details", "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n     \"details\": \"{\\\"someDetail1\\\":\\\"updatedValue1\\\"}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/appointments/31bde7bd-690a-40a6-90cc-3e1998daeaef/details", "host": ["{{base_url}}"], "path": ["appointments", "31bde7bd-690a-40a6-90cc-3e1998<PERSON><PERSON>f", "details"]}}, "response": []}]}, {"name": "Clinic Medications", "item": [{"name": "Get/Search Clinic Mediactions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clinic-medications?search=", "host": ["{{base_url}}"], "path": ["clinic-medications"], "query": [{"key": "search", "value": ""}]}}, "response": []}]}, {"name": "Clinic Plans", "item": [{"name": "Get/Search Clinic Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clinic-plans?search=", "host": ["{{base_url}}"], "path": ["clinic-plans"], "query": [{"key": "search", "value": ""}]}}, "response": []}]}, {"name": "Appointment Assessment", "item": [{"name": "Get/Search Appointment Assessment", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/appointment-assessment?search=", "host": ["{{base_url}}"], "path": ["appointment-assessment"], "query": [{"key": "search", "value": ""}]}}, "response": []}, {"name": "Add assessment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Assessment Test 1017\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/appointment-assessment", "host": ["{{base_url}}"], "path": ["appointment-assessment"]}}, "response": []}]}, {"name": "User OTP's", "item": [{"name": "Create OTP", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/otp/generate?email=<EMAIL>", "host": ["{{base_url}}"], "path": ["otp", "generate"], "query": [{"key": "email", "value": "<EMAIL>"}]}}, "response": []}, {"name": "Validate OTP", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/otp/validate?email=<EMAIL>&otp=561840", "host": ["{{base_url}}"], "path": ["otp", "validate"], "query": [{"key": "email", "value": "<EMAIL>"}, {"key": "otp", "value": "561840"}]}}, "response": []}]}, {"name": "Brands", "item": [{"name": "Create Brands", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "url": {"raw": "{{base_url}}/brands?name=We Care", "host": ["{{base_url}}"], "path": ["brands"], "query": [{"key": "name", "value": "We Care"}]}}, "response": []}, {"name": "Get Brands", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/brands", "host": ["{{base_url}}"], "path": ["brands"]}}, "response": []}]}, {"name": "Clinic Lab Reports", "item": [{"name": "Get/Search Lab Reports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/clinic-lab-reports?search=", "host": ["{{base_url}}"], "path": ["clinic-lab-reports"], "query": [{"key": "search", "value": ""}]}}, "response": []}]}]}