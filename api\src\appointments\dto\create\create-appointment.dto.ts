import { ApiProperty } from '@nestjs/swagger';
import {
	ArrayMinSize,
	ArrayNotEmpty,
	IsArray,
	IsDate,
	IsEmpty,
	IsEnum,
	IsJSON,
	IsNotEmpty,
	IsOptional,
	IsUUID
} from 'class-validator';
// import { EnumAppointmentTriage } from '../enums/enum-appointment-triage';
import { EnumAppointmentType } from '../../enums/enum-appointment-type';
import { EnumAppointmentStatus } from '../../enums/enum-appointment-status';
import { EnumAppointmentMode } from '../../enums/enum-appointment-mode';

export class CreateAppointmentDto {
	@ApiProperty({
		description: 'The clinic id for which appointment is to be made.',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	clinicId!: string;

	@ApiProperty({
		description:
			'The clinic user IDs of doctors for which appointment is to be made.',
		example: ['uuid1, uuid2'],
		isArray: true
	})
	@IsArray()
	@ArrayNotEmpty()
	@ArrayMinSize(1)
	@IsUUID('4', { each: true })
	doctorIds!: string[];

	@ApiProperty({
		description:
			'The clinic user IDs of other providers for the appointment.',
		isArray: true
	})
	@IsArray()
	@IsUUID('4', { each: true })
	@IsOptional()
	providerIds!: string[];

	@ApiProperty({
		description: 'The patient id of a specific clinic.',
		example: 'uuid'
	})
	@IsNotEmpty()
	@IsUUID()
	patientId!: string;

	@ApiProperty({
		description: 'The room id of a specific clinic.',
		example: 'uuid'
	})
	// @IsNotEmpty()
	// @IsUUID()
	@IsOptional()
	roomId?: string;

	@ApiProperty({
		description: 'The date of an appointment'
	})
	@IsNotEmpty()
	// @IsDate()
	date!: string;

	@ApiProperty({
		description: 'The start time of an appointment'
	})
	@IsNotEmpty()
	// @IsDate()
	startTime!: Date;

	@ApiProperty({
		description: 'The end time of an appointment'
	})
	// @IsDate()
	@IsNotEmpty()
	endTime!: Date;

	@ApiProperty({
		description: 'The date of an appointment that was deleted'
	})
	@IsDate()
	@IsOptional()
	deletedAt?: Date;

	@ApiProperty({
		description: 'The reason for an appointment',
		example: 'Visit'
	})
	// @IsNotEmpty()
	@IsOptional()
	reason!: string;

	@ApiProperty({
		description: 'The type of an appointment',
		example: 'Itching'
	})
	@IsNotEmpty()
	@IsEnum(EnumAppointmentType)
	type!: string;

	@ApiProperty({
		description: 'The weight of the patient',
		example: '10'
	})
	@IsOptional()
	weight?: number;

	@ApiProperty({
		description: 'The priority of an appointment',
		example: 'No priority'
	})
	// @IsNotEmpty()
	// @IsEnum(EnumAppointmentTriage)
	@IsOptional()
	triage?: string;

	@ApiProperty({
		description: 'The notes of an appointment'
	})
	@IsOptional()
	notes?: object;

	@ApiProperty({
		description: 'The pre visit questions of an appointment'
	})
	@IsOptional()
	preVisitQuestions?: object;

	@ApiProperty({
		description:
			'The blocked status of a time slot requested for an appointment',
		example: false
	})
	@IsOptional()
	isBlocked?: boolean;

	@IsOptional()
	@IsEnum(EnumAppointmentStatus)
	status?: EnumAppointmentStatus;

	@IsOptional()
	createdAt?: Date;

	@IsOptional()
	updatedAt?: Date;

	@ApiProperty({
		description: 'This is to enter notes during creation',
		example: 'string',
		required: false
	})
	@IsOptional()
	initialNotes?: string;

	@IsOptional()
	@IsEnum(EnumAppointmentMode, {
		message: 'Mode must be either "Clinic" or "Online"'
	})
	mode?: EnumAppointmentMode = EnumAppointmentMode.CLINIC;
}
