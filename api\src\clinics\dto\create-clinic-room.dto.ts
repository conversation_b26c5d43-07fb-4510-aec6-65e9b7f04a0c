import { IsNotEmpty, <PERSON>Optional, IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateClinicRoomDto {
  @ApiProperty({ description: 'The name of the clinic room' })
  @IsNotEmpty()
  @IsString()
  name!: string;

  @ApiProperty({ description: 'The ID of the clinic' })
  @IsNotEmpty()
  @IsUUID()
  clinicId!: string;
}

export class UpdateClinicRoomDto {
    @ApiProperty({ description: 'The name of the clinic room' })
    @IsOptional()
    @IsString()
    name?: string;
  
    @ApiProperty({ description: 'The description of the clinic room' })
    @IsOptional()
    @IsString()
    description?: string;
  
    @ApiProperty({ description: 'The ID of the clinic' })
    @IsOptional()
    @IsUUID()
    clinicId?: string;
  }