"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrandController = void 0;
const common_1 = require("@nestjs/common");
const brands_service_1 = require("./brands.service");
const create_brand_dto_1 = require("./dto/create-brand.dto");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const swagger_1 = require("@nestjs/swagger");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const brand_with_settings_dto_1 = require("./dto/brand-with-settings.dto");
let BrandController = class BrandController {
    constructor(brandService, logger) {
        this.brandService = brandService;
        this.logger = logger;
    }
    async create(createBrandDto) {
        return await this.brandService.createBrand(createBrandDto);
    }
    async findbyId(id) {
        return this.brandService.getBrandById(id);
    }
    async findAll() {
        return this.brandService.getAllBrands();
    }
    async findBySlug(slug) {
        return this.brandService.getBrandBySlug(slug);
    }
};
exports.BrandController = BrandController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Creating a new Brand' }),
    (0, swagger_1.ApiBody)({ type: create_brand_dto_1.CreateBrandDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The Brand has been successfully created.',
        type: create_brand_dto_1.CreateBrandDto
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, track_method_decorator_1.TrackMethod)('create-brands'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_brand_dto_1.CreateBrandDto]),
    __metadata("design:returntype", Promise)
], BrandController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Getting a brand by Id' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Successfully retrieved Brand.',
        type: [create_brand_dto_1.CreateBrandDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, track_method_decorator_1.TrackMethod)('findbyId-brands'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BrandController.prototype, "findbyId", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Getting all Brands' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Successfully retrieved all Brands.',
        type: [create_brand_dto_1.CreateBrandDto]
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, track_method_decorator_1.TrackMethod)('findAll-brands'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BrandController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('slug/:slug'),
    (0, swagger_1.ApiOperation)({
        summary: 'Getting a brand by slug with client booking settings info'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Successfully retrieved Brand by slug with client booking settings status.',
        type: brand_with_settings_dto_1.BrandWithSettingsDto
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('findBySlug-brands'),
    __param(0, (0, common_1.Param)('slug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BrandController.prototype, "findBySlug", null);
exports.BrandController = BrandController = __decorate([
    (0, swagger_1.ApiTags)('Brands')
    // @ApiBearerAuth()
    // @UseGuards(JwtAuthGuard, RolesGuard)
    ,
    (0, common_1.Controller)('brands'),
    __metadata("design:paramtypes", [brands_service_1.BrandService,
        winston_logger_service_1.WinstonLogger])
], BrandController);
//# sourceMappingURL=brands.controller.js.map