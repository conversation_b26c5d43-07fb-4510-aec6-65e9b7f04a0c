import { Test, TestingModule } from '@nestjs/testing';
import { AppointmentAssessmentController } from './appointment-assessment.controller';
import { AppointmentAssessmentService } from './appointment-assessment.service';
import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { User } from '../users/entities/user.entity';
import { CreateAppointmentAssessmentDto } from './dto/create-appointment-assessment.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { RoleService } from '../roles/role.service';
import { retryWhen } from 'rxjs';

describe('AppointmentAssessmentController', () => {
	let controller: AppointmentAssessmentController;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let service: AppointmentAssessmentService;
	const req: { user: { clinicId: string; brandId: string } } = {
		user: { clinicId: 'clinicId', brandId: 'brandId' }
	};
	const mockAppointmentAssessmentService = {
		findAll: jest.fn(),
		createNewAssessment: jest.fn()
	};

	const appointmentAssessments: AppointmentAssessmentEntity[] = [
		{
			id: 1,
			name: 'Respiratory Report',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			isAddedByUser: false,
			clinicId: req.user.clinicId,
			brandId: req.user.brandId
		},
		{
			id: 2,
			name: 'X-ray Report',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			isAddedByUser: false,
			clinicId: req.user.clinicId,
			brandId: req.user.brandId
		}
	];

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [AppointmentAssessmentController],
			providers: [
				{
					provide: AppointmentAssessmentService,
					useValue: mockAppointmentAssessmentService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: { log: jest.fn(), error: jest.fn() }
				}
			]
		}).compile();

		controller = module.get<AppointmentAssessmentController>(
			AppointmentAssessmentController
		);
		service = module.get<AppointmentAssessmentService>(
			AppointmentAssessmentService
		);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('getAppointmentAssessments', () => {
		it('should return an array of appointment assessments without a search keyword', async () => {
			mockAppointmentAssessmentService.findAll.mockResolvedValue(
				appointmentAssessments
			);

			const result = await controller.getAppointmentAssessments(req);
			expect(result).toEqual(appointmentAssessments);
			expect(
				mockAppointmentAssessmentService.findAll
			).toHaveBeenCalledWith(undefined, req.user.clinicId);
		});

		it('should return an array of appointment assessments with a search keyword', async () => {
			const searchKeyword = 'Blood Test';
			const filteredAssessments = appointmentAssessments.filter(
				assessment => assessment.name.includes(searchKeyword)
			);

			mockAppointmentAssessmentService.findAll.mockResolvedValue(
				filteredAssessments
			);

			const result = await controller.getAppointmentAssessments(
				req,
				searchKeyword
			);
			expect(result).toEqual(filteredAssessments);
			expect(
				mockAppointmentAssessmentService.findAll
			).toHaveBeenCalledWith(searchKeyword, req.user.clinicId);
		});
	});

	describe('Create New assessment (user added) - - POST /appointment-assessment', () => {
		const mockNewAssessmentDto: CreateAppointmentAssessmentDto = {
			name: 'New Assessment',
			isAddedByUser: true
		};

		const mockNewAssessmentEntity: AppointmentAssessmentEntity = {
			id: 1001,
			name: 'New Assessment',
			createdAt: new Date(),
			updatedAt: new Date(),
			createdBy: '',
			createdByUser: new User(),
			updatedBy: '',
			updatedByUser: new User(),
			isAddedByUser: true,
			clinicId: req.user.clinicId,
			brandId: req.user.brandId
		};

		it('should have a createNewAssessment function', () => {
			expect(controller.createNewAssessment).toBeDefined();
		});

		it('should create a new assessment (user added)', async () => {
			mockAppointmentAssessmentService.createNewAssessment.mockResolvedValue(
				mockNewAssessmentEntity
			);

			const result = await controller.createNewAssessment(
				mockNewAssessmentDto,
				req
			);

			expect(result).toEqual(mockNewAssessmentEntity);
			expect(
				mockAppointmentAssessmentService.createNewAssessment
			).toHaveBeenCalledWith(
				mockNewAssessmentDto,
				req.user.clinicId,
				req.user.brandId
			);
		});

		it('should throw an error if something goes wrong', async () => {
			const error = new HttpException(
				'Something went wrong',
				HttpStatus.BAD_REQUEST
			);

			mockAppointmentAssessmentService.createNewAssessment.mockRejectedValue(
				error
			);

			await expect(
				controller.createNewAssessment(mockNewAssessmentDto, req)
			).rejects.toThrow('Something went wrong');

			expect(
				mockAppointmentAssessmentService.createNewAssessment
			).toHaveBeenCalledWith(
				mockNewAssessmentDto,
				req.user.clinicId,
				req.user.brandId
			);
		});
	});
});
