import { Module } from '@nestjs/common';
import { AppointmentAssessmentService } from './appointment-assessment.service';
import { AppointmentAssessmentController } from './appointment-assessment.controller';
import { AppointmentAssessmentEntity } from './entities/appointment-assessment.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleModule } from '../roles/role.module';

@Module({
	imports: [TypeOrmModule.forFeature([AppointmentAssessmentEntity]),RoleModule],
	controllers: [AppointmentAssessmentController],
	providers: [AppointmentAssessmentService]
})
export class AppointmentAssessmentModule {}
