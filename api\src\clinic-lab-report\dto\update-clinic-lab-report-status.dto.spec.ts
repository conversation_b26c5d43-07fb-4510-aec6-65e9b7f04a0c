import { validate } from 'class-validator';
import { UpdateStatusDto } from './update-clinic-lab-report-status.dto';

describe('UpdateStatusDto', () => {
    
    it('UpdateStatusDto -> should update status', async () => {
        let dto = new UpdateStatusDto();
        dto.id = '9c1aaa0e-847c-4bca-9da0-72960eaa269d';
        dto.status = 'COMPLETED';
    
        const errors = await validate(dto);

        expect(errors.length).toEqual(0);
    });

    it('dto id should be valid', async () => {
        let dto = new UpdateStatusDto();
        dto.id = '';
        dto.status = 'COMPLETED';
    
        const errors = await validate(dto);

        expect(errors.length).toEqual(1);
    });
});
