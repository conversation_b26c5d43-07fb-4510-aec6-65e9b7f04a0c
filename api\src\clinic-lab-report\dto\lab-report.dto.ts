import 'reflect-metadata';
import {
	IsUUID,
	IsArray,
	ValidateNested,
	IsString,
	IsNumber,
	IsDateString,
	IsEnum,
	IsOptional
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FileInfoDto {
	@IsString()
	s3Url?: string;

	@IsString()
	fileName?: string;

	@IsNumber()
	fileSize?: number;

	@IsDateString()
	uploadDate?: string;

	@IsString()
	fileKey!: string;

	@IsString()
	@IsOptional()
	fileUuid?: string;
}

export class CreateLabReportDto {
	@IsUUID()
	appointmentId!: string;

	@IsUUID()
	clinicLabReportId!: string;

	@IsUUID()
	@IsOptional()
	patientId?: string;

	@IsUUID()
	clinicId!: string;

	@IsUUID()
	@IsOptional()
	lineItemId?: string;

	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => FileInfoDto)
	files!: FileInfoDto[];

	@IsEnum({
		type: 'enum',
		enum: ['PENDING', 'COMPLETED'],
		default: 'PENDING'
	})
	status!: 'PENDING' | 'COMPLETED';

	@ApiProperty({
		description: 'The details of the IDEXX order created',
		example: 'json'
	})
	integrationDetails!: object;

	@ApiProperty({
		description: 'The order id of the IDEXX order created',
		example: '123456'
	})
	integrationOrderId?: string;
}

export class UpdateLabReportDto {
	@IsNumber()
	price!: number;
	@IsNumber()
	tax!: number;
}
