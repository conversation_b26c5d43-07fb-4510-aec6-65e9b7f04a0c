import {
	Injectable,
	NotFoundException,
	BadRequestException,
	ForbiddenException,
	InternalServerErrorException,
	Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as moment from 'moment';

import { AppointmentsService } from '../appointments/appointments.service';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { EnumAppointmentMode } from '../appointments/enums/enum-appointment-mode';
import { EnumAppointmentStatus } from '../appointments/enums/enum-appointment-status';
import { EnumAppointmentType } from '../appointments/enums/enum-appointment-type';
import { EnumAppointmentTriage } from '../appointments/enums/enum-appointment-triage';
import {
	CreateClientBookingDto,
	UpdateClientBookingDto,
	ClientBookingResponseDto
} from './dto/client-booking.dto';
import { Patient } from '../patients/entities/patient.entity';
import { User } from '../users/entities/user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { ClientAvailabilityService } from './client-availability.service';
import { PatientsService } from '../patients/patients.service';
import {
	AppointmentAuditLog,
	AppointmentAuditLogAction
} from '../audit/entities/appointment-audit-log.entity';
import { ClinicService } from '../clinics/clinic.service';
import {
	ClientBookingSettings,
	ClientBookingWorkingHours,
	TimeDuration
} from '../clinics/entities/clinic.entity';

// Helper function (can be moved to a utils file)
function timeDurationToMinutes(
	duration: TimeDuration | null | undefined
): number | null {
	if (!duration) return null;
	const days = duration.days || 0;
	const hours = duration.hours || 0;
	const minutes = duration.minutes || 0;
	const totalMinutes = days * 24 * 60 + hours * 60 + minutes;
	return totalMinutes > 0 ? totalMinutes : null;
}

// Extended settings interface
interface EffectiveClientBookingSettings extends ClientBookingSettings {
	minBookingLeadMinutes?: number | null;
	modificationDeadlineMinutes?: number | null;
	maxAdvanceBookingMinutes?: number | null;
}

@Injectable()
export class ClientBookingService {
	private readonly logger = new Logger(ClientBookingService.name);

	constructor(
		@InjectRepository(AppointmentEntity)
		private appointmentsRepository: Repository<AppointmentEntity>,

		@InjectRepository(Patient)
		private patientsRepository: Repository<Patient>,

		@InjectRepository(User)
		private usersRepository: Repository<User>,

		@InjectRepository(ClinicEntity)
		private clinicsRepository: Repository<ClinicEntity>,

		@InjectRepository(ClinicUser)
		private clinicUsersRepository: Repository<ClinicUser>,

		private appointmentsService: AppointmentsService,
		private clientAvailabilityService: ClientAvailabilityService,
		private patientsService: PatientsService,
		private clinicService: ClinicService,

		@InjectRepository(AppointmentAuditLog)
		private auditLogRepository: Repository<AppointmentAuditLog>
	) {}

	/**
	 * Private helper to log audit events
	 * @param appointmentId ID of the affected appointment
	 * @param userId ID of the user performing the action
	 * @param action The action performed
	 * @param changedFields Details of the changes (optional)
	 * @param context Additional context (optional)
	 */
	private async _logAuditEvent(
		appointmentId: string,
		userId: string,
		action: AppointmentAuditLogAction,
		changedFields: Record<string, any> | null = null,
		context: string | null = null
	): Promise<void> {
		try {
			const logEntry = this.auditLogRepository.create({
				appointmentId,
				userId,
				action,
				changedFields,
				context,
				timestamp: new Date()
			});
			await this.auditLogRepository.save(logEntry);
		} catch (error) {
			this.logger.error('Failed to save appointment audit log', {
				appointmentId,
				action,
				error
			});
		}
	}

	// Helper to get settings and determine effective settings
	private async getEffectiveBookingSettings(
		clinicId: string | undefined
	): Promise<EffectiveClientBookingSettings> {
		const defaultSettings: EffectiveClientBookingSettings = {
			isEnabled: true,
			workingHours: null,
			allowedDoctorIds: null,
			minBookingLeadTime: null,
			modificationDeadlineTime: null,
			maxAdvanceBookingTime: null,
			minBookingLeadHours: 0,
			modificationDeadlineHours: 0,
			minBookingLeadMinutes: null,
			modificationDeadlineMinutes: null,
			maxAdvanceBookingMinutes: null
		};
		if (!clinicId) return defaultSettings;

		try {
			const settings =
				await this.clinicService.getClientBookingSettings(clinicId);

			const minLeadMinutes = timeDurationToMinutes(
				settings?.minBookingLeadTime
			);
			const modDeadlineMinutes = timeDurationToMinutes(
				settings?.modificationDeadlineTime
			);
			const maxAdvanceMinutes = timeDurationToMinutes(
				settings?.maxAdvanceBookingTime
			);

			const effectiveSettings: EffectiveClientBookingSettings = {
				isEnabled: settings?.isEnabled ?? defaultSettings.isEnabled,
				allowAllDoctors:
					settings?.allowAllDoctors ??
					defaultSettings.allowAllDoctors,
				workingHours:
					settings?.workingHours ?? defaultSettings.workingHours,
				allowedDoctorIds:
					settings?.allowedDoctorIds ??
					defaultSettings.allowedDoctorIds,
				minBookingLeadTime:
					settings?.minBookingLeadTime ??
					defaultSettings.minBookingLeadTime,
				modificationDeadlineTime:
					settings?.modificationDeadlineTime ??
					defaultSettings.modificationDeadlineTime,
				maxAdvanceBookingTime:
					settings?.maxAdvanceBookingTime ??
					defaultSettings.maxAdvanceBookingTime,
				minBookingLeadMinutes: minLeadMinutes,
				modificationDeadlineMinutes: modDeadlineMinutes,
				maxAdvanceBookingMinutes: maxAdvanceMinutes,
				minBookingLeadHours:
					settings?.minBookingLeadHours ??
					defaultSettings.minBookingLeadHours,
				modificationDeadlineHours:
					settings?.modificationDeadlineHours ??
					defaultSettings.modificationDeadlineHours
			};
			return effectiveSettings;
		} catch (error: any) {
			console.error(
				`Error fetching settings for clinic ${clinicId}, using defaults`,
				error
			);
			return defaultSettings;
		}
	}

	/**
	 * Create a new client booking
	 * @param createBookingDto DTO with booking details
	 * @param ownerId Owner ID from auth
	 * @returns Booking details
	 */
	async createClientBooking(
		createBookingDto: CreateClientBookingDto,
		ownerId: string
	): Promise<ClientBookingResponseDto> {
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to book appointments'
			);
		}

		// --- Fetch Entities --- Start ---
		// 1. Fetch Pet and verify ownership
		const pet = await this.patientsRepository.findOne({
			where: { id: createBookingDto.petId },
			relations: ['patientOwners', 'patientOwners.ownerBrand']
		});
		if (!pet) {
			throw new NotFoundException(
				`Pet with ID ${createBookingDto.petId} not found`
			);
		}
		const ownerHasPet = pet.patientOwners.some(
			patientOwner => patientOwner.ownerId === ownerId
		);
		if (!ownerHasPet) {
			throw new ForbiddenException(
				'You can only book appointments for your own pets'
			);
		}

		// 2. Fetch Clinic
		const clinic = await this.clinicsRepository.findOne({
			where: { id: createBookingDto.clinicId }
		});
		if (!clinic) {
			throw new NotFoundException(
				`Clinic with ID ${createBookingDto.clinicId} not found`
			);
		}
		const brandId = clinic.brandId; // Needed for appointment creation

		// 3. Fetch Doctor (ClinicUser) and verify clinic association
		const doctorClinicUser = await this.clinicUsersRepository.findOne({
			where: {
				id: createBookingDto.doctorId,
				clinicId: createBookingDto.clinicId
			},
			relations: ['user']
		});
		if (!doctorClinicUser) {
			throw new NotFoundException(
				`Doctor with ID ${createBookingDto.doctorId} not found for this clinic`
			);
		}
		// --- Fetch Entities --- End ---

		// --- Settings Validation --- Start ---
		const settings = await this.getEffectiveBookingSettings(
			createBookingDto.clinicId
		);

		// 1. Check isEnabled
		if (!settings.isEnabled) {
			throw new BadRequestException(
				'Online booking is currently disabled for this clinic.'
			);
		}

		// 2. Check Doctor Allowed (only if allowAllDoctors is false and a specific list is provided)
		if (
			settings.allowAllDoctors === false &&
			Array.isArray(settings.allowedDoctorIds) &&
			settings.allowedDoctorIds.length > 0 &&
			!settings.allowedDoctorIds.includes(createBookingDto.doctorId)
		) {
			throw new BadRequestException(
				'Selected doctor is not available for online booking.'
			);
		}

		// 3. Check Lead Time (using minutes)
		const nowMoment = moment();
		// Parse date in DD-MMM-YYYY format and extract time from ISO string
		const dateMoment = moment(createBookingDto.date, 'DD-MMM-YYYY');
		if (!dateMoment.isValid()) {
			throw new BadRequestException(
				'Invalid date format. Use DD-MMM-YYYY (e.g., 30-Apr-2025).'
			);
		}

		// Parse start and end times from ISO strings
		const startMoment = moment(createBookingDto.startTime);
		const endMoment = moment(createBookingDto.endTime);
		if (!startMoment.isValid() || !endMoment.isValid()) {
			throw new BadRequestException(
				'Invalid start or end time format provided.'
			);
		}

		// Check if start time is before end time
		if (startMoment.isSameOrAfter(endMoment)) {
			throw new BadRequestException(
				'Start time must be earlier than end time.'
			);
		}

		if (settings.minBookingLeadMinutes) {
			const leadTimeThreshold = nowMoment
				.clone()
				.add(settings.minBookingLeadMinutes, 'minutes');
			if (startMoment.isBefore(leadTimeThreshold)) {
				throw new BadRequestException(
					`Booking must be made at least ${settings.minBookingLeadMinutes} minutes in advance.`
				);
			}
		}

		// 4. Check Maximum Advance Booking Time (using minutes)
		if (settings.maxAdvanceBookingMinutes) {
			const maxAdvanceThreshold = nowMoment
				.clone()
				.add(settings.maxAdvanceBookingMinutes, 'minutes');
			if (startMoment.isAfter(maxAdvanceThreshold)) {
				throw new BadRequestException(
					`Booking cannot be made more than ${settings.maxAdvanceBookingMinutes} minutes in advance.`
				);
			}
		}

		// 5. Check Working Hours
		if (settings.workingHours) {
			const dayOfWeek = startMoment
				.format('dddd')
				.toLowerCase() as keyof ClientBookingWorkingHours;
			const daySchedule = settings.workingHours?.[dayOfWeek];

			const allowedIntervals = Array.isArray(daySchedule)
				? daySchedule.filter(
						interval =>
							interval.isWorkingDay &&
							interval.startTime &&
							interval.endTime
					)
				: [];

			// Get clinic timezone for proper comparison
			const clinicTimezone = clinic.timezone || 'Asia/Kolkata'; // Default to IST if no timezone set

			const isWithinAllowedInterval = allowedIntervals.some(interval => {
				if (!interval.startTime || !interval.endTime) return false;

				// Format date as YYYY-MM-DD for consistent comparison
				const formattedDate = dateMoment.format('YYYY-MM-DD');

				// Convert start/end times to clinic timezone for comparison
				const startTimeInClinicTz =
					moment(startMoment).tz(clinicTimezone);
				const endTimeInClinicTz = moment(endMoment).tz(clinicTimezone);

				// Create moments in clinic timezone for working hour intervals
				const intervalStartMoment = moment.tz(
					`${formattedDate} ${interval.startTime}`,
					'YYYY-MM-DD HH:mm',
					clinicTimezone
				);
				const intervalEndMoment = moment.tz(
					`${formattedDate} ${interval.endTime}`,
					'YYYY-MM-DD HH:mm',
					clinicTimezone
				);

				// Compare the time portions in clinic's timezone
				const startTimeLocal = startTimeInClinicTz.format('HH:mm');
				const endTimeLocal = endTimeInClinicTz.format('HH:mm');
				const intervalStartLocal = intervalStartMoment.format('HH:mm');
				const intervalEndLocal = intervalEndMoment.format('HH:mm');

				this.logger.debug('Comparing times in clinic timezone', {
					startTimeLocal,
					endTimeLocal,
					intervalStartLocal,
					intervalEndLocal,
					clinicTimezone
				});

				return (
					startTimeLocal >= intervalStartLocal &&
					endTimeLocal <= intervalEndLocal
				);
			});

			if (!isWithinAllowedInterval) {
				throw new BadRequestException(
					'Requested time slot is outside of the allowed booking hours for this day.'
				);
			}
		}
		// --- Settings Validation --- End ---

		// --- Appointment Creation --- Start ---

		const appointmentData = {
			patientId: createBookingDto.petId,
			clinicId: createBookingDto.clinicId,
			doctorIds: [createBookingDto.doctorId],
			providerIds: [], // Initialize providerIds as empty array (adjust if needed)
			date: createBookingDto.date, // Use YYYY-MM-DD string as required by service
			startTime: startMoment.toDate(), // Use parsed moment objects directly
			endTime: endMoment.toDate(), // Use parsed moment objects directly
			reason: createBookingDto.reason || '', // Provide default empty string for reason
			mode: EnumAppointmentMode.ONLINE,
			status: EnumAppointmentStatus.Scheduled, // Default status for new booking
			type: EnumAppointmentType.Consultation, // Default type for client booking
			ownerId: ownerId,
			createdBy: ownerId // Owner is the creator
			// brandId is needed by appointmentsService.createAppointment
		};

		try {
			const createdAppointmentEntity =
				await this.appointmentsService.createAppointment(
					appointmentData,
					brandId
				);

			// Fetch the newly created appointment with relations for response formatting
			const newAppointment = await this.appointmentsRepository.findOne({
				where: { id: createdAppointmentEntity.id },
				relations: [
					'patient',
					'clinic',
					'appointmentDoctors',
					'appointmentDoctors.clinicUser',
					'appointmentDoctors.clinicUser.user'
				]
			});

			if (!newAppointment) {
				throw new InternalServerErrorException(
					'Failed to retrieve created appointment details.'
				);
			}

			// Log audit event
			await this._logAuditEvent(
				newAppointment.id,
				ownerId,
				AppointmentAuditLogAction.CREATE,
				createBookingDto,
				'Client booking created'
			);

			return this.formatAppointmentResponse(newAppointment);
		} catch (error) {
			this.logger.error('Error creating appointment via client booking', {
				error,
				createBookingDto
			});
			if (
				error instanceof BadRequestException ||
				error instanceof NotFoundException ||
				error instanceof ForbiddenException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to create the appointment.'
			);
		}
		// --- Appointment Creation --- End ---
	}

	/**
	 * Get details of a client booking
	 * @param appointmentId Appointment ID
	 * @param ownerId Owner ID from auth
	 * @returns Booking details
	 */
	async getClientBooking(
		appointmentId: string,
		ownerId: string
	): Promise<ClientBookingResponseDto> {
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to view appointments'
			);
		}

		// Get the appointment with relations
		const appointment = await this.appointmentsRepository.findOne({
			where: { id: appointmentId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'clinic',
				'appointmentDoctors',
				'appointmentDoctors.clinicUser',
				'appointmentDoctors.clinicUser.user'
			]
		});

		if (!appointment) {
			throw new NotFoundException(
				`Appointment with ID ${appointmentId} not found`
			);
		}

		// Verify that the pet belongs to the owner
		const ownerHasPet = appointment.patient.patientOwners.some(
			patientOwner => patientOwner.ownerId === ownerId
		);

		if (!ownerHasPet) {
			throw new ForbiddenException(
				'You can only view appointments for your own pets'
			);
		}

		// Format the response
		return this.formatAppointmentResponse(appointment);
	}

	/**
	 * Update a client booking (reschedule or cancel)
	 * @param appointmentId Appointment ID
	 * @param updateBookingDto DTO with updated booking details
	 * @param ownerId Owner ID from auth
	 * @returns Updated booking details
	 */
	async updateClientBooking(
		appointmentId: string,
		updateBookingDto: UpdateClientBookingDto,
		ownerId: string
	): Promise<ClientBookingResponseDto> {
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to update appointments'
			);
		}

		const appointment = await this.appointmentsRepository.findOne({
			where: { id: appointmentId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'clinic',
				'appointmentDoctors',
				'appointmentDoctors.clinicUser',
				'appointmentDoctors.clinicUser.user'
			]
		});

		if (!appointment) {
			throw new NotFoundException(
				`Appointment with ID ${appointmentId} not found`
			);
		}

		const ownerHasPet = appointment.patient.patientOwners.some(
			patientOwner => patientOwner.ownerId === ownerId
		);
		if (!ownerHasPet) {
			throw new ForbiddenException(
				'You can only update appointments for your own pets'
			);
		}

		const allowedStatuses = [
			EnumAppointmentStatus.Scheduled,
			EnumAppointmentStatus.Checkedin
		];
		if (
			!allowedStatuses.includes(
				appointment.status as EnumAppointmentStatus
			)
		) {
			throw new BadRequestException(
				`Cannot update appointment with status ${appointment.status}`
			);
		}

		// --- Settings Validation (Modification Deadline) --- Start ---
		const settings = await this.getEffectiveBookingSettings(
			appointment.clinicId
		);

		if (settings.modificationDeadlineMinutes !== null) {
			if (!appointment.startTime) {
				throw new InternalServerErrorException(
					'Appointment start time is missing, cannot check deadline.'
				);
			}
			const originalAppointmentStartTime = moment(appointment.startTime);
			const deadline = originalAppointmentStartTime
				.clone()
				.subtract(settings.modificationDeadlineMinutes, 'minutes');
			if (moment().isAfter(deadline)) {
				throw new BadRequestException(
					`Edits are not allowed within ${settings.modificationDeadlineMinutes} minutes of the appointment time.`
				);
			}
		}

		if (
			!settings.isEnabled &&
			moment().isBefore(
				moment(appointment.startTime).subtract(
					settings.modificationDeadlineMinutes ?? 0,
					'minutes'
				)
			)
		) {
			throw new BadRequestException(
				'Online booking modifications are currently disabled for this clinic.'
			);
		}
		// --- Settings Validation --- End ---

		// --- Rescheduling Validation --- Start ---
		const isRescheduling =
			updateBookingDto.date ||
			updateBookingDto.startTime ||
			updateBookingDto.endTime ||
			updateBookingDto.doctorId;

		if (isRescheduling) {
			this.logger.log('Performing reschedule validation', {
				appointmentId
			});

			// Use original values as fallback if not provided in DTO
			const newDateStr =
				updateBookingDto.date ||
				moment(appointment.date).format('DD-MMM-YYYY');
			const newStartTimeStr =
				updateBookingDto.startTime ||
				moment(appointment.startTime).toISOString();
			const newEndTimeStr =
				updateBookingDto.endTime ||
				moment(appointment.endTime).toISOString();
			const newDoctorId =
				updateBookingDto.doctorId ||
				appointment.appointmentDoctors[0]?.doctorId;

			if (!newDoctorId) {
				throw new BadRequestException(
					'Doctor ID is missing for rescheduling.'
				);
			}

			// Attempt to parse the provided date and times
			const newDateMoment = moment(newDateStr, 'DD-MMM-YYYY');
			const newStartMoment = moment(newStartTimeStr); // Parse ISO string
			const newEndMoment = moment(newEndTimeStr); // Parse ISO string

			// Validate parsed moments
			if (
				!newDateMoment.isValid() ||
				!newStartMoment.isValid() ||
				!newEndMoment.isValid() ||
				newStartMoment.isSameOrAfter(newEndMoment)
			) {
				this.logger.warn('Invalid date/time format during reschedule', {
					date: newDateStr,
					startTime: newStartTimeStr,
					endTime: newEndTimeStr
				});
				throw new BadRequestException(
					'Invalid new date, start time, or end time format/values provided.'
				);
			}

			// --- Run checks using the parsed moment objects (newStartMoment, newEndMoment) ---

			// 2. Re-check Doctor Allowed (if doctor changed)
			if (
				updateBookingDto.doctorId &&
				settings.allowAllDoctors === false &&
				Array.isArray(settings.allowedDoctorIds) &&
				settings.allowedDoctorIds.length > 0 &&
				!settings.allowedDoctorIds.includes(newDoctorId)
			) {
				throw new BadRequestException(
					'Selected doctor is not available for online booking.'
				);
			}

			// 3a. Re-check Lead Time for the NEW time against NOW
			const nowMoment = moment();
			if (settings.minBookingLeadMinutes) {
				const leadTimeThreshold = nowMoment
					.clone()
					.add(settings.minBookingLeadMinutes, 'minutes');
				if (newStartMoment.isBefore(leadTimeThreshold)) {
					// Use parsed moment
					throw new BadRequestException(
						`Rescheduled time must be at least ${settings.minBookingLeadMinutes} minutes in advance.`
					);
				}
			}

			// 3b. Re-check Max Advance Booking for the NEW time against NOW
			if (settings.maxAdvanceBookingMinutes) {
				const maxAdvanceThreshold = nowMoment
					.clone()
					.add(settings.maxAdvanceBookingMinutes, 'minutes');
				if (newStartMoment.isAfter(maxAdvanceThreshold)) {
					// Use parsed moment
					throw new BadRequestException(
						`Rescheduled time cannot be more than ${settings.maxAdvanceBookingMinutes} minutes in advance.`
					);
				}
			}

			// 4. Re-check Working Hours for the NEW time
			if (settings.workingHours) {
				const dayOfWeek = newStartMoment
					.format('dddd')
					.toLowerCase() as keyof ClientBookingWorkingHours;
				const daySchedule = settings.workingHours?.[dayOfWeek];
				const allowedIntervals = Array.isArray(daySchedule)
					? daySchedule.filter(
							i => i.isWorkingDay && i.startTime && i.endTime
						)
					: [];

				// Get clinic timezone for proper comparison
				const clinicTimezone =
					appointment.clinic.timezone || 'Asia/Kolkata'; // Default to IST if no timezone set

				const isWithinAllowedInterval = allowedIntervals.some(
					interval => {
						if (!interval.startTime || !interval.endTime)
							return false;

						// Convert start/end times to clinic timezone for comparison
						const startTimeInClinicTz =
							moment(newStartMoment).tz(clinicTimezone);
						const endTimeInClinicTz =
							moment(newEndMoment).tz(clinicTimezone);

						// Create moments in clinic timezone for working hour intervals
						const intervalStartMoment = moment.tz(
							`${newDateMoment.format('YYYY-MM-DD')} ${interval.startTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);
						const intervalEndMoment = moment.tz(
							`${newDateMoment.format('YYYY-MM-DD')} ${interval.endTime}`,
							'YYYY-MM-DD HH:mm',
							clinicTimezone
						);

						// Compare the time portions in clinic's timezone
						const startTimeLocal =
							startTimeInClinicTz.format('HH:mm');
						const endTimeLocal = endTimeInClinicTz.format('HH:mm');
						const intervalStartLocal =
							intervalStartMoment.format('HH:mm');
						const intervalEndLocal =
							intervalEndMoment.format('HH:mm');

						this.logger.debug(
							'Comparing times in clinic timezone for update',
							{
								startTimeLocal,
								endTimeLocal,
								intervalStartLocal,
								intervalEndLocal,
								clinicTimezone
							}
						);

						return (
							startTimeLocal >= intervalStartLocal &&
							endTimeLocal <= intervalEndLocal
						);
					}
				);

				if (!isWithinAllowedInterval) {
					throw new BadRequestException(
						'Requested time slot is outside of the allowed booking hours for this day.'
					);
				}
			}

			this.logger.log('Reschedule validation passed', { appointmentId });
		}
		// --- Rescheduling Validation --- End ---

		// --- Update Appointment --- Start ---
		const updatePayload: any = {
			patientId: appointment.patientId,
			providerIds: []
		};
		const changedFields: Record<string, any> = {};

		// Use parsed moments for setting Date objects if rescheduling occurred
		if (updateBookingDto.date) {
			const parsedDate = moment(updateBookingDto.date, 'DD-MMM-YYYY');
			if (!parsedDate.isValid())
				throw new BadRequestException(
					'Invalid date format for update.'
				);
			updatePayload.date = parsedDate.toDate();
			changedFields.date = updateBookingDto.date;
		}
		if (updateBookingDto.startTime) {
			const parsedStartTime = moment(updateBookingDto.startTime); // Parse ISO
			if (!parsedStartTime.isValid())
				throw new BadRequestException(
					'Invalid startTime format for update.'
				);
			updatePayload.startTime = parsedStartTime.toDate();
			changedFields.startTime = updateBookingDto.startTime;
		}
		if (updateBookingDto.endTime) {
			const parsedEndTime = moment(updateBookingDto.endTime); // Parse ISO
			if (!parsedEndTime.isValid())
				throw new BadRequestException(
					'Invalid endTime format for update.'
				);
			updatePayload.endTime = parsedEndTime.toDate();
			changedFields.endTime = updateBookingDto.endTime;
		}

		if (updateBookingDto.doctorId) {
			updatePayload.doctorIds = [updateBookingDto.doctorId];
			changedFields.doctorId = updateBookingDto.doctorId;
		} else if (isRescheduling) {
			// If rescheduling but doctor didn't change, still need to pass it
			updatePayload.doctorIds = appointment.appointmentDoctors.map(
				doc => doc.doctorId
			);
		}

		try {
			// Only call update if there are actual changes
			if (Object.keys(changedFields).length > 0) {
				await this.appointmentsService.updateAppointment(
					appointmentId,
					updatePayload
				);

				// Log audit event only if changes were made
				await this._logAuditEvent(
					appointmentId,
					ownerId,
					AppointmentAuditLogAction.UPDATE,
					changedFields,
					'Client booking updated'
				);
			} else {
				this.logger.log('No changes detected for appointment update', {
					appointmentId
				});
			}

			// Fetch the updated appointment for response regardless of whether changes were made
			const updatedAppointment =
				await this.appointmentsRepository.findOne({
					where: { id: appointmentId },
					relations: [
						'patient',
						'clinic',
						'appointmentDoctors',
						'appointmentDoctors.clinicUser',
						'appointmentDoctors.clinicUser.user'
					]
				});

			if (!updatedAppointment) {
				throw new InternalServerErrorException(
					'Failed to retrieve appointment details after update.'
				);
			}

			return this.formatAppointmentResponse(updatedAppointment);
		} catch (error) {
			this.logger.error('Error updating appointment via client booking', {
				error,
				appointmentId,
				updateBookingDto
			});
			if (
				error instanceof BadRequestException ||
				error instanceof NotFoundException ||
				error instanceof ForbiddenException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to update the appointment.'
			);
		}
		// --- Update Appointment --- End ---
	}

	/**
	 * Format an appointment entity to the client booking response format
	 * @param appointment Appointment entity
	 * @returns Formatted booking response
	 */
	private formatAppointmentResponse(
		appointment: AppointmentEntity
	): ClientBookingResponseDto {
		if (!appointment) {
			throw new Error('Cannot format null appointment');
		}

		// Mark deleted appointments as cancelled
		if (appointment.deletedAt) {
			appointment.status = EnumAppointmentStatus.Cancelled;
		}

		// Convert dates to strings
		let dateStr = '';
		if (appointment.date) {
			dateStr =
				appointment.date instanceof Date
					? appointment.date.toISOString().split('T')[0] // YYYY-MM-DD
					: String(appointment.date);
		}

		let startTimeStr = '';
		if (appointment.startTime) {
			startTimeStr =
				appointment.startTime instanceof Date
					? appointment.startTime.toTimeString().slice(0, 5) // HH:MM
					: String(appointment.startTime);
		}

		let endTimeStr = '';
		if (appointment.endTime) {
			endTimeStr =
				appointment.endTime instanceof Date
					? appointment.endTime.toTimeString().slice(0, 5) // HH:MM
					: String(appointment.endTime);
		}

		// Get doctor info safely
		let doctorId = '';
		let doctorName = 'Unknown';

		if (appointment.appointmentDoctors?.length > 0) {
			// Get the doctor ID from the appointmentDoctors relation
			doctorId = appointment.appointmentDoctors[0].doctorId || '';

			// Try to get doctor name through the clinicUser relationship
			if (appointment.appointmentDoctors[0]?.clinicUser?.user) {
				const doctor =
					appointment.appointmentDoctors[0].clinicUser.user;
				doctorName =
					`Dr. ${doctor.firstName || ''} ${doctor.lastName || ''}`.trim();
				if (doctorName === 'Dr.') {
					doctorName = `Dr. ID: ${doctorId.slice(-6)}`;
				}
			} else {
				// Fallback to showing just the ID
				doctorName = doctorId
					? `Dr. ID: ${doctorId.slice(-6)}`
					: 'Unknown';
			}
		}

		// Format notes - handle object conversion to avoid "[object Object]"
		let notesStr = '';
		if (appointment.notes) {
			if (typeof appointment.notes === 'object') {
				// If it's an object with a text property (common format)
				if ((appointment.notes as any).text) {
					notesStr = (appointment.notes as any).text;
				} else {
					// Otherwise try to stringify it
					try {
						notesStr = JSON.stringify(appointment.notes);
					} catch {
						notesStr = String(appointment.notes);
					}
				}
			} else {
				notesStr = String(appointment.notes);
			}
		}

		// Get pet name properly
		const petName = appointment.patient?.patientName || 'Unknown';

		// Get clinic name
		const clinicName = appointment.clinic?.name || 'Unknown';

		return {
			id: appointment.id,
			petId: appointment.patientId,
			petName: petName,
			doctorId: doctorId,
			doctorName: doctorName,
			clinicId: appointment.clinicId,
			clinicName: clinicName,
			date: dateStr,
			startTime: startTimeStr,
			endTime: endTimeStr,
			status: appointment.status ? String(appointment.status) : '',
			notes: notesStr,
			createdAt: appointment.createdAt || new Date(),
			updatedAt: appointment.updatedAt || new Date()
		};
	}

	/**
	 * Delete a client booking (cancel)
	 * @param appointmentId Appointment ID
	 * @param ownerId Owner ID from auth
	 * @returns Deleted booking details
	 */
	async deleteClientBooking(
		appointmentId: string,
		ownerId: string
	): Promise<ClientBookingResponseDto> {
		if (!ownerId) {
			throw new ForbiddenException(
				'User must be a pet owner to cancel appointments'
			);
		}

		// Get the appointment with relations
		const appointment = await this.appointmentsRepository.findOne({
			where: { id: appointmentId },
			relations: [
				'patient',
				'patient.patientOwners',
				'patient.patientOwners.ownerBrand',
				'clinic',
				'appointmentDoctors',
				'appointmentDoctors.clinicUser',
				'appointmentDoctors.clinicUser.user'
			]
		});

		if (!appointment) {
			throw new NotFoundException(
				`Appointment with ID ${appointmentId} not found`
			);
		}

		// Verify that the pet belongs to the owner
		const ownerHasPet = appointment.patient.patientOwners.some(
			patientOwner => patientOwner.ownerId === ownerId
		);
		if (!ownerHasPet) {
			throw new ForbiddenException(
				'You can only cancel appointments for your own pets'
			);
		}

		// --- Settings Validation for Cancellation Deadline --- Start ---
		const settings = await this.getEffectiveBookingSettings(
			appointment.clinicId
		);

		// Check Modification/Cancellation Deadline (using minutes)
		if (settings.modificationDeadlineMinutes !== null) {
			if (!appointment.startTime) {
				throw new InternalServerErrorException(
					'Appointment start time is missing, cannot check deadline.'
				);
			}
			const originalAppointmentStartTime = moment(appointment.startTime);
			const deadline = originalAppointmentStartTime
				.clone()
				.subtract(settings.modificationDeadlineMinutes, 'minutes');

			if (moment().isAfter(deadline)) {
				throw new BadRequestException(
					`Cancellation is not allowed within ${settings.modificationDeadlineMinutes} minutes of the appointment time.`
				);
			}
		}
		// --- Settings Validation for Cancellation Deadline --- End ---

		// Verify that the appointment is in a status that allows cancellation
		// Typically, only 'Scheduled' appointments can be cancelled by the client.
		if (appointment.status !== EnumAppointmentStatus.Scheduled) {
			throw new BadRequestException(
				`Cannot cancel appointment with status ${appointment.status}`
			);
		}

		// Perform cancellation using the underlying AppointmentsService
		try {
			// Assuming deleteAppointment handles the cancellation logic (e.g., soft delete)
			await this.appointmentsService.deleteAppointment(appointmentId);

			// Log audit event for cancellation
			await this._logAuditEvent(
				appointmentId,
				ownerId,
				AppointmentAuditLogAction.CANCEL, // Use CANCEL action
				null,
				'Client booking cancelled'
			);

			// Return the (now cancelled/deleted) appointment details
			const cancelledAppointment = {
				...appointment,
				status: EnumAppointmentStatus.Cancelled, // Reflect cancellation
				deletedAt: new Date() // Reflect soft delete if applicable
			} as AppointmentEntity;

			return this.formatAppointmentResponse(cancelledAppointment);
		} catch (error) {
			this.logger.error(
				'Error cancelling appointment via client booking',
				{
					error,
					appointmentId
				}
			);
			if (
				error instanceof BadRequestException ||
				error instanceof NotFoundException ||
				error instanceof ForbiddenException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'Failed to cancel the appointment.'
			);
		}
	}
}
