import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClinicMedicationEntity } from './entities/clinic-medication.entity';
import { ClinicMedicationsService } from './clinic-medications.service';
import { ClinicMedicationsController } from './clinic-medications.controller';
import { RoleModule } from '../roles/role.module';

@Module({
	imports: [TypeOrmModule.forFeature([ClinicMedicationEntity]),RoleModule],
	controllers: [ClinicMedicationsController],
	providers: [ClinicMedicationsService],
	exports:[ClinicMedicationsService]
})
export class ClinicMedicationsModule {}
