import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ClinicProductsController } from './clinic-products.controller';
import { ClinicProductsService } from './clinic-products.service';
import { ClinicProductEntity } from './entities/clinic-product.entity';
import { RoleService } from '../roles/role.service';
import { CreateProductDto } from './dto/create-products.dto';
import { UpdateProductDto } from './dto/update-products.dto';

describe('ClinicProductsController', () => {
	let controller: ClinicProductsController;
	let productsService: jest.Mocked<ClinicProductsService>;
	let logger: jest.Mocked<WinstonLogger>;

	const mockClinicProductsService = {
		getProducts: jest.fn(),
		create: jest.fn(),
		update: jest.fn(),
		findOne: jest.fn(),
		remove: jest.fn(),
		bulkInsert: jest.fn()
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [ClinicProductsController],
			providers: [
				{
					provide: ClinicProductsService,
					useValue: mockClinicProductsService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: {
						log: jest.fn(),
						error: jest.fn()
					}
				}
			]
		}).compile();

		controller = module.get<ClinicProductsController>(
			ClinicProductsController
		);
		productsService = module.get(ClinicProductsService);
		logger = module.get(WinstonLogger);

		// Reset all mocks before each test
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('getAllProducts', () => {
		const productsDataList: ClinicProductEntity[] = [
			{
				id: 'product_uuid_1',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_1',
				productName: 'product_name_A',
				currentStock: 12,
				minimumQuantity: 2,
				chargeablePrice: 0,
				tax: 10
			},
			{
				id: 'product_uuid_2',
				clinicId: 'clinic_uuid_1',
				brandId: 'brand_uuid_1',
				uniqueId: 'unique_id_2',
				productName: 'product_name_B',
				currentStock: 12,
				minimumQuantity: 2,
				chargeablePrice: 0,
				tax: 5
			}
		];

		it('should have a getAllProducts function', () => {
			expect(controller.getAllProducts).toBeDefined();
		});

		it('should return a list of products without a search keyword', async () => {
			productsService.getProducts.mockResolvedValue(productsDataList);

			const result = await controller.getAllProducts('clinic_uuid_1');

			expect(result).toEqual(productsDataList);
			expect(productsService.getProducts).toHaveBeenCalledWith(
				'clinic_uuid_1',
				undefined
			);
			expect(logger.log).toHaveBeenCalledWith('Fetching all products');
		});

		it('should return filtered products with search keyword', async () => {
			const searchKeyword = 'product_name_A';
			const filteredData = [productsDataList[0]];

			productsService.getProducts.mockResolvedValue(filteredData);
			const result = await controller.getAllProducts(
				'clinic_uuid_1',
				searchKeyword
			);

			expect(result).toEqual(filteredData);
			expect(productsService.getProducts).toHaveBeenCalledWith(
				'clinic_uuid_1',
				searchKeyword
			);
		});

		it('should throw HttpException with BAD_REQUEST when service throws error', async () => {
			const error = new HttpException(
				'Error fetching all the products',
				HttpStatus.BAD_REQUEST
			);
			productsService.getProducts.mockRejectedValue(error);

			await expect(
				controller.getAllProducts('clinic_uuid_1')
			).rejects.toThrow(error);

			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching products',
				{
					error
				}
			);
		});
	});

	describe('create', () => {
		const createProductDto: CreateProductDto = {
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			productName: 'Test Product',
			chargeablePrice: 100,
			tax: 10,
			currentStock: 50,
			minimumQuantity: 5
		};

		it('should create a new product successfully', async () => {
			const createdProduct = {
				...createProductDto,
				id: 'new_uuid',
				uniqueId: 'P_000001'
			};
			productsService.create.mockResolvedValue(createdProduct);

			const result = await controller.create(createProductDto);

			expect(result).toEqual(createdProduct);
			expect(productsService.create).toHaveBeenCalledWith(
				createProductDto
			);
		});

		it('should throw HttpException with BAD_REQUEST when creation fails', async () => {
			const error = new Error('Creation failed');
			productsService.create.mockRejectedValue(error);

			await expect(controller.create(createProductDto)).rejects.toThrow(
				new HttpException(
					'Error creating product',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(logger.error).toHaveBeenCalledWith(
				'Error creating product',
				{
					error
				}
			);
		});

		it('should validate required fields in CreateProductDto', async () => {
			const invalidDto = {
				clinicId: 'clinic_uuid'
				// Missing required fields
			};

			await expect(
				controller.create(invalidDto as CreateProductDto)
			).rejects.toThrow();
		});
	});

	describe('update', () => {
		const updateProductDto: UpdateProductDto = {
			productName: 'Updated Product',
			chargeablePrice: 150
		};

		const existingProduct: ClinicProductEntity = {
			id: 'product_uuid',
			uniqueId: 'P_000001',
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			productName: 'Test Product',
			chargeablePrice: 100,
			tax: 10,
			currentStock: 50,
			minimumQuantity: 5
		};

		it('should update a product successfully', async () => {
			const updatedProduct = { ...existingProduct, ...updateProductDto };
			productsService.update.mockResolvedValue(updatedProduct);

			const result = await controller.update(
				'product_uuid',
				updateProductDto
			);

			expect(result).toEqual(updatedProduct);
			expect(productsService.update).toHaveBeenCalledWith(
				'product_uuid',
				updateProductDto
			);
		});

		it('should throw HttpException with BAD_REQUEST when update fails', async () => {
			const error = new Error('Update failed');
			productsService.update.mockRejectedValue(error);

			await expect(
				controller.update('product_uuid', updateProductDto)
			).rejects.toThrow(
				new HttpException(
					'Error updating product',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(logger.error).toHaveBeenCalledWith(
				'Error updating product',
				{
					error
				}
			);
		});
	});

	describe('findOne', () => {
		const existingProduct: ClinicProductEntity = {
			id: 'product_uuid',
			uniqueId: 'P_000001',
			clinicId: 'clinic_uuid',
			brandId: 'brand_uuid',
			productName: 'Test Product',
			chargeablePrice: 100,
			tax: 10,
			currentStock: 50,
			minimumQuantity: 5
		};

		it('should return a product by id successfully', async () => {
			productsService.findOne.mockResolvedValue(existingProduct);

			const result = await controller.findOne('product_uuid');

			expect(result).toEqual(existingProduct);
			expect(productsService.findOne).toHaveBeenCalledWith(
				'product_uuid'
			);
		});

		it('should throw HttpException with NOT_FOUND when product not found', async () => {
			const error = new Error('Product not found');
			productsService.findOne.mockRejectedValue(error);

			await expect(
				controller.findOne('nonexistent_uuid')
			).rejects.toThrow(
				new HttpException(
					'Error fetching product',
					HttpStatus.NOT_FOUND
				)
			);

			expect(logger.error).toHaveBeenCalledWith(
				'Error fetching product',
				{
					error
				}
			);
		});
	});

	describe('remove', () => {
		it('should delete a product and return success message', async () => {
			productsService.remove.mockResolvedValue(undefined);

			const result = await controller.remove('product_uuid');

			expect(result).toEqual({ message: 'Product deleted successfully' });
			expect(productsService.remove).toHaveBeenCalledWith('product_uuid');
		});

		it('should throw HttpException with BAD_REQUEST when deletion fails', async () => {
			const error = new Error('Deletion failed');
			productsService.remove.mockRejectedValue(error);

			await expect(controller.remove('product_uuid')).rejects.toThrow(
				new HttpException(
					'Error deleting product',
					HttpStatus.BAD_REQUEST
				)
			);

			expect(logger.error).toHaveBeenCalledWith(
				'Error deleting product',
				{
					error
				}
			);
		});
	});
});
