import { IsUUID, IsObject, IsOptional, IsString, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TemplateType } from '../entities/diagnostic-template.entity';

export class CreateDiagnosticNoteDto {
  @ApiProperty()
  @IsUUID()
  labReportId!: string;

  @ApiProperty()
  @IsUUID()
  clinicId!: string;

  @ApiProperty()
  @IsUUID()
  diagnosticId? :string

  @ApiPropertyOptional()
  @IsUUID()
  @IsOptional()
  templateId?: string;

  templateName!:string;

  @ApiProperty()
  @IsObject()
  noteData!: {
    values?: Record<string, any>;
    notes?: string;
  };
}

export class UpdateDiagnosticNoteDto {
  @IsOptional()
  @IsString()
  templateId?: string;

  @IsOptional()
  @IsString()
  templateName?: string;

  @IsOptional()
  @IsString()
  templateType?: 'notes' | 'table';

  @IsOptional()
  noteData?: {
      notes?: string;
      values?: Record<string, any>;
  };
}

export class CreateTemplateDto {
  @ApiProperty()
  @IsString()
  templateName!: string;

  @ApiProperty()
  @IsUUID()
  clinicId!: string;

  @ApiProperty({ type: [Object] })
  @ValidateNested({ each: true })
  @Type(() => Object)
  assignedDiagnostics!: Array<{
    id: string;
    name: string;
  }>;

  @ApiProperty({ enum: TemplateType })
  @IsEnum(TemplateType)
  templateType!: TemplateType;

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  tableStructure?: {
    columns: Array<{
      name: string;
      type: string;
    }>;
  };

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  notes?: string;
}