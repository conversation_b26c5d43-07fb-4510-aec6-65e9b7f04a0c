import { validate } from 'class-validator';
import { CreateClinicPlanDto } from './create-clinic-plan.dto';

describe('CreateClinicPlanDto', () => {
	let dto: CreateClinicPlanDto;

	beforeEach(() => {
		dto = new CreateClinicPlanDto();
	});

	it('should fail if clinicId is not provided', async () => {
		dto.name = 'Premium Plan';

		const errors = await validate(dto);

		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].property).toBe('clinicId');
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
		expect(errors[0].constraints?.isNotEmpty).toContain(
			'The clinic id should be provided.'
		);
	});

	it('should fail if name is not provided', async () => {
		dto.clinicId = '123e4567-e89b-12d3-a456-426614174000';

		const errors = await validate(dto);

		expect(errors.length).toBeGreaterThan(0);
		expect(errors[0].property).toBe('name');
		expect(errors[0].constraints).toHaveProperty('isNotEmpty');
		expect(errors[0].constraints?.isNotEmpty).toContain(
			'The lab report should have a name.'
		);
	});

	it('should pass if both clinicId and name are provided', async () => {
		dto.clinicId = '123e4567-e89b-12d3-a456-426614174000';
		dto.name = 'Premium Plan';

		const errors = await validate(dto);

		expect(errors.length).toBe(0);
	});
});
