import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
	IsOptional,
	IsBoolean,
	IsNumber,
	IsArray,
	IsUUID,
	ValidateNested,
	IsString,
	Matches,
	ArrayMinSize,
	ValidateIf,
	Min,
	IsInt
} from 'class-validator';

/**
 * Represents the schedule for a single day within the client booking working hours.
 */
export class ClientBookingDayScheduleDto {
	@ApiProperty({
		description:
			'Start time for the booking slot (HH:MM format). Null if not a working interval.',
		example: '09:00',
		required: false,
		nullable: true
	})
	@IsOptional()
	@ValidateIf(o => o.startTime !== null)
	@IsString()
	@Matches(/^([01]\d|2[0-3]):([0-5]\d)$/, {
		message: 'startTime must be in HH:MM format or null'
	})
	startTime!: string | null;

	@ApiProperty({
		description:
			'End time for the booking slot (HH:MM format). Null if not a working interval.',
		example: '17:00',
		required: false,
		nullable: true
	})
	@IsOptional()
	@ValidateIf(o => o.endTime !== null)
	@IsString()
	@Matches(/^([01]\d|2[0-3]):([0-5]\d)$/, {
		message: 'endTime must be in HH:MM format or null'
	})
	endTime!: string | null;

	@ApiProperty({
		description:
			'Indicates if this time block represents working/bookable hours.',
		example: true
	})
	@IsBoolean()
	isWorkingDay!: boolean;
}

/**
 * Represents the weekly working hours configuration specifically for client bookings.
 */
export class ClientBookingWorkingHoursDto {
	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	monday?: ClientBookingDayScheduleDto[];

	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	tuesday?: ClientBookingDayScheduleDto[];

	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	wednesday?: ClientBookingDayScheduleDto[];

	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	thursday?: ClientBookingDayScheduleDto[];

	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	friday?: ClientBookingDayScheduleDto[];

	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	saturday?: ClientBookingDayScheduleDto[];

	@ApiProperty({ type: [ClientBookingDayScheduleDto], required: false })
	@IsOptional()
	@IsArray()
	@ValidateNested({ each: true })
	@Type(() => ClientBookingDayScheduleDto)
	sunday?: ClientBookingDayScheduleDto[];
}

/**
 * DTO for time duration components (days, hours, minutes)
 */
export class TimeDurationDto {
	@ApiProperty({
		description: 'Number of days component',
		example: 1,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	days?: number;

	@ApiProperty({
		description: 'Number of hours component',
		example: 12,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	hours?: number;

	@ApiProperty({
		description: 'Number of minutes component',
		example: 30,
		required: false,
		nullable: true
	})
	@IsInt()
	@Min(0)
	@IsOptional()
	minutes?: number;
}

/**
 * DTO for updating the client booking settings of a clinic.
 * Allows partial updates.
 */
export class UpdateClientBookingSettingsDto {
	@ApiProperty({
		description:
			'Enable or disable the client booking feature entirely for this clinic.',
		example: true,
		required: false
	})
	@IsOptional()
	@IsBoolean()
	isEnabled?: boolean;

	@ApiProperty({
		description:
			'Explicitly allow booking with any doctor, overriding allowedDoctorIds if true.',
		example: false,
		required: false
	})
	@IsOptional()
	@IsBoolean()
	allowAllDoctors?: boolean;

	@ApiProperty({
		description:
			'Working hours during which clients are allowed to book appointments. If null, booking hours are not restricted (but subject to general availability).',
		type: ClientBookingWorkingHoursDto,
		required: false,
		nullable: true
	})
	@IsOptional()
	@ValidateNested()
	@Type(() => ClientBookingWorkingHoursDto)
	workingHours?: ClientBookingWorkingHoursDto | null;

	@ApiProperty({
		description:
			'List of specific doctor (ClinicUser) IDs allowed for client booking. If null or empty, all doctors are allowed (subject to their general availability).',
		example: ['uuid1', 'uuid2'],
		required: false,
		nullable: true,
		type: [String]
	})
	@IsOptional()
	@IsArray()
	@ValidateIf(o => o.allowedDoctorIds !== null) // Only validate elements if array is not null
	@IsUUID('4', {
		each: true,
		message: 'Each allowedDoctorId must be a valid UUID'
	})
	allowedDoctorIds?: string[] | null;

	@ApiProperty({
		description:
			'Minimum time before an appointment that a client can book, with days/hours/minutes components.',
		type: TimeDurationDto,
		required: false,
		nullable: true
	})
	@IsOptional()
	@ValidateNested()
	@Type(() => TimeDurationDto)
	minBookingLeadTime?: TimeDurationDto | null;

	@ApiProperty({
		description:
			'Deadline before an appointment when clients can no longer modify or cancel it, with days/hours/minutes components.',
		type: TimeDurationDto,
		required: false,
		nullable: true
	})
	@IsOptional()
	@ValidateNested()
	@Type(() => TimeDurationDto)
	modificationDeadlineTime?: TimeDurationDto | null;

	@ApiProperty({
		description:
			'Maximum time in advance that a client can book an appointment, with days/hours/minutes components.',
		type: TimeDurationDto,
		required: false,
		nullable: true
	})
	@IsOptional()
	@ValidateNested()
	@Type(() => TimeDurationDto)
	maxAdvanceBookingTime?: TimeDurationDto | null;

	// Legacy fields for backward compatibility
	@ApiProperty({
		description:
			'Minimum number of hours in advance a client must book an appointment (legacy field).',
		example: 4,
		required: false,
		nullable: true
	})
	@IsOptional()
	@IsNumber()
	@ValidateIf(o => o.minBookingLeadHours !== null)
	@Min(0, { message: 'minBookingLeadHours cannot be negative' })
	minBookingLeadHours?: number | null;

	@ApiProperty({
		description:
			'Number of hours before an appointment when clients can no longer modify or cancel it (legacy field).',
		example: 2,
		required: false,
		nullable: true
	})
	@IsOptional()
	@IsNumber()
	@ValidateIf(o => o.modificationDeadlineHours !== null)
	@Min(0, { message: 'modificationDeadlineHours cannot be negative' })
	modificationDeadlineHours?: number | null;
}
