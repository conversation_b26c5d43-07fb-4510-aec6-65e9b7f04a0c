const fs = require('fs');
const path = require('path');
const https = require('https');
const url = require('url');

// Read and parse the JSON file
const jsonData = JSON.parse(fs.readFileSync('emr_pdf.json', 'utf8'));

// Create the EMR_PDFs folder if it doesn't exist
const downloadFolder = 'EMR_PDFs';
if (!fs.existsSync(downloadFolder)) {
  fs.mkdirSync(downloadFolder);
}

// Function to download a file
function downloadFile(fileUrl, fileName) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(downloadFolder, fileName);
    const file = fs.createWriteStream(filePath);

    https.get(fileUrl, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${fileName}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file async
      console.error(`Error downloading ${fileName}: ${err.message}`);
      reject(err);
    });
  });
}

// Main function to process the JSON and download PDFs
async function downloadPDFs() {
  const pdfUrls = jsonData
    .map(item => item.Page_URL)
    .filter(url => url.includes('loadpdf.pdf'));

  for (const [index, pdfUrl] of pdfUrls.entries()) {
    const parsedUrl = url.parse(pdfUrl);
    const query = new URLSearchParams(parsedUrl.query);
    const fname = query.get('fname');
    const fileName = `pdf_${path.basename(fname.split('?')[0])}`;

    try {
      await downloadFile(pdfUrl, fileName);
    } catch (error) {
      console.error(`Failed to download ${fileName}`);
    }
  }

  console.log('All downloads completed.');
}

// Run the script
downloadPDFs();