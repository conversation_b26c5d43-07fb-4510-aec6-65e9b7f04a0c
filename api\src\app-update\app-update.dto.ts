import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class AppUpdateDto {
	@ApiProperty({
		description: 'The build type',
		example: 'iOS or Android'
	})
	@IsNotEmpty()
	buildType!: string;

	@ApiProperty({
		description: 'The build  major version. Eg: X.Y.Z',
		example: 'X'
	})
	@IsNotEmpty()
	@IsString()
	majorVersion!: string;

	@ApiProperty({
		description: 'The minor version. Eg: X.Y.Z',
		example: 'Y'
	})
	@IsNotEmpty()
	minorVersion!: string;

	@ApiProperty({
		description: 'The build patchversion. Eg: X.Y.Z',
		example: 'Z'
	})
	@IsNotEmpty()
	patchVersion!: string;
}
