import { ApiProperty } from '@nestjs/swagger';
import { IsJSON, IsOptional, IsUUID, IsString } from 'class-validator';

export class CreateAppointmentDetailsDto {
	@ApiProperty({
		description:
			'The appointment id for which appointment details are to be updated.',
		example: 'uuid'
	})
	@IsUUID()
	appointmentId!: string;

	@ApiProperty({
		description:
			'The details of the appointment details are to be updated.',
		example: 'json'
	})
	details!: object;

	@ApiProperty({
		description:
			'Identifier for the frontend call site initiating this update.',
		example: 'CALL_SITE_ID_001:SUB_ID_XYZ',
		required: false,
		default: 'DEFAULT_CALL_SITE'
	})
	@IsOptional()
	@IsString()
	callSiteId: string = 'DEFAULT_CALL_SITE';

	@IsOptional()
	createdAt?: Date;

	@IsOptional()
	updatedAt?: Date;

	@IsOptional()
	createdBy?: string;

	@IsOptional()
	updatedBy?: string;
}
