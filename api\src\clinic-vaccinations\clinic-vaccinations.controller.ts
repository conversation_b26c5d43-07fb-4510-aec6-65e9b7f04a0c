import {
	Body,
	Controller,
	Delete,
	Get,
	HttpException,
	HttpStatus,
	Param,
	Patch,
	Post,
	Query,
	UseGuards
} from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import {
	ApiOkResponse,
	ApiOperation,
	ApiQuery,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { ClinicVaccinationsService } from './clinic-vaccinations.service';
import { ClinicVaccinationEntity } from './entities/clinic-vaccination.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { CreateVaccinationDto } from './dto/create-vaccinations.dto';
import { UpdateVaccinationDto } from './dto/update-vaccinations.dto';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';

@ApiTags('Inventories')
@Controller('clinic-vaccinations')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ClinicVaccinationsController {
	constructor(
		private readonly logger: WinstonLogger,
		private readonly vaccinationService: ClinicVaccinationsService
	) {}

	@ApiOkResponse({
		description: 'Gets all vaccination items',
		isArray: true,
		type: ClinicVaccinationEntity
	})
	@ApiQuery({ name: 'search', required: false })
	@ApiQuery({ name: 'clinicId', required: false })
	@Get()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@TrackMethod('getAllVaccinations-clinic-vaccinations')
	async getAllVaccinations(
		@Query('clinicId') clinicId: string,
		@Query('search') searchKeyword?: string
	) {
		try {
			this.logger.log('Fetching all vaccinations');

			return await this.vaccinationService.getVaccinations(
				clinicId,
				searchKeyword
			);
		} catch (error) {
			this.logger.error('Error fetching all the vaccinations', error);

			throw new HttpException(
				'Error fetching all the vaccinations',
				HttpStatus.BAD_REQUEST
			);
		}
	}
	@Post()
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Create new vaccination' })
	@ApiResponse({
		status: 201,
		description: 'Vaccination created successfully'
	})
	@TrackMethod('create-clinic-vaccinations')
	async create(@Body() createVaccinationDto: CreateVaccinationDto) {
		try {
			return await this.vaccinationService.create(createVaccinationDto);
		} catch (error) {
			this.logger.error('Error creating vaccination', { error });
			throw new HttpException(
				'Error creating vaccination',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Patch(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Update vaccination' })
	@TrackMethod('update-clinic-vaccinations')
	async update(
		@Param('id') id: string,
		@Body() updateVaccinationDto: UpdateVaccinationDto
	) {
		try {
			return await this.vaccinationService.update(
				id,
				updateVaccinationDto
			);
		} catch (error) {
			this.logger.error('Error updating vaccination', { error });
			throw new HttpException(
				'Error updating vaccination',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Get vaccination by id' })
	@TrackMethod('findOne-clinic-vaccinations')
	async findOne(@Param('id') id: string) {
		try {
			return await this.vaccinationService.findOne(id);
		} catch (error) {
			this.logger.error('Error fetching vaccination', { error });
			throw new HttpException(
				'Error fetching vaccination',
				HttpStatus.NOT_FOUND
			);
		}
	}

	@Delete(':id')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.LAB_TECHNICIAN, Role.RECEPTIONIST)
	@ApiOperation({ summary: 'Delete vaccination' })
	@TrackMethod('remove-clinic-vaccinations')
	async remove(@Param('id') id: string) {
		try {
			await this.vaccinationService.remove(id);
			return { message: 'Vaccination deleted successfully' };
		} catch (error) {
			this.logger.error('Error deleting vaccination', { error });
			throw new HttpException(
				'Error deleting vaccination',
				HttpStatus.BAD_REQUEST
			);
		}
	}
}
