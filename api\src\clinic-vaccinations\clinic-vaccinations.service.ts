import {
	Injectable,
	InternalServerErrorException,
	NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { ClinicVaccinationEntity } from './entities/clinic-vaccination.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateVaccinationDto } from './dto/create-vaccinations.dto';
import { UpdateVaccinationDto } from './dto/update-vaccinations.dto';

@Injectable()
export class ClinicVaccinationsService {
	constructor(
		@InjectRepository(ClinicVaccinationEntity)
		private readonly vaccinationRepository: Repository<ClinicVaccinationEntity>,
		private readonly logger: WinstonLogger
	) {}

	private async generateUniqueId(
		prefix: string,
		clinicId: string
	): Promise<string> {
		const count = await this.vaccinationRepository.count({
			where: { clinicId }
		});
		const nextNumber = count + 1;
		return `${prefix}${nextNumber.toString().padStart(6, '0')}`;
	}

	async getVaccinations(clinicId: string, searchKeyword?: string) {
		if (searchKeyword) {
			if (clinicId) {
				return this.vaccinationRepository.find({
					where: {
						productName: ILike(`%${searchKeyword}%`),
						clinicId
					}
				});
			}

			return this.vaccinationRepository.find({
				where: { productName: ILike(`%${searchKeyword}%`) }
			});
		}

		return this.vaccinationRepository.find({
			where: { clinicId }
		});
	}

	async bulkInsert(
		clinicVaccinations: CreateVaccinationDto[]
	): Promise<string> {
		try {
			for (const vaccination of clinicVaccinations) {
				// Check if item exists for the clinic
				const existingVaccination =
					await this.vaccinationRepository.findOne({
						where: {
							clinicId: vaccination.clinicId,
							productName: vaccination.productName
						}
					});

				if (existingVaccination) {
					// Update existing item
					Object.assign(existingVaccination, vaccination);
					await this.vaccinationRepository.save(existingVaccination);
				} else {
					// Create new item with unique ID
					const uniqueId = await this.generateUniqueId(
						'V_',
						vaccination.clinicId
					);
					await this.vaccinationRepository.save({
						...vaccination,
						uniqueId
					});
				}
			}

			this.logger.log(
				`Bulk insert completed. Processed ${clinicVaccinations.length} vaccinations.`
			);
			return `Bulk insert of ${clinicVaccinations.length} vaccinations completed successfully`;
		} catch (error) {
			this.logger.error('Error during bulk insert of vaccinations', {
				error
			});
			throw new InternalServerErrorException(
				'Failed to insert vaccinations'
			);
		}
	}
	async create(
		createVaccinationDto: CreateVaccinationDto
	): Promise<ClinicVaccinationEntity> {
		try {
			const uniqueId = await this.generateUniqueId(
				'V_',
				createVaccinationDto.clinicId
			);
			const vaccination = this.vaccinationRepository.create({
				...createVaccinationDto,
				uniqueId
			});
			return await this.vaccinationRepository.save(vaccination);
		} catch (error) {
			this.logger.error('Error creating vaccination', { error });
			throw error;
		}
	}

	async update(
		id: string,
		updateVaccinationDto: UpdateVaccinationDto
	): Promise<ClinicVaccinationEntity> {
		const vaccination = await this.vaccinationRepository.findOneBy({ id });

		if (!vaccination) {
			const error = new NotFoundException(
				`Vaccination with ID ${id} not found`
			);
			this.logger.error('Error updating vaccination', { error });
			throw error;
		}

		try {
			Object.assign(vaccination, updateVaccinationDto);
			return await this.vaccinationRepository.save(vaccination);
		} catch (error) {
			this.logger.error('Error updating vaccination', { error });
			throw error;
		}
	}

	async findOne(id: string): Promise<ClinicVaccinationEntity> {
		const vaccination = await this.vaccinationRepository.findOneBy({ id });
		if (!vaccination) {
			throw new NotFoundException(`Vaccination with ID ${id} not found`);
		}
		return vaccination;
	}

	async remove(id: string): Promise<void> {
		const result = await this.vaccinationRepository.delete(id);
		if (result.affected === 0) {
			throw new NotFoundException(`Vaccination with ID ${id} not found`);
		}
	}

	async findOneEntry(criteria: { productName: string; clinicId: string }) {
		return this.vaccinationRepository.findOne({ where: criteria });
	}

	async deleteItem(itemId: string) {
		return this.vaccinationRepository.delete(itemId);
	}
}
