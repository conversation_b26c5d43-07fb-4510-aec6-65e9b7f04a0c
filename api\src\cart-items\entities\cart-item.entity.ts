import {
	<PERSON>um<PERSON>,
	CreateDate<PERSON><PERSON><PERSON>n,
	<PERSON>tity,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	OneToMany,
	OneToOne,
	PrimaryGeneratedColumn,
	UpdateDateColumn
} from 'typeorm';
import { ClinicProductEntity } from '../../clinic-products/entities/clinic-product.entity';
import { ClinicServiceEntity } from '../../clinic-services/entities/clinic-service.entity';
import { ClinicVaccinationEntity } from '../../clinic-vaccinations/entities/clinic-vaccination.entity';
import { ClinicMedicationEntity } from '../../clinic-medications/entities/clinic-medication.entity';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';

@Entity('cart_items')
export class CartItemEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'cart_id' })
	cartId!: string;

	@Column({ type: 'uuid', name: 'appointment_id', nullable: true })
	appointmentId?: string;

	@Column({ name: 'type' })
	type!: string;

	@Column({ type: 'uuid', name: 'product_id' })
	productId?: string;

	@Column({ type: 'uuid', name: 'service_id' })
	serviceId?: string;

	@Column({ type: 'uuid', name: 'vaccination_id' })
	vaccinationId?: string;

	@Column({ type: 'uuid', name: 'prescription_id' })
	prescriptionId?: string;

	@Column({ type: 'uuid', name: 'labreport_id' })
	labReportId?: string;

	@Column({ name: 'is_added_to_cart', default: true })
	isAddedToCart?: boolean;

	@Column({ name: 'quantity', default: 1 })
	quantity?: number;

	@Column('decimal', { 
        name: 'price',
        precision: 10,  
        scale: 2,      
        nullable: true
    })
	price?: number;

	@Column({ name: 'comment' })
	comment?: string;

	@Column({ name: 'added_from' })
	addedFrom?: string;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { nullable: true, name: 'created_by' })
	createdBy!: string;

	@Column('uuid', { nullable: true, name: 'updated_by' })
	updatedBy!: string;

	@OneToOne(() => ClinicProductEntity, product => product.cart, {
		eager: true
	})
	@JoinColumn({ name: 'product_id' })
	product?: ClinicProductEntity;

	@OneToOne(() => ClinicServiceEntity, service => service.cart, {
		eager: true
	})
	@JoinColumn({ name: 'service_id' })
	service?: ClinicServiceEntity;

	@OneToOne(() => ClinicVaccinationEntity, vaccination => vaccination.cart, {
		eager: true
	})
	@JoinColumn({ name: 'vaccination_id' })
	vaccination?: ClinicVaccinationEntity;

	@OneToOne(() => ClinicMedicationEntity, prescription => prescription.cart, {
		eager: true
	})
	@JoinColumn({ name: 'prescription_id' })
	prescription?: ClinicMedicationEntity;

	@OneToOne(() => ClinicLabReport, labReport => labReport.cart, {
		eager: true
	})
	@JoinColumn({ name: 'labreport_id' })
	labReport?: ClinicLabReport;
}
