import {
	<PERSON><PERSON><PERSON>,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn,
	OneToMany
} from 'typeorm';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
import { User } from '../../users/entities/user.entity';
import { AppointmentDoctorsEntity } from '../../appointments/entities/appointment-doctor.entity';
import { ChatRoomUser } from '../../chat-room/chat-room-users.entity';
import { ChatUserSessions } from '../../socket/chat-user-sessions.entity';
import { AvailabilityExceptionEntity } from '../../users/entities/availability-exception.entity';

@Entity('clinic_users')
export class ClinicUser {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column('uuid', { name: 'clinic_id' })
	clinicId!: string;

	@ManyToOne(() => ClinicEntity)
	@JoinColumn({ name: 'clinic_id' })
	clinic!: ClinicEntity;

	@Column('uuid', { name: 'user_id' })
	userId!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'user_id' })
	user!: User;

	@Column({ default: false, name: 'is_primary' })
	isPrimary!: boolean;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;

	@Column('uuid', { name: 'created_by', nullable: true })
	createdBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'created_by' })
	createdByUser!: User;

	@Column('uuid', { name: 'updated_by', nullable: true })
	updatedBy!: string;

	@ManyToOne(() => User)
	@JoinColumn({ name: 'updated_by' })
	updatedByUser!: User;

	@OneToMany(
		() => AppointmentDoctorsEntity,
		appointmentDoctor => appointmentDoctor.clinicUser
	)
	appointmentDoctors!: AppointmentDoctorsEntity[];

	@Column({ name: 'brand_id', type: 'uuid', nullable: true })
	brandId?: string;

	@Column('jsonb', {
		name: 'working_hours',
		nullable: true,
		default: {
			workingHours: {
				monday: [
					{ endTime: '17:00', startTime: '09:00', isWorkingDay: true }
				],
				tuesday: [
					{ endTime: '17:00', startTime: '09:00', isWorkingDay: true }
				],
				wednesday: [
					{ endTime: '17:00', startTime: '09:00', isWorkingDay: true }
				],
				thursday: [
					{ endTime: '17:00', startTime: '09:00', isWorkingDay: true }
				],
				friday: [
					{ endTime: '17:00', startTime: '09:00', isWorkingDay: true }
				],
				saturday: [
					{ endTime: null, startTime: null, isWorkingDay: false }
				],
				sunday: [
					{ endTime: null, startTime: null, isWorkingDay: false }
				]
			}
		}
	})
	workingHours!: any;

	@Column('boolean', { name: 'is_onboarded', default: false })
	isOnboarded!: boolean;

	@OneToMany(() => ChatRoomUser, chatRoomUser => chatRoomUser.clinicUser)
	chatRoomUser!: ChatRoomUser[];

	@OneToMany(() => ChatUserSessions, chatUserSession => chatUserSession.user)
	userSessions!: ChatUserSessions[];

	@OneToMany(
		() => AvailabilityExceptionEntity,
		exception => exception.clinicUser
	)
	availabilityExceptions?: AvailabilityExceptionEntity[];
}
