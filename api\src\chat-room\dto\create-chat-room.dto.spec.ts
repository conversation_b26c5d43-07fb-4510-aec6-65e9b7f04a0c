import { validate } from 'class-validator';
import { CreateChatRoomDto } from './create-chat-room.dto';

describe('create chat room dto', () => {
	it('should validate successfully with valid data', async () => {
		const dto = new CreateChatRoomDto();
		dto.userIds = ['9c1aaa0e-847c-4bca-9da0-72960eaa269d'];
		
		const errors = await validate(dto);
		expect(errors.length).toBe(0);
	});

	it('should fail if userId is not there', async () => {
		const dto = new CreateChatRoomDto();
		dto.userIds = [''];
		const errors = await validate(dto);
		expect(errors.length).toBe(1);
	});
});
