import { Test, TestingModule } from '@nestjs/testing';
import { AuthController, RequestWithUser } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { Role } from '../roles/role.enum';
import {
	EmailLoginDto,
	VerifyOtpDto,
	PinLoginDto
} from './dto/auth.dto';
import { ConflictException, HttpException, HttpStatus } from '@nestjs/common';

describe('AuthController', () => {
	let controller: AuthController;
	let authService: jest.Mocked<AuthService>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [AuthController],
			providers: [
				{
					provide: AuthService,
					useValue: {
						registerSuperAdmin: jest.fn(),
						registerAdmin: jest.fn(),
						registerStaff: jest.fn(),
						loginByEmail: jest.fn(),
						verifyOtp: jest.fn(),
						loginPin: jest.fn()
					}
				}
			]
		})
			.overrideGuard(JwtAuthGuard)
			.useValue({ canActivate: () => true })
			.overrideGuard(RolesGuard)
			.useValue({ canActivate: () => true })
			.compile();

		controller = module.get<AuthController>(AuthController);
		authService = module.get(AuthService);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('loginByEmail with OTP', () => {
		it('should call authService.loginByEmail', async () => {
			const dto: VerifyOtpDto = {
				email: '<EMAIL>',
				otp: '123456'
			};
			await controller.loginByEmail(dto);
			expect(authService.loginByEmail).toHaveBeenCalledWith(dto);
		});

		it('should handle ConflictException during OTP login', async () => {
			const dto: VerifyOtpDto = {
				email: '<EMAIL>',
				otp: '123456'
			};
			const error = new ConflictException('Invalid OTP');
			authService.loginByEmail.mockRejectedValue(error);

			await expect(controller.loginByEmail(dto)).rejects.toThrow(
				'Invalid OTP'
			);
			expect(authService.loginByEmail).toHaveBeenCalledWith(dto);
		});

		it('should throw an HttpException for server errors', async () => {
			const dto: VerifyOtpDto = {
				email: '<EMAIL>',
				otp: '123456'
			};
			const error = new HttpException(
				'Server Error',
				HttpStatus.INTERNAL_SERVER_ERROR
			);
			authService.loginByEmail.mockRejectedValue(error);

			await expect(controller.loginByEmail(dto)).rejects.toThrow(
				'Server Error'
			);
			expect(authService.loginByEmail).toHaveBeenCalledWith(dto);
		});
	});

	describe('verifyOtp', () => {
		it('should call authService.verifyOtp', async () => {
			const dto: VerifyOtpDto = {
				email: '<EMAIL>',
				otp: '123456'
			};
			await controller.verifyOtp(dto);
			expect(authService.verifyOtp).toHaveBeenCalledWith(dto);
		});
	});

	describe('loginPin', () => {
		it('should call authService.loginPin', async () => {
			const dto: PinLoginDto = {
				pin: '123456',
				brandId: ''
			};
			await controller.loginPin(dto);
			expect(authService.loginPin).toHaveBeenCalledWith(dto);
		});
	});
});
