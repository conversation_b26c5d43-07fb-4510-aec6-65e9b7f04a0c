import {
	IsBoolean,
	<PERSON>NotEmpty,
	<PERSON><PERSON><PERSON>ber,
	<PERSON><PERSON><PERSON><PERSON>,
	IsString,
	IsUUI<PERSON>
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateServiceDto {
	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	clinicId!: string;

	@ApiProperty()
	@IsUUID()
	@IsNotEmpty()
	brandId!: string;

	// @ApiProperty()
	// @IsString()
	// @IsNotEmpty()
	// uniqueId!: string;

	@ApiProperty()
	@IsString()
	@IsNotEmpty()
	serviceName!: string;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	chargeablePrice!: number;

	@ApiProperty()
	@IsNumber()
	@IsNotEmpty()
	tax!: number;
	@ApiProperty()
	@IsBoolean()
	@IsOptional()
	euthanasia?: boolean;

	@ApiProperty()
	@IsBoolean()
	@IsOptional()
	neuteredSpayed?: boolean;
}
